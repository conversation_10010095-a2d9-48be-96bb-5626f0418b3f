#ifndef __DEFINE_H
#define __DEFINE_H
#include "cms8s6990.h"


#define K1         P17
#define K2         P16
#define K3         P15

#define LEDRON     P23=1
#define LEDROFF    P23=0
#define LEDRTOGGLE P23^=1
#define LEDGON     P22=1
#define LEDGOFF    P22=0
#define LEDGTOGGLE P22^=1

#define CHG_FULL   P25
#define CHG_CHARGE P24


#define BAT_LOW_LEVEL 1902      //3.1
#define BAT_WARNING_LEVEL 2149  //3.5


extern bit MOTOR_RUNNING_FLAG;

#define Battery_ADC ADC_CH_14  


extern bit K1_cnt_EN;
extern bit K2_cnt_EN;
extern bit K3_cnt_EN;
extern volatile bit longhit;


extern volatile bit key1_handle;
extern volatile bit key3_handle;
extern volatile uint16_t key1_duration;
extern volatile uint16_t key3_duration;
extern volatile uint16_t timer_1ms_count;


extern bit batlow, batlow1;


#define Power	 P26




//Motor - 2相步进电机定义
// 物理引脚定义 (保持与硬件连接一致)
#define A_Phase_P  P14  // A相正极 (红线 PIN1)
#define A_Phase_N  P13  // A相负极 (黄线 PIN2)
#define B_Phase_P  P04  // B相正极 (白线 PIN3)
#define B_Phase_N  P05  // B相负极 (黑线 PIN4)

// 兼容性定义 (保持原有变量名)
#define	Motor_A	 P14   // A相正极
#define	Motor_B	 P13   // A相负极
#define	Motor_C  P04   // B相正极
#define	Motor_D	 P05   // B相负极

// 兼容性别名
#define A_Phase  A_Phase_P
#define B_Phase  A_Phase_N
#define C_Phase  B_Phase_P
#define D_Phase  B_Phase_N

#define High 0
#define Low  1

// 2-2相励磁步进序列 (根据电机参数表定义)
// STEP 1: A+ B+ (A相正向，B相正向)
#define Coil_Step1 {A_Phase_P = Low; A_Phase_N = High; B_Phase_P = Low; B_Phase_N = High;}
// STEP 2: A+ B- (A相正向，B相反向)
#define Coil_Step2 {A_Phase_P = Low; A_Phase_N = High; B_Phase_P = High; B_Phase_N = Low;}
// STEP 3: A- B- (A相反向，B相反向)
#define Coil_Step3 {A_Phase_P = High; A_Phase_N = Low; B_Phase_P = High; B_Phase_N = Low;}
// STEP 4: A- B+ (A相反向，B相正向)
#define Coil_Step4 {A_Phase_P = High; A_Phase_N = Low; B_Phase_P = Low; B_Phase_N = High;}

// 兼容性宏定义 (映射到新的步进序列)
#define Coil_A Coil_Step1
#define Coil_B Coil_Step2
#define Coil_C Coil_Step3
#define Coil_D Coil_Step4

// 关闭所有线圈
#define Coil_OFF {A_Phase_P = High; A_Phase_N = High; B_Phase_P = High; B_Phase_N = High;}



#define Time_1ms_Offset		63536	//(65536 - 2000)
#define Time_10ms_Offset	45536	//(65536 - 20000)


extern bit Bit_1_ms_Buff,Bit_N_ms_Buff,Bit_Toggle;

extern uint16_t Battery_ADC_Wait_Time ;
extern uint16_t Delay_Time_Count;
extern bit Delay_Open ;
extern uint16_t Delay_Time ;
extern bit Delay_Over ;

extern bit Power_count_clean;
extern bit Center_Line_Control;


//UART Used
extern bit Get_String_Buff ;
extern uint8_t Get_String_Wait_Time ;
//UART Used END
extern int Num,Count_Toggle;

//Motor
#define Direction_Clockwise		0x0F
#define Direction_Cclockwise	0xF0

#define Motor_Fast_Speed		1		// ����ٶ� (Լ3000��/��)
#define Motor_Middle_Speed		3		// �е��ٶ� (Լ300��/��)
#define Motor_Slow_Speed		17		// ���� (Լ60��/��)
#define Motor_Slowest_Speed		20		// ������ (Լ50��/��)

#define Motor_Auto_Rotate_Speed		3		// ��ת�ٶ�ʹ���е��ٶ�
#define Motor_Key_Control_Speed		17		// ��������ʹ������

extern uint8_t Motor_Direction_Data;
extern uint16_t Motor_Speed_Data ;
//Motor END


void Init_RAM_Variant(void) ;


#endif

