LX51 LINKER/LOCATER V4.66.97.0                                                          07/23/2025  13:16:50  PAGE 1


LX51 LINKER/LOCATER V4.66.97.0, INVOKED BY:
D:\KEILC51\C51\BIN\LX51.EXE .\Objects\startup_cms8s6990.obj, .\Objects\adc.obj, .\Objects\epwm.obj, .\Objects\gpio.obj, 
>> .\Objects\system.obj, .\Objects\timer.obj, .\Objects\uart.obj, .\Objects\wdt.obj, .\Objects\flash.obj, .\Objects\ADC_
>> Init.obj, .\Objects\define.obj, .\Objects\GPIO_Init.obj, .\Objects\Timer_Init.obj, .\Objects\UART_Init.obj, .\Objects
>> \isr.obj, .\Objects\UART_Function.obj, .\Objects\Battery_Function.obj, .\Objects\Key.obj, .\Objects\ADC_Used.obj, .\O
>> bjects\main.obj TO .\Objects\Project PRINT (.\Listings\Project.map) REMOVEUNUSED


CPU MODE:     8051 MODE
MEMORY MODEL: LARGE WITH FLOATING POINT ARITHMETIC


INPUT MODULES INCLUDED:
  .\Objects\startup_cms8s6990.obj (?C_STARTUP)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  .\Objects\adc.obj (ADC)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\epwm.obj (EPWM)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\gpio.obj (GPIO)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\system.obj (SYSTEM)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\timer.obj (TIMER)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\uart.obj (UART)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\wdt.obj (WDT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\flash.obj (FLASH)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\ADC_Init.obj (ADC_INIT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\define.obj (DEFINE)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\GPIO_Init.obj (GPIO_INIT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\Timer_Init.obj (TIMER_INIT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\UART_Init.obj (UART_INIT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\isr.obj (ISR)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\UART_Function.obj (UART_FUNCTION)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\Battery_Function.obj (BATTERY_FUNCTION)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\Key.obj (KEY)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\ADC_Used.obj (ADC_USED)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\main.obj (MAIN)
         COMMENT TYPE 0: C51 V9.60.0.0
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPMUL)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPDIV)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FCAST)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (PRINTF)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPGETOPN)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPROUND)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPCONVERT)
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 2


         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPADD)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FTNPWR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C_INIT)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?COPY)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CLDPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CLDOPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CSTPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CSTOPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?UIDIV)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?ILDIX)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?ULDIV)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?LNEG)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?LSTXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?LSTKXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?PLDIXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?PSTXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CCASE)
         COMMENT TYPE 1: A51 / ASM51 Assembler


ACTIVE MEMORY CLASSES OF MODULE:  .\Objects\Project (?C_STARTUP)

BASE        START       END         USED      MEMORY CLASS
==========================================================
C:000000H   C:000000H   C:00FFFFH   00224CH   CODE
I:000000H   I:000000H   I:0000FFH   000001H   IDATA
X:000000H   X:000000H   X:00FFFFH   0000A2H   XDATA
I:000020H.0 I:000020H.0 I:00002FH.7 000006H.2 BIT
C:000000H   C:000000H   C:00FFFFH   00000DH   CONST
I:000000H   I:000000H   I:00007FH   000008H   DATA


MEMORY MAP OF MODULE:  .\Objects\Project (?C_STARTUP)


START     STOP      LENGTH    ALIGN  RELOC    MEMORY CLASS   SEGMENT NAME
=========================================================================

* * * * * * * * * * *   D A T A   M E M O R Y   * * * * * * * * * * * * *
000000H   000007H   000008H   ---    AT..     DATA           "REG BANK 0"
000008H.0 00001FH.7 000018H.0 ---    ---      **GAP**
000020H.0 000024H.4 000004H.5 BIT    UNIT     BIT            ?BI?MAIN
000024H.5 000025H.1 000000H.5 BIT    UNIT     BIT            ?BI?KEY
000025H.2 000025H.5 000000H.4 BIT    UNIT     BIT            ?BI?DEFINE
000025H.6 000026H.1 000000H.4 BIT    UNIT     BIT            _BIT_GROUP_
000026H.2 000026H   000000H.6 ---    ---      **GAP**
000027H   000027H   000001H   BYTE   UNIT     IDATA          ?STACK

LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 3


* * * * * * * * * * *   C O D E   M E M O R Y   * * * * * * * * * * * * *
000000H   000002H   000003H   ---    OFFS..   CODE           ?CO??C_STARTUP?0
000003H   000005H   000003H   BYTE   OFFS..   CODE           ?ISR?00003
000006H   000009H   000004H   BYTE   UNIT     CODE           ?PR?ADC_START?ADC
00000AH   00000AH   000001H   BYTE   UNIT     CODE           ?PR?INT0_IRQHANDLER?ISR
00000BH   00000DH   000003H   BYTE   OFFS..   CODE           ?ISR?0000B
00000EH   000011H   000004H   BYTE   UNIT     CODE           ?PR?SYS_ENABLEWAKEUP?SYSTEM
000012H   000012H   000001H   BYTE   UNIT     CODE           ?PR?INT1_IRQHANDLER?ISR
000013H   000015H   000003H   BYTE   OFFS..   CODE           ?ISR?00013
000016H   000019H   000004H   BYTE   UNIT     CODE           ?PR?FLASH_UNLOCK?FLASH
00001AH   00001AH   000001H   BYTE   UNIT     CODE           ?PR?TIMER2_IRQHANDLER?ISR
00001BH   00001DH   000003H   BYTE   OFFS..   CODE           ?ISR?0001B
00001EH   000021H   000004H   BYTE   UNIT     CODE           ?PR?FLASH_LOCK?FLASH
000022H   000022H   000001H   BYTE   UNIT     CODE           ?PR?UART1_IRQHANDLER?ISR
000023H   000025H   000003H   BYTE   OFFS..   CODE           ?ISR?00023
000026H   000028H   000003H   BYTE   UNIT     CODE           ?PR?ADC_GETRESULT?ADC_USED
000029H   000029H   000001H   BYTE   UNIT     CODE           ?PR?P0EI_IRQHANDLER?ISR
00002AH   00002AH   000001H   BYTE   UNIT     CODE           ?PR?P3EI_IRQHANDLER?ISR
00002BH   00002DH   000003H   BYTE   OFFS..   CODE           ?ISR?0002B
00002EH   00002EH   000001H   BYTE   UNIT     CODE           ?PR?LVD_IRQHANDLER?ISR
00002FH   00002FH   000001H   BYTE   UNIT     CODE           ?PR?LSE_IRQHANDLER?ISR
000030H   000030H   000001H   BYTE   UNIT     CODE           ?PR?ACMP_IRQHANDLER?ISR
000031H   000031H   000001H   BYTE   UNIT     CODE           ?PR?TIMER3_IRQHANDLER?ISR
000032H   000032H   000001H   BYTE   UNIT     CODE           ?PR?TIMER4_IRQHANDLER?ISR
000033H   000035H   000003H   BYTE   OFFS..   CODE           ?ISR?00033
000036H   000036H   000001H   BYTE   UNIT     CODE           ?PR?EPWM_IRQHANDLER?ISR
000037H   000037H   000001H   BYTE   UNIT     CODE           ?PR?ADC_IRQHANDLER?ISR
000038H   000038H   000001H   BYTE   UNIT     CODE           ?PR?WDT_IRQHANDLER?ISR
000039H   000039H   000001H   BYTE   UNIT     CODE           ?PR?I2C_IRQHANDLER?ISR
00003AH   00003AH   000001H   BYTE   UNIT     CODE           ?PR?SPI_IRQHANDLER?ISR
00003BH   00003DH   000003H   BYTE   OFFS..   CODE           ?ISR?0003B
00003EH   00003EH   000001H   BYTE   UNIT     CODE           ?PR?ADC_CLEARCONVERTINTFLAG?ADC_USED
00003FH   000042H   000004H   ---    ---      **GAP**
000043H   000045H   000003H   BYTE   OFFS..   CODE           ?ISR?00043
000046H   00004AH   000005H   ---    ---      **GAP**
00004BH   00004DH   000003H   BYTE   OFFS..   CODE           ?ISR?0004B
00004EH   000052H   000005H   ---    ---      **GAP**
000053H   000055H   000003H   BYTE   OFFS..   CODE           ?ISR?00053
000056H   000061H   00000CH   BYTE   UNIT     CODE           ?PR?SYS_ENTERSTOP?SYSTEM
000062H   000062H   000001H   ---    ---      **GAP**
000063H   000065H   000003H   BYTE   OFFS..   CODE           ?ISR?00063
000066H   00006AH   000005H   ---    ---      **GAP**
00006BH   00006DH   000003H   BYTE   OFFS..   CODE           ?ISR?0006B
00006EH   000072H   000005H   ---    ---      **GAP**
000073H   000075H   000003H   BYTE   OFFS..   CODE           ?ISR?00073
000076H   00007AH   000005H   ---    ---      **GAP**
00007BH   00007DH   000003H   BYTE   OFFS..   CODE           ?ISR?0007B
00007EH   000082H   000005H   ---    ---      **GAP**
000083H   000085H   000003H   BYTE   OFFS..   CODE           ?ISR?00083
000086H   00008FH   00000AH   BYTE   UNIT     CODE           ?PR?ADC_GETCONVERTINTFLAG?ADC_USED
000090H   000092H   000003H   ---    ---      **GAP**
000093H   000095H   000003H   BYTE   OFFS..   CODE           ?ISR?00093
000096H   00009AH   000005H   ---    ---      **GAP**
00009BH   00009DH   000003H   BYTE   OFFS..   CODE           ?ISR?0009B
00009EH   0000A2H   000005H   ---    ---      **GAP**
0000A3H   0000A5H   000003H   BYTE   OFFS..   CODE           ?ISR?000A3
0000A6H   0000AAH   000005H   ---    ---      **GAP**
0000ABH   0000ADH   000003H   BYTE   OFFS..   CODE           ?ISR?000AB
0000AEH   0000B2H   000005H   ---    ---      **GAP**
0000B3H   0000B5H   000003H   BYTE   OFFS..   CODE           ?ISR?000B3
0000B6H   0009C7H   000912H   BYTE   UNIT     CODE           ?PR?MAIN?MAIN
0009C8H   0011D9H   000812H   BYTE   UNIT     CODE           ?C?LIB_CODE
0011DAH   001344H   00016BH   BYTE   UNIT     CODE           ?PR?KEY_INTERRUPT_PROCESS?MAIN
001345H   00146EH   00012AH   BYTE   UNIT     CODE           ?PR?KEY_SCAN?KEY
00146FH   001589H   00011BH   BYTE   UNIT     CODE           ?PR?TIMER0_IRQHANDLER?ISR
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 4


00158AH   0016A2H   000119H   BYTE   UNIT     CODE           ?PR?UART_DATA_PROCESS?UART_FUNCTION
0016A3H   0017B2H   000110H   BYTE   UNIT     CODE           ?PR?LED_CONTROL?MAIN
0017B3H   0018A3H   0000F1H   BYTE   UNIT     CODE           ?C_INITSEG
0018A4H   00196EH   0000CBH   BYTE   UNIT     CODE           ?PR?BATTERY_CHECK?MAIN
00196FH   001A08H   00009AH   BYTE   UNIT     CODE           ?C_C51STARTUP
001A09H   001A80H   000078H   BYTE   UNIT     CODE           ?PR?P1EI_IRQHANDLER?ISR
001A81H   001AF8H   000078H   BYTE   UNIT     CODE           ?PR?P2EI_IRQHANDLER?ISR
001AF9H   001B6BH   000073H   BYTE   UNIT     CODE           ?PR?_FUNCTION_UART_SEND_CMD?UART_FUNCTION
001B6CH   001BDBH   000070H   BYTE   UNIT     CODE           ?PR?GPIO_CONFIG?GPIO_INIT
001BDCH   001C40H   000065H   BYTE   UNIT     CODE           ?PR?_MOTOR_STEP_CONTROL?MAIN
001C41H   001C9FH   00005FH   BYTE   UNIT     CODE           ?PR?_UART_CONFIGRUNMODE?UART
001CA0H   001CFCH   00005DH   BYTE   UNIT     CODE           ?PR?_UART_SEND_STRING?UART_INIT
001CFDH   001D57H   00005BH   BYTE   UNIT     CODE           ?PR?UART0_IRQHANDLER?ISR
001D58H   001DA6H   00004FH   BYTE   UNIT     CODE           ?PR?UART_0_CONFIG?UART_INIT
001DA7H   001DF2H   00004CH   BYTE   UNIT     CODE           ?PR?_TMR_CONFIGRUNMODE?TIMER
001DF3H   001E3CH   00004AH   BYTE   UNIT     CODE           ?PR?_TMR_CONFIGTIMERCLK?TIMER
001E3DH   001E7BH   00003FH   BYTE   UNIT     CODE           ?PR?UART_1_CONFIG?UART_INIT
001E7CH   001EB8H   00003DH   BYTE   UNIT     CODE           ?PR?_KEY_FUNCTION_SWITCH_SYSTEM?MAIN
001EB9H   001EF2H   00003AH   BYTE   UNIT     CODE           ?PR?KEY_BUFF_RETURN?KEY
001EF3H   001F27H   000035H   BYTE   UNIT     CODE           ?PR?_FLASH_WRITE?FLASH
001F28H   001F5BH   000034H   BYTE   UNIT     CODE           ?PR?_FLASH_READ?FLASH
001F5CH   001F8CH   000031H   BYTE   UNIT     CODE           ?PR?_FLASH_ERASE?FLASH
001F8DH   001FB9H   00002DH   BYTE   UNIT     CODE           ?PR?_STORE_DLY?MAIN
001FBAH   001FE3H   00002AH   BYTE   UNIT     CODE           ?PR?TMR0_CONFIG?TIMER_INIT
001FE4H   00200CH   000029H   BYTE   UNIT     CODE           ?PR?TMR1_CONFIG?TIMER_INIT
00200DH   002033H   000027H   BYTE   UNIT     CODE           ?PR?_TMR_CONFIGTIMERPERIOD?TIMER
002034H   002059H   000026H   BYTE   UNIT     CODE           ?PR?RESTORE_DLY?MAIN
00205AH   00207EH   000025H   BYTE   UNIT     CODE           ?PR?ADC_GETADCRESULT?ADC
00207FH   0020A3H   000025H   BYTE   UNIT     CODE           ?PR?_UART_GETRECEIVEINTFLAG?UART
0020A4H   0020C7H   000024H   BYTE   UNIT     CODE           ?PR?TIMER1_IRQHANDLER?ISR
0020C8H   0020E7H   000020H   BYTE   UNIT     CODE           ?PR?_UART_DATA_COPY?UART_FUNCTION
0020E8H   002107H   000020H   BYTE   UNIT     CODE           ?PR?UART_DATA_INIT?UART_FUNCTION
002108H   002127H   000020H   BYTE   UNIT     CODE           ?PR?_DELAY1MS?MAIN
002128H   002146H   00001FH   BYTE   UNIT     CODE           ?PR?_TMR_START?TIMER
002147H   002165H   00001FH   BYTE   UNIT     CODE           ?PR?_TMR_STOP?TIMER
002166H   002184H   00001FH   BYTE   UNIT     CODE           ?PR?_UART_CLEARRECEIVEINTFLAG?UART
002185H   0021A2H   00001EH   BYTE   UNIT     CODE           ?PR?_ADC_ENABLECHANNEL?ADC
0021A3H   0021BFH   00001DH   BYTE   UNIT     CODE           ?PR?_TMR_ENABLEOVERFLOWINT?TIMER
0021C0H   0021D9H   00001AH   BYTE   UNIT     CODE           ?PR?ADC_CONFIG?ADC_INIT
0021DAH   0021EFH   000016H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGRUNMODE?ADC
0021F0H   002203H   000014H   BYTE   UNIT     CODE           ?PR?_UART_GETBUFF?UART
002204H   002216H   000013H   BYTE   UNIT     CODE           ?PR?_UART_ENABLEDOUBLEFREQUENCY?UART
002217H   002229H   000013H   BYTE   UNIT     CODE           ?PR?_UART_ENABLERECEIVE?UART
00222AH   00223CH   000013H   BYTE   UNIT     CODE           ?PR?GPIO_KEY_INTERRUPT_CONFIG?GPIO_INIT
00223DH   00224DH   000011H   BYTE   UNIT     CODE           ?PR?_UART_ENABLEINT?UART
00224EH   002256H   000009H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGADCVREF?ADC
002257H   00225FH   000009H   BYTE   UNIT     CODE           ?PR?_UART_CONFIGBRTCLK?UART
002260H   002268H   000009H   BYTE   UNIT     CODE           ?PR?_UART_CONFIGBRTPERIOD?UART
002269H   002271H   000009H   BYTE   UNIT     CODE           ?PR?_ADC_STARTCONVERT?ADC_USED
002272H   002279H   000008H   BYTE   UNIT     CODE           ?PR?UART_ENABLEBRT?UART
00227AH   00227FH   000006H   BYTE   UNIT     CODE           ?PR?RETURN_UART_DATA_LENGTH?UART_FUNCTION
002280H   002285H   000006H   BYTE   UNIT     CODE           ?PR?CLEAN_UART_DATA_LENGTH?UART_FUNCTION
002286H   002292H   00000DH   BYTE   UNIT     CONST          ?CO?UART_FUNCTION

* * * * * * * * * * *  X D A T A   M E M O R Y  * * * * * * * * * * * * *
000000H   000032H   000033H   BYTE   UNIT     XDATA          ?XD?MAIN
000033H   000056H   000024H   BYTE   UNIT     XDATA          _XDATA_GROUP_
000057H   000077H   000021H   BYTE   UNIT     XDATA          ?XD?UART_FUNCTION
000078H   00008CH   000015H   BYTE   UNIT     XDATA          ?XD?DEFINE
00008DH   000097H   00000BH   BYTE   UNIT     XDATA          ?XD?ADC_USED
000098H   00009DH   000006H   BYTE   UNIT     XDATA          ?XD?KEY
00009EH   0000A1H   000004H   BYTE   UNIT     XDATA          ?XD?ISR

* * * * * * * * *   R E M O V E D     S E G M E N T S   * * * * * * * *
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_STOP?ADC
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 5


   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_ENABLEHARDWARETRIG?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_DISABLEHARDWARETRIG?ADC
   *DEL*:           000015H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGHARDWARETRIG?ADC
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGAN31?ADC
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_ADC_SETTRIGDELAYTIME?ADC
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGADCBRAKE?ADC
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGCOMPAREVALUE?ADC
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?ADC_GETCMPRESULT?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_ENABLEINT?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_DISABLEINT?ADC
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?ADC_GETINTFLAG?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_CLEARINTFLAG?ADC
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?ADC_ENABLELDO?ADC
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?ADC_DISABLELDO?ADC
   *DEL*:           000006H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGRUNMODE?EPWM
   *DEL*:           00005CH   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELCLK?EPWM
   *DEL*:           000080H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELPERIOD?EPWM
   *DEL*:           000080H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELSYMDUTY?EPWM
   *DEL*:           0000C2H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELASYMDUTY?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEONESHOTMODE?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEAUTOLOADMODE?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_START?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_STOP?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEOUTPUT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEOUTPUT?EPWM
   *DEL*:           000033H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEFAULTBRAKE?EPWM
   *DEL*:           00002BH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEFAULTBRAKE?EPWM
   *DEL*:           000015H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELBRAKELEVEL?EPWM
   *DEL*:           000039H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEDEADZONE?EPWM
   *DEL*:           00002CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEDEADZONE?EPWM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEMASKCONTROL?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEMASKCONTROL?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEUPCMPINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEUPCMPINT?EPWM
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_EPWM_GETUPCMPINTFLAG?EPWM
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_EPWM_CLEARUPCMPINTFLAG?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEDOWNCMPINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEDOWNCMPINT?EPWM
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_EPWM_GETDOWNCMPINTFLAG?EPWM
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_EPWM_CLEARDOWNCMPINTFLAG?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEPERIODINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEPERIODINT?EPWM
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_EPWM_CLEARPERIODINTFLAG?EPWM
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_EPWM_GETPERIODINTFLAG?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEZEROINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEZEROINT?EPWM
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_EPWM_CLEARZEROINTFLAG?EPWM
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_EPWM_GETZEROINTFLAG?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?EPWM_ENABLEFAULTBRAKEINT?EPWM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?EPWM_DISABLEFAULTBRAKEINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?EPWM_GETFAULTBRAKEINTFLAG?EPWM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?EPWM_CLEARFAULTBRAKEINTFLAG?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEREVERSEOUTPUT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEREVERSEOUTPUT?EPWM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?EPWM_TRIGSOFTWAREBRAKE?EPWM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?EPWM_DISABLESOFTWAREBRAKE?EPWM
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGFBBRAKE?EPWM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?EPWM_ALLINTENABLE?EPWM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?EPWM_ALLINTDISABLE?EPWM
   *DEL*:           0000DEH   BYTE   UNIT     CODE           ?PR?_GPIO_CONFIGGPIOMODE?GPIO
   *DEL*:           00001EH   BYTE   UNIT     CODE           ?PR?_GPIO_ENABLEINT?GPIO
   *DEL*:           000022H   BYTE   UNIT     CODE           ?PR?_GPIO_DISABLEINT?GPIO
   *DEL*:           000058H   BYTE   UNIT     CODE           ?PR?_GPIO_GETINTFLAG?GPIO
   *DEL*:           00004CH   BYTE   UNIT     CODE           ?PR?_GPIO_CLEARINTFLAG?GPIO
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_ENABLELVD?SYSTEM
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 6


   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_DISABLELVD?SYSTEM
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_SYS_CONFIGLVD?SYSTEM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_ENABLELVDINT?SYSTEM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_DISABLELVDINT?SYSTEM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?SYS_GETLVDINTFLAG?SYSTEM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_CLEARLVDINTFLAG?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_ENABLEWDTRESET?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_DISABLEWDTRESET?SYSTEM
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?SYS_GETWDTRESETFLAG?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_CLEARWDTRESETFLAG?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_ENABLESOFTWARERESET?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_DISABLESOFTWARERESET?SYSTEM
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?SYS_GETPOWERONRESETFLAG?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_CLEARPOWERONRESETFLAG?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_DISABLEWAKEUP?SYSTEM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?SYS_ENTERIDLE?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_ENABLEWAKEUPTRIG?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_DISABLEWAKEUPTRIG?SYSTEM
   *DEL*:           00000EH   BYTE   UNIT     CODE           ?PR?_SYS_CONFIGWUTCLK?SYSTEM
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_SYS_CONFIGWUTTIME?SYSTEM
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_TMR_ENABLEGATE?TIMER
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_TMR_DISABLEGATE?TIMER
   *DEL*:           000044H   BYTE   UNIT     CODE           ?PR?_TMR_GETCOUNTVALUE?TIMER
   *DEL*:           00001DH   BYTE   UNIT     CODE           ?PR?_TMR_DISABLEOVERFLOWINT?TIMER
   *DEL*:           000033H   BYTE   UNIT     CODE           ?PR?_TMR_GETOVERFLOWINTFLAG?TIMER
   *DEL*:           00001DH   BYTE   UNIT     CODE           ?PR?_TMR_CLEAROVERFLOWINTFLAG?TIMER
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGRUNMODE?TIMER
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGTIMERCLK?TIMER
   *DEL*:           000021H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGTIMERPERIOD?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_ENABLEGATE?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_DISABLEGATE?TIMER
   *DEL*:           00003DH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECOMPARE?TIMER
   *DEL*:           000025H   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECOMPARE?TIMER
   *DEL*:           000029H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGCOMPAREVALUE?TIMER
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGCOMPAREINTMODE?TIMER
   *DEL*:           00007BH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECAPTURE?TIMER
   *DEL*:           000025H   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECAPTURE?TIMER
   *DEL*:           00003CH   BYTE   UNIT     CODE           ?PR?_TMR2_GETCAPTUREVALUE?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_ENABLEOVERFLOWINT?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_DISABLEOVERFLOWINT?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_GETOVERFLOWINTFLAG?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_CLEAROVERFLOWINTFLAG?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_ENABLET2EXINT?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_DISABLET2EXINT?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_GETT2EXINTFLAG?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_CLEART2EXINTFLAG?TIMER
   *DEL*:           00000EH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECOMPAREINT?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECOMPAREINT?TIMER
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?_TMR2_GETCOMPAREINTFLAG?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_CLEARCOMPAREINTFLAG?TIMER
   *DEL*:           00000EH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECAPTUREINT?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECAPTUREINT?TIMER
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?_TMR2_GETCAPTUREINTFLAG?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_CLEARCAPTUREINTFLAG?TIMER
   *DEL*:           000003H   BYTE   UNIT     CODE           ?PR?TMR2_ALLINTENABLE?TIMER
   *DEL*:           000003H   BYTE   UNIT     CODE           ?PR?TMR2_ALLINTDISABLE?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_START?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_STOP?TIMER
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_UART_DISABLEDOUBLEFREQUENCY?UART
   *DEL*:           000010H   BYTE   UNIT     CODE           ?PR?_UART_CONFIGBAUDRATE?UART
   *DEL*:           000005H   BYTE   UNIT     XDATA          ?XD?_UART_CONFIGBAUDRATE?UART
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_UART_DISABLERECEIVE?UART
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_UART_DISABLEINT?UART
   *DEL*:           000025H   BYTE   UNIT     CODE           ?PR?_UART_GETSENDINTFLAG?UART
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_UART_CLEARSENDINTFLAG?UART
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 7


   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_UART_SENDBUFF?UART
   *DEL*:           000022H   BYTE   UNIT     CODE           ?PR?_UART_SENDNINTHBIT?UART
   *DEL*:           000017H   BYTE   UNIT     CODE           ?PR?_UART_GETNINTHBIT?UART
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?UART_DISABLEBRT?UART
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_WDT_CONFIGOVERFLOWTIME?WDT
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?WDT_CLEARWDT?WDT
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?WDT_ENABLEOVERFLOWINT?WDT
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?WDT_DISABLEOVERFLOWINT?WDT
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?WDT_GETOVERFLOWINTFLAG?WDT
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?WDT_CLEAROVERFLOWINTFLAG?WDT
   *DEL*:           00001AH   BYTE   UNIT     CODE           ?PR?INIT_RAM_VARIANT?DEFINE
   *DEL*:           00000DH   BYTE   UNIT     CODE           ?PR?_PUTCHAR?UART_INIT
   *DEL*:           00000BH   BYTE   UNIT     CODE           ?PR?GETCHAR?UART_INIT
   *DEL*:           000016H   BYTE   UNIT     CODE           ?PR?_PUTS?UART_INIT
   *DEL*:           0000ADH   BYTE   UNIT     CODE           ?PR?_FUNCTION_STRCAT_PLUS_ASSIGN?UART_FUNCTION
   *DEL*:           00000EH   BYTE   UNIT     XDATA          ?XD?_FUNCTION_STRCAT_PLUS_ASSIGN?UART_FUNCTION
   *DEL*:           00013BH   BYTE   UNIT     CODE           ?PR?_ADC?ADC_USED
   *DEL*:           00000FH   BYTE   UNIT     XDATA          ?XD?_ADC?ADC_USED
   *DEL*:           00001CH   BYTE   UNIT     CONST          ?CO?ADC_USED
   *DEL*:           000483H   BYTE   UNIT     CODE           ?PR?PRINTF?PRINTF
   *DEL*:           000005H   BYTE   UNIT     DATA           ?DT?PRINTF?PRINTF
   *DEL*:           000001H.1 BIT    UNIT     BIT            ?BI?PRINTF?PRINTF
   *DEL*:           000030H   BYTE   UNIT     XDATA          ?XD?PRINTF?PRINTF



OVERLAY MAP OF MODULE:   .\Objects\Project (?C_STARTUP)


FUNCTION/MODULE                                BIT_GROUP   XDATA_GROUP
--> CALLED FUNCTION/MODULE                    START  STOP  START  STOP
======================================================================
?C_C51STARTUP                                 ----- -----  ----- -----
  +--> MAIN/MAIN
  +--> ?C_INITSEG

MAIN/MAIN                                     25H.6 26H.1  0033H 0043H
  +--> GPIO_CONFIG/GPIO_INIT
  +--> _DELAY1MS/MAIN
  +--> ADC_CONFIG/ADC_INIT
  +--> UART_1_CONFIG/UART_INIT
  +--> UART_0_CONFIG/UART_INIT
  +--> TMR0_CONFIG/TIMER_INIT
  +--> TMR1_CONFIG/TIMER_INIT
  +--> RESTORE_DLY/MAIN
  +--> _STORE_DLY/MAIN
  +--> GPIO_KEY_INTERRUPT_CONFIG/GPIO_INIT
  +--> KEY_SCAN/KEY
  +--> KEY_BUFF_RETURN/KEY
  +--> _FUNCTION_UART_SEND_CMD/UART_FUNCTION
  +--> UART_DATA_INIT/UART_FUNCTION
  +--> BATTERY_CHECK/MAIN
  +--> LED_CONTROL/MAIN
  +--> _TMR_STOP/TIMER
  +--> _TMR_CONFIGTIMERPERIOD/TIMER
  +--> _TMR_START/TIMER
  +--> _MOTOR_STEP_CONTROL/MAIN
  +--> KEY_INTERRUPT_PROCESS/MAIN
  +--> _KEY_FUNCTION_SWITCH_SYSTEM/MAIN
  +--> UART_DATA_PROCESS/UART_FUNCTION
  +--> SYS_ENABLEWAKEUP/SYSTEM
  +--> SYS_ENTERSTOP/SYSTEM

GPIO_CONFIG/GPIO_INIT                         ----- -----  ----- -----

LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 8


_DELAY1MS/MAIN                                ----- -----  ----- -----

ADC_CONFIG/ADC_INIT                           ----- -----  ----- -----
  +--> _ADC_CONFIGRUNMODE/ADC
  +--> _ADC_ENABLECHANNEL/ADC
  +--> _ADC_CONFIGADCVREF/ADC
  +--> ADC_START/ADC

_ADC_CONFIGRUNMODE/ADC                        ----- -----  ----- -----

_ADC_ENABLECHANNEL/ADC                        ----- -----  ----- -----

_ADC_CONFIGADCVREF/ADC                        ----- -----  ----- -----

ADC_START/ADC                                 ----- -----  ----- -----

UART_1_CONFIG/UART_INIT                       ----- -----  0044H 0049H
  +--> _UART_CONFIGRUNMODE/UART
  +--> _UART_ENABLERECEIVE/UART
  +--> _UART_CONFIGBRTCLK/UART
  +--> _UART_ENABLEDOUBLEFREQUENCY/UART
  +--> _UART_CONFIGBRTPERIOD/UART
  +--> UART_ENABLEBRT/UART

_UART_CONFIGRUNMODE/UART                      ----- -----  ----- -----

_UART_ENABLERECEIVE/UART                      ----- -----  ----- -----

_UART_CONFIGBRTCLK/UART                       ----- -----  ----- -----

_UART_ENABLEDOUBLEFREQUENCY/UART              ----- -----  ----- -----

_UART_CONFIGBRTPERIOD/UART                    ----- -----  ----- -----

UART_ENABLEBRT/UART                           ----- -----  ----- -----

UART_0_CONFIG/UART_INIT                       ----- -----  0044H 0049H
  +--> _UART_CONFIGRUNMODE/UART
  +--> _UART_ENABLERECEIVE/UART
  +--> _UART_CONFIGBRTCLK/UART
  +--> _UART_ENABLEDOUBLEFREQUENCY/UART
  +--> _UART_CONFIGBRTPERIOD/UART
  +--> UART_ENABLEBRT/UART
  +--> _UART_ENABLEINT/UART

_UART_ENABLEINT/UART                          ----- -----  ----- -----

TMR0_CONFIG/TIMER_INIT                        ----- -----  ----- -----
  +--> _TMR_CONFIGRUNMODE/TIMER
  +--> _TMR_CONFIGTIMERCLK/TIMER
  +--> _TMR_CONFIGTIMERPERIOD/TIMER
  +--> _TMR_ENABLEOVERFLOWINT/TIMER
  +--> _TMR_START/TIMER

_TMR_CONFIGRUNMODE/TIMER                      ----- -----  ----- -----

_TMR_CONFIGTIMERCLK/TIMER                     ----- -----  ----- -----

_TMR_CONFIGTIMERPERIOD/TIMER                  ----- -----  ----- -----

_TMR_ENABLEOVERFLOWINT/TIMER                  ----- -----  ----- -----

_TMR_START/TIMER                              ----- -----  ----- -----

TMR1_CONFIG/TIMER_INIT                        ----- -----  ----- -----
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 9


  +--> _TMR_CONFIGRUNMODE/TIMER
  +--> _TMR_CONFIGTIMERCLK/TIMER
  +--> _TMR_CONFIGTIMERPERIOD/TIMER
  +--> _TMR_ENABLEOVERFLOWINT/TIMER
  +--> _TMR_START/TIMER

RESTORE_DLY/MAIN                              ----- -----  0044H 0045H
  +--> FLASH_UNLOCK/FLASH
  +--> _FLASH_READ/FLASH
  +--> FLASH_LOCK/FLASH

FLASH_UNLOCK/FLASH                            ----- -----  ----- -----

_FLASH_READ/FLASH                             ----- -----  ----- -----

FLASH_LOCK/FLASH                              ----- -----  ----- -----

_STORE_DLY/MAIN                               ----- -----  0044H 0045H
  +--> FLASH_UNLOCK/FLASH
  +--> _FLASH_ERASE/FLASH
  +--> _FLASH_WRITE/FLASH
  +--> FLASH_LOCK/FLASH

_FLASH_ERASE/FLASH                            ----- -----  ----- -----

_FLASH_WRITE/FLASH                            ----- -----  ----- -----

GPIO_KEY_INTERRUPT_CONFIG/GPIO_INIT           ----- -----  ----- -----

KEY_SCAN/KEY                                  ----- -----  ----- -----

KEY_BUFF_RETURN/KEY                           ----- -----  ----- -----

_FUNCTION_UART_SEND_CMD/UART_FUNCTION         ----- -----  0044H 0051H
  +--> _UART_SEND_STRING/UART_INIT

_UART_SEND_STRING/UART_INIT                   ----- -----  0052H 0056H

UART_DATA_INIT/UART_FUNCTION                  ----- -----  ----- -----

BATTERY_CHECK/MAIN                            ----- -----  ----- -----
  +--> _ADC_STARTCONVERT/ADC_USED
  +--> ADC_GETCONVERTINTFLAG/ADC_USED
  +--> ADC_GETRESULT/ADC_USED
  +--> ADC_CLEARCONVERTINTFLAG/ADC_USED

_ADC_STARTCONVERT/ADC_USED                    ----- -----  ----- -----
  +--> _ADC_ENABLECHANNEL/ADC

ADC_GETCONVERTINTFLAG/ADC_USED                ----- -----  ----- -----

ADC_GETRESULT/ADC_USED                        ----- -----  ----- -----
  +--> ADC_GETADCRESULT/ADC

ADC_GETADCRESULT/ADC                          ----- -----  ----- -----

ADC_CLEARCONVERTINTFLAG/ADC_USED              ----- -----  ----- -----

LED_CONTROL/MAIN                              ----- -----  ----- -----

_TMR_STOP/TIMER                               ----- -----  ----- -----

_MOTOR_STEP_CONTROL/MAIN                      ----- -----  ----- -----

KEY_INTERRUPT_PROCESS/MAIN                    ----- -----  ----- -----
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 10


  +--> _KEY_FUNCTION_SWITCH_SYSTEM/MAIN

_KEY_FUNCTION_SWITCH_SYSTEM/MAIN              ----- -----  ----- -----

UART_DATA_PROCESS/UART_FUNCTION               ----- -----  ----- -----
  +--> RETURN_UART_DATA_LENGTH/UART_FUNCTION
  +--> CLEAN_UART_DATA_LENGTH/UART_FUNCTION

RETURN_UART_DATA_LENGTH/UART_FUNCTION         ----- -----  ----- -----

CLEAN_UART_DATA_LENGTH/UART_FUNCTION          ----- -----  ----- -----

SYS_ENABLEWAKEUP/SYSTEM                       ----- -----  ----- -----

SYS_ENTERSTOP/SYSTEM                          ----- -----  ----- -----

?C_INITSEG                                    ----- -----  ----- -----

*** NEW ROOT ********************************

INT0_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER0_IRQHANDLER/ISR                         ----- -----  ----- -----

*** NEW ROOT ********************************

INT1_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER1_IRQHANDLER/ISR                         ----- -----  ----- -----

*** NEW ROOT ********************************

UART0_IRQHANDLER/ISR                          ----- -----  ----- -----
  +--> _UART_GETRECEIVEINTFLAG/UART
  +--> _UART_GETBUFF/UART
  +--> _UART_DATA_COPY/UART_FUNCTION
  +--> _UART_CLEARRECEIVEINTFLAG/UART

_UART_GETRECEIVEINTFLAG/UART                  ----- -----  ----- -----

_UART_GETBUFF/UART                            ----- -----  ----- -----

_UART_DATA_COPY/UART_FUNCTION                 ----- -----  ----- -----

_UART_CLEARRECEIVEINTFLAG/UART                ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER2_IRQHANDLER/ISR                         ----- -----  ----- -----

*** NEW ROOT ********************************

UART1_IRQHANDLER/ISR                          ----- -----  ----- -----

*** NEW ROOT ********************************

P0EI_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

P1EI_IRQHANDLER/ISR                           ----- -----  ----- -----
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 11



*** NEW ROOT ********************************

P2EI_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

P3EI_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

LVD_IRQHANDLER/ISR                            ----- -----  ----- -----

*** NEW ROOT ********************************

LSE_IRQHANDLER/ISR                            ----- -----  ----- -----

*** NEW ROOT ********************************

ACMP_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER3_IRQHANDLER/ISR                         ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER4_IRQHANDLER/ISR                         ----- -----  ----- -----

*** NEW ROOT ********************************

EPWM_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

ADC_IRQHANDLER/ISR                            ----- -----  ----- -----

*** NEW ROOT ********************************

WDT_IRQHANDLER/ISR                            ----- -----  ----- -----

*** NEW ROOT ********************************

I2C_IRQHANDLER/ISR                            ----- -----  ----- -----

*** NEW ROOT ********************************

SPI_IRQHANDLER/ISR                            ----- -----  ----- -----



PUBLIC SYMBOLS OF MODULE:  .\Objects\Project (?C_STARTUP)


      VALUE       CLASS    TYPE      PUBLIC SYMBOL NAME
      =================================================
*DEL*:00000000H   XDATA    BYTE      ?_Function_Strcat_Plus_Assign?BYTE
*DEL*:00000000H   XDATA    ---       ?_PRINTF?BYTE
*DEL*:00000000H   XDATA    ---       ?_SPRINTF?BYTE
*DEL*:00000000H   XDATA    BYTE      ?_UART_ConfigBaudRate?BYTE
      02000052H   XDATA    BYTE      ?_UART_Send_String?BYTE
      010011B4H   CODE     ---       ?C?CCASE
      01000FAFH   CODE     ---       ?C?CLDOPTR
      01000F96H   CODE     ---       ?C?CLDPTR
      00000000H   NUMBER   ---       ?C?CODESEG
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 12


      01000F70H   CODE     ---       ?C?COPY
      01000FEEH   CODE     ---       ?C?CSTOPTR
      01000FDCH   CODE     ---       ?C?CSTPTR
      01000B78H   CODE     ---       ?C?FCASTC
      01000B73H   CODE     ---       ?C?FCASTI
      01000B6EH   CODE     ---       ?C?FCASTL
      01000D3FH   CODE     ---       ?C?FPADD
      01000C33H   CODE     ---       ?C?FPCONVERT
      01000AD1H   CODE     ---       ?C?FPDIV
      01000BACH   CODE     ---       ?C?FPGETOPN2
      010009C8H   CODE     ---       ?C?FPMUL
      01000BE1H   CODE     ---       ?C?FPNANRESULT
      01000BEBH   CODE     ---       ?C?FPOVERFLOW
      01000BC3H   CODE     ---       ?C?FPRESULT
      01000BD7H   CODE     ---       ?C?FPRESULT2
      01000BF6H   CODE     ---       ?C?FPROUND
      01000D3BH   CODE     ---       ?C?FPSUB
      01000BE8H   CODE     ---       ?C?FPUNDERFLOW
      01000E60H   CODE     ---       ?C?FTNPWR
      01001065H   CODE     ---       ?C?ILDIX
      01001149H   CODE     ---       ?C?LNEG
      01001163H   CODE     ---       ?C?LSTKXDATA
      01001157H   CODE     ---       ?C?LSTXDATA
      01001194H   CODE     ---       ?C?PLDIXDATA
      010011ABH   CODE     ---       ?C?PSTXDATA
      01001010H   CODE     ---       ?C?UIDIV
      010010B7H   CODE     ---       ?C?ULDIV
      00000000H   NUMBER   ---       ?C?XDATASEG
      010019C4H   CODE     ---       ?C_START
      01000000H   CODE     ---       ?C_STARTUP
*DEL*:00000000H   CODE     ---       _ADC
*DEL*:00000000H   CODE     ---       _ADC_ConfigADCBrake
      0100224EH   CODE     ---       _ADC_ConfigADCVref
*DEL*:00000000H   CODE     ---       _ADC_ConfigAN31
*DEL*:00000000H   CODE     ---       _ADC_ConfigCompareValue
*DEL*:00000000H   CODE     ---       _ADC_ConfigHardwareTrig
      010021DAH   CODE     ---       _ADC_ConfigRunMode
      01002185H   CODE     ---       _ADC_EnableChannel
*DEL*:00000000H   CODE     ---       _ADC_SetTrigDelayTime
      01002269H   CODE     ---       _ADC_StartConvert
      01002108H   CODE     ---       _Delay1ms
*DEL*:00000000H   CODE     ---       _EPWM_ClearDownCmpIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_ClearPeriodIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_ClearUpCmpIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_ClearZeroIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelAsymDuty
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelBrakeLevel
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelClk
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelPeriod
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelSymDuty
*DEL*:00000000H   CODE     ---       _EPWM_ConfigFBBrake
*DEL*:00000000H   CODE     ---       _EPWM_ConfigRunMode
*DEL*:00000000H   CODE     ---       _EPWM_DisableDeadZone
*DEL*:00000000H   CODE     ---       _EPWM_DisableDownCmpInt
*DEL*:00000000H   CODE     ---       _EPWM_DisableFaultBrake
*DEL*:00000000H   CODE     ---       _EPWM_DisableMaskControl
*DEL*:00000000H   CODE     ---       _EPWM_DisableOutput
*DEL*:00000000H   CODE     ---       _EPWM_DisablePeriodInt
*DEL*:00000000H   CODE     ---       _EPWM_DisableReverseOutput
*DEL*:00000000H   CODE     ---       _EPWM_DisableUpCmpInt
*DEL*:00000000H   CODE     ---       _EPWM_DisableZeroInt
*DEL*:00000000H   CODE     ---       _EPWM_EnableAutoLoadMode
*DEL*:00000000H   CODE     ---       _EPWM_EnableDeadZone
*DEL*:00000000H   CODE     ---       _EPWM_EnableDownCmpInt
*DEL*:00000000H   CODE     ---       _EPWM_EnableFaultBrake
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 13


*DEL*:00000000H   CODE     ---       _EPWM_EnableMaskControl
*DEL*:00000000H   CODE     ---       _EPWM_EnableOneShotMode
*DEL*:00000000H   CODE     ---       _EPWM_EnableOutput
*DEL*:00000000H   CODE     ---       _EPWM_EnablePeriodInt
*DEL*:00000000H   CODE     ---       _EPWM_EnableReverseOutput
*DEL*:00000000H   CODE     ---       _EPWM_EnableUpCmpInt
*DEL*:00000000H   CODE     ---       _EPWM_EnableZeroInt
*DEL*:00000000H   CODE     ---       _EPWM_GetDownCmpIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_GetPeriodIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_GetUpCmpIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_GetZeroIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_Start
*DEL*:00000000H   CODE     ---       _EPWM_Stop
      01001F5CH   CODE     ---       _FLASH_Erase
      01001F28H   CODE     ---       _FLASH_Read
      01001EF3H   CODE     ---       _FLASH_Write
*DEL*:00000000H   CODE     ---       _Function_Strcat_Plus_Assign
      01001AF9H   CODE     ---       _Function_UART_Send_CMD
*DEL*:00000000H   CODE     ---       _GPIO_ClearIntFlag
*DEL*:00000000H   CODE     ---       _GPIO_ConfigGPIOMode
*DEL*:00000000H   CODE     ---       _GPIO_DisableInt
*DEL*:00000000H   CODE     ---       _GPIO_EnableInt
*DEL*:00000000H   CODE     ---       _GPIO_GetIntFlag
      01001E7CH   CODE     ---       _Key_Function_Switch_System
      01001BDCH   CODE     ---       _Motor_Step_Control
*DEL*:0000006BH   CODE     ---       _PRINTF
*DEL*:00000000H   CODE     ---       _putchar
*DEL*:00000000H   CODE     ---       _puts
*DEL*:00000065H   CODE     ---       _SPRINTF
      01001F8DH   CODE     ---       _Store_dly
*DEL*:00000000H   CODE     ---       _SYS_ConfigLVD
*DEL*:00000000H   CODE     ---       _SYS_ConfigWUTCLK
*DEL*:00000000H   CODE     ---       _SYS_ConfigWUTTime
*DEL*:00000000H   CODE     ---       _TMR2_ClearCaptureIntFlag
*DEL*:00000000H   CODE     ---       _TMR2_ClearCompareIntFlag
*DEL*:00000000H   CODE     ---       _TMR2_ConfigCompareIntMode
*DEL*:00000000H   CODE     ---       _TMR2_ConfigCompareValue
*DEL*:00000000H   CODE     ---       _TMR2_ConfigRunMode
*DEL*:00000000H   CODE     ---       _TMR2_ConfigTimerClk
*DEL*:00000000H   CODE     ---       _TMR2_ConfigTimerPeriod
*DEL*:00000000H   CODE     ---       _TMR2_DisableCapture
*DEL*:00000000H   CODE     ---       _TMR2_DisableCaptureInt
*DEL*:00000000H   CODE     ---       _TMR2_DisableCompare
*DEL*:00000000H   CODE     ---       _TMR2_DisableCompareInt
*DEL*:00000000H   CODE     ---       _TMR2_EnableCapture
*DEL*:00000000H   CODE     ---       _TMR2_EnableCaptureInt
*DEL*:00000000H   CODE     ---       _TMR2_EnableCompare
*DEL*:00000000H   CODE     ---       _TMR2_EnableCompareInt
*DEL*:00000000H   CODE     ---       _TMR2_GetCaptureIntFlag
*DEL*:00000000H   CODE     ---       _TMR2_GetCaptureValue
*DEL*:00000000H   CODE     ---       _TMR2_GetCompareIntFlag
*DEL*:00000000H   CODE     ---       _TMR_ClearOverflowIntFlag
      01001DA7H   CODE     ---       _TMR_ConfigRunMode
      01001DF3H   CODE     ---       _TMR_ConfigTimerClk
      0100200DH   CODE     ---       _TMR_ConfigTimerPeriod
*DEL*:00000000H   CODE     ---       _TMR_DisableGATE
*DEL*:00000000H   CODE     ---       _TMR_DisableOverflowInt
*DEL*:00000000H   CODE     ---       _TMR_EnableGATE
      010021A3H   CODE     ---       _TMR_EnableOverflowInt
*DEL*:00000000H   CODE     ---       _TMR_GetCountValue
*DEL*:00000000H   CODE     ---       _TMR_GetOverflowIntFlag
      01002128H   CODE     ---       _TMR_Start
      01002147H   CODE     ---       _TMR_Stop
      01002166H   CODE     ---       _UART_ClearReceiveIntFlag
*DEL*:00000000H   CODE     ---       _UART_ClearSendIntFlag
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 14


*DEL*:00000000H   CODE     ---       _UART_ConfigBaudRate
      01002257H   CODE     ---       _UART_ConfigBRTClk
      01002260H   CODE     ---       _UART_ConfigBRTPeriod
      01001C41H   CODE     ---       _UART_ConfigRunMode
      010020C8H   CODE     ---       _UART_Data_Copy
*DEL*:00000000H   CODE     ---       _UART_DisableDoubleFrequency
*DEL*:00000000H   CODE     ---       _UART_DisableInt
*DEL*:00000000H   CODE     ---       _UART_DisableReceive
      01002204H   CODE     ---       _UART_EnableDoubleFrequency
      0100223DH   CODE     ---       _UART_EnableInt
      01002217H   CODE     ---       _UART_EnableReceive
      010021F0H   CODE     ---       _UART_GetBuff
*DEL*:00000000H   CODE     ---       _UART_GetNinthBit
      0100207FH   CODE     ---       _UART_GetReceiveIntFlag
*DEL*:00000000H   CODE     ---       _UART_GetSendIntFlag
      01001CA0H   CODE     ---       _UART_Send_String
*DEL*:00000000H   CODE     ---       _UART_SendBuff
*DEL*:00000000H   CODE     ---       _UART_SendNinthBit
*DEL*:00000000H   CODE     ---       _WDT_ConfigOverflowTime
*SFR* 000000D0H.6 DATA     BIT       AC
*SFR* 000000E0H   DATA     BYTE      ACC
      01000030H   CODE     ---       ACMP_IRQHandler
      0100003EH   CODE     ---       ADC_ClearConvertIntFlag
*DEL*:00000000H   CODE     ---       ADC_ClearIntFlag
      010021C0H   CODE     ---       ADC_Config
*DEL*:00000000H   CODE     ---       ADC_DisableHardwareTrig
*DEL*:00000000H   CODE     ---       ADC_DisableInt
*DEL*:00000000H   CODE     ---       ADC_DisableLDO
*DEL*:00000000H   CODE     ---       ADC_EnableHardwareTrig
*DEL*:00000000H   CODE     ---       ADC_EnableInt
*DEL*:00000000H   CODE     ---       ADC_EnableLDO
      0100205AH   CODE     ---       ADC_GetADCResult
*DEL*:00000000H   CODE     ---       ADC_GetCmpResult
      01000086H   CODE     ---       ADC_GetConvertIntFlag
*DEL*:00000000H   CODE     ---       ADC_GetIntFlag
      01000026H   CODE     ---       ADC_GetResult
      01000037H   CODE     ---       ADC_IRQHandler
      01000006H   CODE     ---       ADC_Start
*DEL*:00000000H   CODE     ---       ADC_Stop
*SFR* 000000D1H   DATA     BYTE      ADCMPC
*SFR* 000000D5H   DATA     BYTE      ADCMPH
*SFR* 000000D4H   DATA     BYTE      ADCMPL
*SFR* 000000DFH   DATA     BYTE      ADCON0
*SFR* 000000DEH   DATA     BYTE      ADCON1
*SFR* 000000E9H   DATA     BYTE      ADCON2
*SFR* 000000D3H   DATA     BYTE      ADDLYL
*SFR* 000000DDH   DATA     BYTE      ADRESH
*SFR* 000000DCH   DATA     BYTE      ADRESL
      00000020H.1 BIT      BIT       auto_rotate_entry_complete
      00000021H.2 BIT      BIT       auto_rotate_flash
      02000014H   XDATA    WORD      auto_rotate_flash_timer
      00000021H.7 BIT      BIT       auto_rotate_mode
      00000020H.6 BIT      BIT       auto_rotate_running
*SFR* 000000F0H   DATA     BYTE      B
      00000020H.2 BIT      BIT       batlow
      00000022H.0 BIT      BIT       batlow1
      0200002DH   XDATA    BYTE      batlow1_cnt
      0200001BH   XDATA    BYTE      batlow_cnt
      02000088H   XDATA    WORD      Battery_ADC_Wait_Time
      010018A4H   CODE     ---       Battery_Check
      0200001AH   XDATA    BYTE      battery_check_divider
      0200002EH   XDATA    WORD      BatV
      00000023H.0 BIT      BIT       Bit_1_ms_Buff
      00000024H.0 BIT      BIT       Bit_N_ms_Buff
      00000023H.4 BIT      BIT       Bit_Toggle
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 15


*SFR* 000000BFH   DATA     BYTE      BUZCON
*SFR* 000000BEH   DATA     BYTE      BUZDIV
*SFR* 000000C8H.5 DATA     BIT       CAPES
*SFR* 000000CEH   DATA     BYTE      CCEN
*SFR* 000000C3H   DATA     BYTE      CCH1
*SFR* 000000C5H   DATA     BYTE      CCH2
*SFR* 000000C7H   DATA     BYTE      CCH3
*SFR* 000000C2H   DATA     BYTE      CCL1
*SFR* 000000C4H   DATA     BYTE      CCL2
*SFR* 000000C6H   DATA     BYTE      CCL3
      00000025H.3 BIT      BIT       Center_Line_Control
      00000021H.6 BIT      BIT       Charg_State_Buff
      00000023H.2 BIT      BIT       charge_flash
      02000005H   XDATA    WORD      charge_flash_cnt
      00000022H.6 BIT      BIT       Charge_Was_Connected
*SFR* 0000008EH   DATA     BYTE      CKCON
      01002280H   CODE     ---       Clean_UART_Data_Length
*SFR* 0000008FH   DATA     BYTE      CLKDIV
      0200001CH   XDATA    INT       Count_1_Degree_Pulse
      02000083H   XDATA    INT       Count_Toggle
*SFR* 000000D0H.7 DATA     BIT       CY
      02000077H   XDATA    BYTE      Data_Length
      00000023H.3 BIT      BIT       Delay_Open
      00000025H.5 BIT      BIT       Delay_Over
      02000085H   XDATA    WORD      Delay_Time
      02000081H   XDATA    WORD      Delay_Time_Count
      00000024H.1 BIT      BIT       direction_changed
      02000022H   XDATA    WORD      dly
*SFR* 00000083H   DATA     BYTE      DPH0
*SFR* 00000085H   DATA     BYTE      DPH1
*SFR* 00000082H   DATA     BYTE      DPL0
*SFR* 00000084H   DATA     BYTE      DPL1
*SFR* 00000086H   DATA     BYTE      DPS
*SFR* 00000093H   DATA     BYTE      DPX0
*SFR* 00000095H   DATA     BYTE      DPX1
*SFR* 000000A8H.7 DATA     BIT       EA
*SFR* 000000AAH   DATA     BYTE      EIE2
*SFR* 000000B2H   DATA     BYTE      EIF2
*SFR* 000000B9H   DATA     BYTE      EIP1
*SFR* 000000BAH   DATA     BYTE      EIP2
*DEL*:00000000H   CODE     ---       EPWM_AllIntDisable
*DEL*:00000000H   CODE     ---       EPWM_AllIntEnable
*DEL*:00000000H   CODE     ---       EPWM_ClearFaultBrakeIntFlag
*DEL*:00000000H   CODE     ---       EPWM_DisableFaultBrakeInt
*DEL*:00000000H   CODE     ---       EPWM_DisableSoftwareBrake
*DEL*:00000000H   CODE     ---       EPWM_EnableFaultBrakeInt
*DEL*:00000000H   CODE     ---       EPWM_GetFaultBrakeIntFlag
      01000036H   CODE     ---       EPWM_IRQHandler
*DEL*:00000000H   CODE     ---       EPWM_TrigSoftwareBrake
*SFR* 000000A8H.4 DATA     BIT       ES0
*SFR* 000000A8H.6 DATA     BIT       ES1
*SFR* 000000A8H.1 DATA     BIT       ET0
*SFR* 000000A8H.3 DATA     BIT       ET1
*SFR* 000000A8H.5 DATA     BIT       ET2
*SFR* 000000A8H.0 DATA     BIT       EX0
*SFR* 000000A8H.2 DATA     BIT       EX1
*SFR* 000000D0H.5 DATA     BIT       F0
      0100001EH   CODE     ---       FLASH_Lock
      01000016H   CODE     ---       FLASH_UnLock
*SFR* 00000091H   DATA     BYTE      FUNCCR
      00000025H.4 BIT      BIT       Get_String_Buff
      02000087H   XDATA    BYTE      Get_String_Wait_Time
*DEL*:00000000H   CODE     ---       getchar
      01001B6CH   CODE     ---       GPIO_Config
      0100222AH   CODE     ---       GPIO_Key_Interrupt_Config
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 16


      01000039H   CODE     ---       I2C_IRQHandler
*SFR* 000000F6H   DATA     BYTE      I2CMBUF
*SFR* 000000F5H   DATA     BYTE      I2CMCR
*SFR* 000000F4H   DATA     BYTE      I2CMSA
*SFR* 000000F5H   DATA     BYTE      I2CMSR
*SFR* 000000F7H   DATA     BYTE      I2CMTP
*SFR* 000000F1H   DATA     BYTE      I2CSADR
*SFR* 000000F3H   DATA     BYTE      I2CSBUF
*SFR* 000000F2H   DATA     BYTE      I2CSCR
*SFR* 000000F2H   DATA     BYTE      I2CSSR
*SFR* 000000C8H.6 DATA     BIT       I3FR
*SFR* 000000A8H   DATA     BYTE      IE
*SFR* 00000088H.1 DATA     BIT       IE0
*SFR* 00000088H.3 DATA     BIT       IE1
*DEL*:00000000H   CODE     ---       Init_RAM_Variant
      0100000AH   CODE     ---       INT0_IRQHandler
      01000012H   CODE     ---       INT1_IRQHandler
*SFR* 000000B8H   DATA     BYTE      IP
*SFR* 00000088H.0 DATA     BIT       IT0
*SFR* 00000088H.2 DATA     BIT       IT1
      0200007AH   XDATA    BYTE      K1_cnt
      00000020H.7 BIT      BIT       K1_cnt_EN
      02000099H   XDATA    BYTE      K1_Count
      00000024H.5 BIT      BIT       K1_Press
      0200007BH   XDATA    BYTE      K2_cnt
      00000021H.0 BIT      BIT       K2_cnt_EN
      0200009AH   XDATA    BYTE      K2_Count
      00000023H.5 BIT      BIT       k2_long_press_detected
      0200001EH   XDATA    WORD      k2_long_press_timer
      00000024H.6 BIT      BIT       K2_Press
      00000022H.1 BIT      BIT       k2_released
      0200007CH   XDATA    BYTE      K3_cnt
      00000021H.1 BIT      BIT       K3_cnt_EN
      0200009BH   XDATA    BYTE      K3_Count
      00000024H.7 BIT      BIT       K3_Press
      00000022H.2 BIT      BIT       k3_released
      0200009CH   XDATA    BYTE      K4_Count
      00000025H.0 BIT      BIT       K4_Press
      0200009DH   XDATA    BYTE      K5_Count
      00000025H.1 BIT      BIT       K5_Press
      02000020H   XDATA    WORD      key1_duration
      00000022H.3 BIT      BIT       key1_handle
      00000023H.6 BIT      BIT       key1_long_started
      0200000CH   XDATA    WORD      key1_press_time
      00000022H.7 BIT      BIT       key1_pressed
      02000024H   XDATA    WORD      key3_duration
      00000022H.4 BIT      BIT       key3_handle
      00000023H.7 BIT      BIT       key3_long_started
      0200000EH   XDATA    WORD      key3_press_time
      00000023H.1 BIT      BIT       key3_pressed
      02000098H   XDATA    BYTE      Key_Buff
      01001EB9H   CODE     ---       Key_Buff_Return
      00000020H.4 BIT      BIT       key_control_active
      010011DAH   CODE     ---       Key_Interrupt_Process
      00000020H.3 BIT      BIT       Key_Long_Press
      01001345H   CODE     ---       Key_Scan
      0200000BH   XDATA    BYTE      key_scan_divider
      00000024H.2 BIT      BIT       key_short_press_mode
      0200002CH   XDATA    BYTE      last_direction
      010016A3H   CODE     ---       LED_Control
      00000021H.5 BIT      BIT       led_flash_state
      02000018H   XDATA    WORD      led_flash_timer
      00000022H.5 BIT      BIT       ledonoff
      00000020H.0 BIT      BIT       ledonoff1
      02000017H   XDATA    BYTE      ledonoff1_cnt
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 17


      02000032H   XDATA    BYTE      ledonoff_cnt
      00000024H.3 BIT      BIT       longhit
      02000078H   XDATA    WORD      longhit_cnt
      0100002FH   CODE     ---       LSE_IRQHandler
      0100002EH   CODE     ---       LVD_IRQHandler
*SFR* 000000FDH   DATA     BYTE      MADRH
*SFR* 000000FCH   DATA     BYTE      MADRL
      010000B6H   CODE     ---       main
      02000016H   XDATA    BYTE      main_loop_counter
*SFR* 000000FFH   DATA     BYTE      MCTRL
*SFR* 000000FEH   DATA     BYTE      MDATA
*SFR* 000000FBH   DATA     BYTE      MLOCK
      0200008CH   XDATA    BYTE      Motor_Direction_Data
      00000020H.5 BIT      BIT       MOTOR_RUNNING_FLAG
      0200007FH   XDATA    WORD      Motor_Speed_Data
      00000021H.4 BIT      BIT       need_led_flash
      0200007DH   XDATA    INT       Num
      0200009EH   XDATA    INT       Num_Forward_Pulse
      020000A0H   XDATA    INT       Num_Reverse_Pulse
      02000029H   XDATA    WORD      original_speed
*SFR* 000000D0H.2 DATA     BIT       OV
*SFR* 000000D0H.0 DATA     BIT       P
*SFR* 00000080H   DATA     BYTE      P0
*SFR* 00000080H.0 DATA     BIT       P00
*SFR* 00000080H.1 DATA     BIT       P01
*SFR* 00000080H.2 DATA     BIT       P02
*SFR* 00000080H.3 DATA     BIT       P03
*SFR* 00000080H.4 DATA     BIT       P04
*SFR* 00000080H.5 DATA     BIT       P05
*SFR* 00000080H.6 DATA     BIT       P06
*SFR* 00000080H.7 DATA     BIT       P07
      01000029H   CODE     ---       P0EI_IRQHandler
*SFR* 000000ACH   DATA     BYTE      P0EXTIE
*SFR* 000000B4H   DATA     BYTE      P0EXTIF
*SFR* 0000009AH   DATA     BYTE      P0TRIS
*SFR* 00000090H   DATA     BYTE      P1
*SFR* 00000090H.0 DATA     BIT       P10
*SFR* 00000090H.1 DATA     BIT       P11
*SFR* 00000090H.2 DATA     BIT       P12
*SFR* 00000090H.3 DATA     BIT       P13
*SFR* 00000090H.4 DATA     BIT       P14
*SFR* 00000090H.5 DATA     BIT       P15
*SFR* 00000090H.6 DATA     BIT       P16
*SFR* 00000090H.7 DATA     BIT       P17
      01001A09H   CODE     ---       P1EI_IRQHandler
*SFR* 000000ADH   DATA     BYTE      P1EXTIE
*SFR* 000000B5H   DATA     BYTE      P1EXTIF
*SFR* 000000A1H   DATA     BYTE      P1TRIS
*SFR* 000000A0H   DATA     BYTE      P2
*SFR* 000000A0H.0 DATA     BIT       P20
*SFR* 000000A0H.1 DATA     BIT       P21
*SFR* 000000A0H.2 DATA     BIT       P22
*SFR* 000000A0H.3 DATA     BIT       P23
*SFR* 000000A0H.4 DATA     BIT       P24
*SFR* 000000A0H.5 DATA     BIT       P25
*SFR* 000000A0H.6 DATA     BIT       P26
*SFR* 000000A0H.7 DATA     BIT       P27
      01001A81H   CODE     ---       P2EI_IRQHandler
*SFR* 000000AEH   DATA     BYTE      P2EXTIE
*SFR* 000000B6H   DATA     BYTE      P2EXTIF
*SFR* 000000A2H   DATA     BYTE      P2TRIS
*SFR* 000000B0H   DATA     BYTE      P3
*SFR* 000000B0H.0 DATA     BIT       P30
*SFR* 000000B0H.1 DATA     BIT       P31
*SFR* 000000B0H.2 DATA     BIT       P32
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 18


*SFR* 000000B0H.3 DATA     BIT       P33
*SFR* 000000B0H.4 DATA     BIT       P34
*SFR* 000000B0H.5 DATA     BIT       P35
*SFR* 000000B0H.6 DATA     BIT       P36
*SFR* 000000B0H.7 DATA     BIT       P37
      0100002AH   CODE     ---       P3EI_IRQHandler
*SFR* 000000AFH   DATA     BYTE      P3EXTIE
*SFR* 000000B7H   DATA     BYTE      P3EXTIF
*SFR* 000000A3H   DATA     BYTE      P3TRIS
*SFR* 00000087H   DATA     BYTE      PCON
      00000025H.2 BIT      BIT       Power_count_clean
      0200008AH   XDATA    WORD      Power_Off_Wait_Time
      02000030H   XDATA    WORD      precise_k2_timer
*SFR* 000000B8H.4 DATA     BIT       PS0
*SFR* 000000B8H.6 DATA     BIT       PS1
*SFR* 000000D0H   DATA     BYTE      PSW
*SFR* 000000B8H.1 DATA     BIT       PT0
*SFR* 000000B8H.3 DATA     BIT       PT1
*SFR* 000000B8H.5 DATA     BIT       PT2
*SFR* 000000B8H.0 DATA     BIT       PX0
*SFR* 000000B8H.2 DATA     BIT       PX1
      01002034H   CODE     ---       Restore_dly
      0100227AH   CODE     ---       Return_UART_Data_Length
*SFR* 00000098H.0 DATA     BIT       RI0
*SFR* 000000CBH   DATA     BYTE      RLDH
*SFR* 000000CAH   DATA     BYTE      RLDL
*SFR* 000000D0H.3 DATA     BIT       RS0
*SFR* 000000D0H.4 DATA     BIT       RS1
*SFR* 00000099H   DATA     BYTE      SBUF
*SFR* 00000099H   DATA     BYTE      SBUF0
*SFR* 000000EBH   DATA     BYTE      SBUF1
*SFR* 00000098H   DATA     BYTE      SCON0
*SFR* 000000EAH   DATA     BYTE      SCON1
      02000026H   XDATA    INT       Self_Check
*SFR* 00000081H   DATA     BYTE      SP
*SFR* 000000ECH   DATA     BYTE      SPCR
*SFR* 000000EEH   DATA     BYTE      SPDR
      00000024H.4 BIT      BIT       speedup
      02000010H   XDATA    WORD      speedup_cnt
      0100003AH   CODE     ---       SPI_IRQHandler
*SFR* 000000EDH   DATA     BYTE      SPSR
*SFR* 000000EFH   DATA     BYTE      SSCR
*DEL*:00000000H   CODE     ---       SYS_ClearLVDIntFlag
*DEL*:00000000H   CODE     ---       SYS_ClearPowerOnResetFlag
*DEL*:00000000H   CODE     ---       SYS_ClearWDTResetFlag
*DEL*:00000000H   CODE     ---       SYS_DisableLVD
*DEL*:00000000H   CODE     ---       SYS_DisableLVDInt
*DEL*:00000000H   CODE     ---       SYS_DisableSoftwareReset
*DEL*:00000000H   CODE     ---       SYS_DisableWakeUp
*DEL*:00000000H   CODE     ---       SYS_DisableWakeUpTrig
*DEL*:00000000H   CODE     ---       SYS_DisableWDTReset
*DEL*:00000000H   CODE     ---       SYS_EnableLVD
*DEL*:00000000H   CODE     ---       SYS_EnableLVDInt
*DEL*:00000000H   CODE     ---       SYS_EnableSoftwareReset
      0100000EH   CODE     ---       SYS_EnableWakeUp
*DEL*:00000000H   CODE     ---       SYS_EnableWakeUpTrig
*DEL*:00000000H   CODE     ---       SYS_EnableWDTReset
*DEL*:00000000H   CODE     ---       SYS_EnterIdle
      01000056H   CODE     ---       SYS_EnterStop
*DEL*:00000000H   CODE     ---       SYS_GetLVDIntFlag
*DEL*:00000000H   CODE     ---       SYS_GetPowerOnResetFlag
*DEL*:00000000H   CODE     ---       SYS_GetWDTResetFlag
      0200002BH   XDATA    BYTE      System_Mode_Before_Charge
      02000028H   XDATA    BYTE      System_Mode_Data
      02000007H   XDATA    DWORD     Systemclock
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 19


*SFR* 000000C8H.2 DATA     BIT       T2CM
*SFR* 000000C8H   DATA     BYTE      T2CON
*SFR* 000000C8H.0 DATA     BIT       T2I0
*SFR* 000000C8H.1 DATA     BIT       T2I1
*SFR* 000000CFH   DATA     BYTE      T2IE
*SFR* 000000C9H   DATA     BYTE      T2IF
*SFR* 000000C8H.7 DATA     BIT       T2PS
*SFR* 000000C8H.3 DATA     BIT       T2R0
*SFR* 000000C8H.4 DATA     BIT       T2R1
*SFR* 000000D2H   DATA     BYTE      T34MOD
*SFR* 00000096H   DATA     BYTE      TA
*SFR* 00000088H   DATA     BYTE      TCON
*SFR* 00000088H.5 DATA     BIT       TF0
*SFR* 00000088H.7 DATA     BIT       TF1
*SFR* 0000008CH   DATA     BYTE      TH0
*SFR* 0000008DH   DATA     BYTE      TH1
*SFR* 000000CDH   DATA     BYTE      TH2
*SFR* 000000DBH   DATA     BYTE      TH3
*SFR* 000000E3H   DATA     BYTE      TH4
*SFR* 00000098H.1 DATA     BIT       TI0
      0100146FH   CODE     ---       Timer0_IRQHandler
      010020A4H   CODE     ---       Timer1_IRQHandler
      0100001AH   CODE     ---       Timer2_IRQHandler
      01000031H   CODE     ---       Timer3_IRQHandler
      01000032H   CODE     ---       Timer4_IRQHandler
      02000012H   XDATA    WORD      timer_1ms_count
*SFR* 0000008AH   DATA     BYTE      TL0
*SFR* 0000008BH   DATA     BYTE      TL1
*SFR* 000000CCH   DATA     BYTE      TL2
*SFR* 000000DAH   DATA     BYTE      TL3
*SFR* 000000E2H   DATA     BYTE      TL4
*SFR* 00000089H   DATA     BYTE      TMOD
      01001FBAH   CODE     ---       TMR0_Config
      01001FE4H   CODE     ---       TMR1_Config
*DEL*:00000000H   CODE     ---       TMR2_AllIntDisable
*DEL*:00000000H   CODE     ---       TMR2_AllIntEnable
*DEL*:00000000H   CODE     ---       TMR2_ClearOverflowIntFlag
*DEL*:00000000H   CODE     ---       TMR2_ClearT2EXIntFlag
*DEL*:00000000H   CODE     ---       TMR2_DisableGATE
*DEL*:00000000H   CODE     ---       TMR2_DisableOverflowInt
*DEL*:00000000H   CODE     ---       TMR2_DisableT2EXInt
*DEL*:00000000H   CODE     ---       TMR2_EnableGATE
*DEL*:00000000H   CODE     ---       TMR2_EnableOverflowInt
*DEL*:00000000H   CODE     ---       TMR2_EnableT2EXInt
*DEL*:00000000H   CODE     ---       TMR2_GetOverflowIntFlag
*DEL*:00000000H   CODE     ---       TMR2_GetT2EXIntFlag
*DEL*:00000000H   CODE     ---       TMR2_Start
*DEL*:00000000H   CODE     ---       TMR2_Stop
*SFR* 00000088H.4 DATA     BIT       TR0
*SFR* 00000088H.6 DATA     BIT       TR1
*SFR* 00000098H.2 DATA     BIT       U0RB8
*SFR* 00000098H.4 DATA     BIT       U0REN
*SFR* 00000098H.7 DATA     BIT       U0SM0
*SFR* 00000098H.6 DATA     BIT       U0SM1
*SFR* 00000098H.5 DATA     BIT       U0SM2
*SFR* 00000098H.3 DATA     BIT       U0TB8
      01001CFDH   CODE     ---       UART0_IRQHandler
      01000022H   CODE     ---       UART1_IRQHandler
      01001D58H   CODE     ---       UART_0_Config
      01001E3DH   CODE     ---       UART_1_Config
      010020E8H   CODE     ---       UART_Data_Init
      0100158AH   CODE     ---       UART_Data_Process
*DEL*:00000000H   CODE     ---       UART_DisableBRT
      01002272H   CODE     ---       UART_EnableBRT
      02000057H   XDATA    ---       UART_Get_String
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 20


      00000021H.3 BIT      BIT       use_precise_timer
*SFR* 00000097H   DATA     BYTE      WDCON
*DEL*:00000000H   CODE     ---       WDT_ClearOverflowIntFlag
*DEL*:00000000H   CODE     ---       WDT_ClearWDT
*DEL*:00000000H   CODE     ---       WDT_DisableOverflowInt
*DEL*:00000000H   CODE     ---       WDT_EnableOverflowInt
*DEL*:00000000H   CODE     ---       WDT_GetOverflowIntFlag
      01000038H   CODE     ---       WDT_IRQHandler
*SFR* 000000BDH   DATA     BYTE      WUTCRH
*SFR* 000000BCH   DATA     BYTE      WUTCRL



SYMBOL TABLE OF MODULE:  .\Objects\Project (?C_STARTUP)

      VALUE       REP       CLASS    TYPE      SYMBOL NAME
      ====================================================
      ---         MODULE    ---      ---       ?C_STARTUP
      01000000H   PUBLIC    CODE     ---       ?C_STARTUP
      000000E0H   SYMBOL    DATA     ---       ACC
      000000F0H   SYMBOL    DATA     ---       B
      00000083H   SYMBOL    DATA     ---       DPH
      00000082H   SYMBOL    DATA     ---       DPL
      00000000H   SYMBOL    NUMBER   ---       IBPSTACK
      00000100H   SYMBOL    NUMBER   ---       IBPSTACKTOP
      00000100H   SYMBOL    NUMBER   ---       IDATALEN
      01001972H   SYMBOL    CODE     ---       IDATALOOP
      00000000H   SYMBOL    NUMBER   ---       PBPSTACK
      00000100H   SYMBOL    NUMBER   ---       PBPSTACKTOP
      00000000H   SYMBOL    NUMBER   ---       PDATALEN
      00000000H   SYMBOL    NUMBER   ---       PDATASTART
      00000000H   SYMBOL    NUMBER   ---       PPAGE
      00000000H   SYMBOL    NUMBER   ---       PPAGEENABLE
      000000A0H   SYMBOL    DATA     ---       PPAGE_SFR
      00000081H   SYMBOL    DATA     ---       SP
      0100196FH   SYMBOL    CODE     ---       STARTUP1
      00000000H   SYMBOL    NUMBER   ---       XBPSTACK
      00000000H   SYMBOL    NUMBER   ---       XBPSTACKTOP
      00000400H   SYMBOL    NUMBER   ---       XDATALEN
      0100197DH   SYMBOL    CODE     ---       XDATALOOP
      00000000H   SYMBOL    NUMBER   ---       XDATASTART
      01000000H   LINE      CODE     ---       #126
      0100196FH   LINE      CODE     ---       #133
      01001971H   LINE      CODE     ---       #134
      01001972H   LINE      CODE     ---       #135
      01001973H   LINE      CODE     ---       #136
      01001975H   LINE      CODE     ---       #140
      01001978H   LINE      CODE     ---       #141
      0100197AH   LINE      CODE     ---       #145
      0100197CH   LINE      CODE     ---       #147
      0100197DH   LINE      CODE     ---       #148
      0100197EH   LINE      CODE     ---       #149
      0100197FH   LINE      CODE     ---       #150
      01001981H   LINE      CODE     ---       #151
      01001983H   LINE      CODE     ---       #185
      01001986H   LINE      CODE     ---       #196

      ---         MODULE    ---      ---       ADC
      0100224EH   PUBLIC    CODE     ---       _ADC_ConfigADCVref
      0100205AH   PUBLIC    CODE     ---       ADC_GetADCResult
      01002185H   PUBLIC    CODE     ---       _ADC_EnableChannel
      010021DAH   PUBLIC    CODE     ---       _ADC_ConfigRunMode
      01000006H   PUBLIC    CODE     ---       ADC_Start
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 21


      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 22


      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 23


      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01000006H   BLOCK     CODE     ---       LVL=0
      01000006H   LINE      CODE     ---       #66
      01000006H   LINE      CODE     ---       #67
      01000006H   LINE      CODE     ---       #68
      01000009H   LINE      CODE     ---       #69
      ---         BLOCKEND  ---      ---       LVL=0

      010021DAH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCClkDiv
      00000005H   SYMBOL    DATA     BYTE      ADCResultTpye
      010021DAH   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      010021DAH   LINE      CODE     ---       #88
      010021DAH   LINE      CODE     ---       #89
      010021DAH   LINE      CODE     ---       #90
      010021DAH   LINE      CODE     ---       #92
      010021DCH   LINE      CODE     ---       #93
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 24


      010021DFH   LINE      CODE     ---       #94
      010021E0H   LINE      CODE     ---       #95
      010021E2H   LINE      CODE     ---       #97
      010021E4H   LINE      CODE     ---       #98
      010021E8H   LINE      CODE     ---       #99
      010021EDH   LINE      CODE     ---       #100
      010021EFH   LINE      CODE     ---       #101
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCTGSource
      00000005H   SYMBOL    DATA     BYTE      TGMode
      00000006H   SYMBOL    DATA     BYTE      Temp

      01002185H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCChannel
      01002185H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      01002185H   LINE      CODE     ---       #154
      01002185H   LINE      CODE     ---       #155
      01002185H   LINE      CODE     ---       #156
      01002185H   LINE      CODE     ---       #158
      01002187H   LINE      CODE     ---       #159
      0100218BH   LINE      CODE     ---       #160
      01002194H   LINE      CODE     ---       #161
      01002196H   LINE      CODE     ---       #163
      01002198H   LINE      CODE     ---       #164
      0100219CH   LINE      CODE     ---       #165
      010021A0H   LINE      CODE     ---       #166
      010021A2H   LINE      CODE     ---       #168
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      An31Channel
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000006H   SYMBOL    DATA     WORD      TrigTime
      00000005H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      ADCBrake
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000006H   SYMBOL    DATA     WORD      ADCCompareValue

      0100205AH   BLOCK     CODE     ---       LVL=0
      0100205AH   LINE      CODE     ---       #258
      0100205AH   LINE      CODE     ---       #259
      0100205AH   LINE      CODE     ---       #260
      01002061H   LINE      CODE     ---       #261
      01002061H   LINE      CODE     ---       #262
      01002074H   LINE      CODE     ---       #263
      01002074H   LINE      CODE     ---       #264
      0100207EH   LINE      CODE     ---       #265
      ---         BLOCKEND  ---      ---       LVL=0

      0100224EH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCVref
      0100224EH   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      0100224EH   LINE      CODE     ---       #344
      0100224EH   LINE      CODE     ---       #345
      0100224EH   LINE      CODE     ---       #346
      0100224EH   LINE      CODE     ---       #348
      01002252H   LINE      CODE     ---       #349
      01002254H   LINE      CODE     ---       #350
      01002255H   LINE      CODE     ---       #351
      01002256H   LINE      CODE     ---       #353
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       EPWM
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 25


      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 26


      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 27


      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      EpwmRunModeMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000005H   SYMBOL    DATA     BYTE      ClkDiv
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000004H   SYMBOL    DATA     WORD      Period
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000004H   SYMBOL    DATA     WORD      Duty
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000004H   SYMBOL    DATA     WORD      UpCmp
      00000002H   SYMBOL    DATA     WORD      DowmCmp
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      BrakeSource
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 28


      00000005H   SYMBOL    DATA     BYTE      CountMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      BrakeSource
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000005H   SYMBOL    DATA     BYTE      BrakeLevel
      00000007H   SYMBOL    DATA     BYTE      Channel
      00000005H   SYMBOL    DATA     BYTE      DeadTime
      00000007H   SYMBOL    DATA     BYTE      Channel
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000005H   SYMBOL    DATA     BYTE      MaskLevel
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      FBBrakeLevel
      00000006H   SYMBOL    DATA     BYTE      Temp

      ---         MODULE    ---      ---       GPIO
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 29


      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 30


      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 31


      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinMSK
      00000003H   SYMBOL    DATA     BYTE      PinMode
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinMSK
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinMSK
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinNum
      00000006H   SYMBOL    DATA     BYTE      PinIntFlag
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinNum

      ---         MODULE    ---      ---       SYSTEM
      01000056H   PUBLIC    CODE     ---       SYS_EnterStop
      0100000EH   PUBLIC    CODE     ---       SYS_EnableWakeUp
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 32


      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 33


      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 34


      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      LVDValue
      00000006H   SYMBOL    DATA     BYTE      Temp

      0100000EH   BLOCK     CODE     ---       LVL=0
      0100000EH   LINE      CODE     ---       #333
      0100000EH   LINE      CODE     ---       #334
      0100000EH   LINE      CODE     ---       #335
      01000011H   LINE      CODE     ---       #336
      ---         BLOCKEND  ---      ---       LVL=0

      01000056H   BLOCK     CODE     ---       LVL=0
      01000056H   LINE      CODE     ---       #358
      01000056H   LINE      CODE     ---       #359
      01000056H   LINE      CODE     ---       #360
      01000057H   LINE      CODE     ---       #361
      01000058H   LINE      CODE     ---       #362
      0100005BH   LINE      CODE     ---       #363
      0100005CH   LINE      CODE     ---       #364
      0100005DH   LINE      CODE     ---       #365
      0100005EH   LINE      CODE     ---       #366
      0100005FH   LINE      CODE     ---       #367
      01000060H   LINE      CODE     ---       #368
      01000061H   LINE      CODE     ---       #369
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      clkdiv
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000004H   SYMBOL    DATA     WORD      time
      00000003H   SYMBOL    DATA     BYTE      Temp

      ---         MODULE    ---      ---       TIMER
      01002147H   PUBLIC    CODE     ---       _TMR_Stop
      01002128H   PUBLIC    CODE     ---       _TMR_Start
      010021A3H   PUBLIC    CODE     ---       _TMR_EnableOverflowInt
      0100200DH   PUBLIC    CODE     ---       _TMR_ConfigTimerPeriod
      01001DF3H   PUBLIC    CODE     ---       _TMR_ConfigTimerClk
      01001DA7H   PUBLIC    CODE     ---       _TMR_ConfigRunMode
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 35


      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 36


      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 37


      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01001DA7H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000005H   SYMBOL    DATA     BYTE      TimerMode
      00000003H   SYMBOL    DATA     BYTE      TimerModeBranch
      01001DA7H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      01001DA7H   LINE      CODE     ---       #74
      01001DA7H   LINE      CODE     ---       #75
      01001DA7H   LINE      CODE     ---       #76
      01001DA7H   LINE      CODE     ---       #78
      01001DB6H   LINE      CODE     ---       #79
      01001DB6H   LINE      CODE     ---       #80
      01001DB6H   LINE      CODE     ---       #81
      01001DB8H   LINE      CODE     ---       #82
      01001DBCH   LINE      CODE     ---       #83
      01001DC2H   LINE      CODE     ---       #84
      01001DC2H   LINE      CODE     ---       #85
      01001DC4H   LINE      CODE     ---       #86
      01001DC4H   LINE      CODE     ---       #87
      01001DC6H   LINE      CODE     ---       #88
      01001DCAH   LINE      CODE     ---       #89
      01001DD7H   LINE      CODE     ---       #90
      01001DD9H   LINE      CODE     ---       #91
      01001DDAH   LINE      CODE     ---       #92
      01001DDAH   LINE      CODE     ---       #93
      01001DDCH   LINE      CODE     ---       #94
      01001DDFH   LINE      CODE     ---       #95
      01001DE0H   LINE      CODE     ---       #96
      01001DE2H   LINE      CODE     ---       #97
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 38


      01001DE3H   LINE      CODE     ---       #98
      01001DE3H   LINE      CODE     ---       #99
      01001DE5H   LINE      CODE     ---       #100
      01001DE9H   LINE      CODE     ---       #101
      01001DF0H   LINE      CODE     ---       #102
      01001DF2H   LINE      CODE     ---       #103
      01001DF2H   LINE      CODE     ---       #104
      01001DF2H   LINE      CODE     ---       #105
      01001DF2H   LINE      CODE     ---       #106
      01001DF2H   LINE      CODE     ---       #107
      ---         BLOCKEND  ---      ---       LVL=0

      01001DF3H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000005H   SYMBOL    DATA     BYTE      TimerClkDiv
      01001DF3H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      01001DF3H   LINE      CODE     ---       #117
      01001DF3H   LINE      CODE     ---       #118
      01001DF3H   LINE      CODE     ---       #119
      01001DF3H   LINE      CODE     ---       #121
      01001E02H   LINE      CODE     ---       #122
      01001E02H   LINE      CODE     ---       #123
      01001E02H   LINE      CODE     ---       #124
      01001E04H   LINE      CODE     ---       #125
      01001E08H   LINE      CODE     ---       #126
      01001E0EH   LINE      CODE     ---       #127
      01001E0EH   LINE      CODE     ---       #128
      01001E10H   LINE      CODE     ---       #129
      01001E10H   LINE      CODE     ---       #130
      01001E12H   LINE      CODE     ---       #131
      01001E16H   LINE      CODE     ---       #132
      01001E1BH   LINE      CODE     ---       #133
      01001E1DH   LINE      CODE     ---       #134
      01001E1EH   LINE      CODE     ---       #135
      01001E1EH   LINE      CODE     ---       #136
      01001E20H   LINE      CODE     ---       #137
      01001E24H   LINE      CODE     ---       #138
      01001E29H   LINE      CODE     ---       #139
      01001E29H   LINE      CODE     ---       #140
      01001E2BH   LINE      CODE     ---       #141
      01001E2BH   LINE      CODE     ---       #142
      01001E2DH   LINE      CODE     ---       #143
      01001E31H   LINE      CODE     ---       #144
      01001E3AH   LINE      CODE     ---       #145
      01001E3CH   LINE      CODE     ---       #146
      01001E3CH   LINE      CODE     ---       #147
      01001E3CH   LINE      CODE     ---       #148
      01001E3CH   LINE      CODE     ---       #149
      01001E3CH   LINE      CODE     ---       #150
      ---         BLOCKEND  ---      ---       LVL=0

      0100200DH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000005H   SYMBOL    DATA     BYTE      TimerPeriodHigh
      00000003H   SYMBOL    DATA     BYTE      TimerPeriodLow
      0100200DH   LINE      CODE     ---       #160
      0100200DH   LINE      CODE     ---       #161
      0100200DH   LINE      CODE     ---       #162
      0100201CH   LINE      CODE     ---       #163
      0100201CH   LINE      CODE     ---       #164
      0100201CH   LINE      CODE     ---       #165
      0100201EH   LINE      CODE     ---       #166
      01002020H   LINE      CODE     ---       #167
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 39


      01002021H   LINE      CODE     ---       #168
      01002021H   LINE      CODE     ---       #169
      01002023H   LINE      CODE     ---       #170
      01002025H   LINE      CODE     ---       #171
      01002026H   LINE      CODE     ---       #172
      01002026H   LINE      CODE     ---       #173
      01002028H   LINE      CODE     ---       #174
      0100202AH   LINE      CODE     ---       #175
      0100202BH   LINE      CODE     ---       #176
      0100202BH   LINE      CODE     ---       #177
      0100202FH   LINE      CODE     ---       #178
      01002033H   LINE      CODE     ---       #179
      01002033H   LINE      CODE     ---       #180
      01002033H   LINE      CODE     ---       #181
      01002033H   LINE      CODE     ---       #182
      01002033H   LINE      CODE     ---       #183
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000007H   SYMBOL    DATA     BYTE      Timern

      010021A3H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      010021A3H   LINE      CODE     ---       #256
      010021A3H   LINE      CODE     ---       #257
      010021A3H   LINE      CODE     ---       #258
      010021B2H   LINE      CODE     ---       #259
      010021B2H   LINE      CODE     ---       #260
      010021B2H   LINE      CODE     ---       #261
      010021B4H   LINE      CODE     ---       #262
      010021B5H   LINE      CODE     ---       #263
      010021B5H   LINE      CODE     ---       #264
      010021B7H   LINE      CODE     ---       #265
      010021B8H   LINE      CODE     ---       #266
      010021B8H   LINE      CODE     ---       #267
      010021BBH   LINE      CODE     ---       #268
      010021BCH   LINE      CODE     ---       #269
      010021BCH   LINE      CODE     ---       #270
      010021BFH   LINE      CODE     ---       #271
      010021BFH   LINE      CODE     ---       #272
      010021BFH   LINE      CODE     ---       #273
      010021BFH   LINE      CODE     ---       #274
      010021BFH   LINE      CODE     ---       #275
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000006H   SYMBOL    DATA     BYTE      IntFlag
      00000007H   SYMBOL    DATA     BYTE      Timern

      01002128H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      01002128H   LINE      CODE     ---       #368
      01002128H   LINE      CODE     ---       #369
      01002128H   LINE      CODE     ---       #370
      01002137H   LINE      CODE     ---       #371
      01002137H   LINE      CODE     ---       #372
      01002137H   LINE      CODE     ---       #373
      0100213AH   LINE      CODE     ---       #374
      0100213BH   LINE      CODE     ---       #375
      0100213BH   LINE      CODE     ---       #376
      0100213EH   LINE      CODE     ---       #377
      0100213FH   LINE      CODE     ---       #378
      0100213FH   LINE      CODE     ---       #379
      01002142H   LINE      CODE     ---       #380
      01002143H   LINE      CODE     ---       #381
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 40


      01002143H   LINE      CODE     ---       #382
      01002146H   LINE      CODE     ---       #383
      01002146H   LINE      CODE     ---       #384
      01002146H   LINE      CODE     ---       #385
      01002146H   LINE      CODE     ---       #386
      01002146H   LINE      CODE     ---       #387
      ---         BLOCKEND  ---      ---       LVL=0

      01002147H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      01002147H   LINE      CODE     ---       #395
      01002147H   LINE      CODE     ---       #396
      01002147H   LINE      CODE     ---       #397
      01002156H   LINE      CODE     ---       #398
      01002156H   LINE      CODE     ---       #399
      01002156H   LINE      CODE     ---       #400
      01002159H   LINE      CODE     ---       #401
      0100215AH   LINE      CODE     ---       #402
      0100215AH   LINE      CODE     ---       #403
      0100215DH   LINE      CODE     ---       #404
      0100215EH   LINE      CODE     ---       #405
      0100215EH   LINE      CODE     ---       #406
      01002161H   LINE      CODE     ---       #407
      01002162H   LINE      CODE     ---       #408
      01002162H   LINE      CODE     ---       #409
      01002165H   LINE      CODE     ---       #410
      01002165H   LINE      CODE     ---       #411
      01002165H   LINE      CODE     ---       #412
      01002165H   LINE      CODE     ---       #413
      01002165H   LINE      CODE     ---       #414
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timer2Mode
      00000005H   SYMBOL    DATA     BYTE      Timer2LoadMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      TimerClkDiv
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000004H   SYMBOL    DATA     WORD      TimerPeriod
      00000007H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000005H   SYMBOL    DATA     BYTE      CompareMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000003H   SYMBOL    DATA     BYTE      Timer2CCn
      00000004H   SYMBOL    DATA     WORD      CompareValue
      00000007H   SYMBOL    DATA     BYTE      Timer2CompareIntMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000005H   SYMBOL    DATA     BYTE      Timer2CaptureMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000004H   SYMBOL    DATA     WORD      CaputerValue
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn

      ---         MODULE    ---      ---       UART
      01002260H   PUBLIC    CODE     ---       _UART_ConfigBRTPeriod
      01002257H   PUBLIC    CODE     ---       _UART_ConfigBRTClk
      01002272H   PUBLIC    CODE     ---       UART_EnableBRT
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 41


      010021F0H   PUBLIC    CODE     ---       _UART_GetBuff
      01002166H   PUBLIC    CODE     ---       _UART_ClearReceiveIntFlag
      0100207FH   PUBLIC    CODE     ---       _UART_GetReceiveIntFlag
      0100223DH   PUBLIC    CODE     ---       _UART_EnableInt
      01002217H   PUBLIC    CODE     ---       _UART_EnableReceive
      01002204H   PUBLIC    CODE     ---       _UART_EnableDoubleFrequency
      01001C41H   PUBLIC    CODE     ---       _UART_ConfigRunMode
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 42


      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 43


      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01001C41H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000005H   SYMBOL    DATA     BYTE      UARTMode
      00000003H   SYMBOL    DATA     BYTE      UARTBaudTimer
      01001C41H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      01001C41H   LINE      CODE     ---       #70
      01001C41H   LINE      CODE     ---       #71
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 44


      01001C41H   LINE      CODE     ---       #72
      01001C41H   LINE      CODE     ---       #74
      01001C44H   LINE      CODE     ---       #75
      01001C44H   LINE      CODE     ---       #76
      01001C46H   LINE      CODE     ---       #77
      01001C4AH   LINE      CODE     ---       #78
      01001C51H   LINE      CODE     ---       #79
      01001C53H   LINE      CODE     ---       #81
      01001C56H   LINE      CODE     ---       #82
      01001C62H   LINE      CODE     ---       #83
      01001C62H   LINE      CODE     ---       #84
      01001C62H   LINE      CODE     ---       #85
      01001C62H   LINE      CODE     ---       #86
      01001C62H   LINE      CODE     ---       #87
      01001C65H   LINE      CODE     ---       #88
      01001C67H   LINE      CODE     ---       #89
      01001C67H   LINE      CODE     ---       #90
      01001C6AH   LINE      CODE     ---       #91
      01001C6CH   LINE      CODE     ---       #92
      01001C6CH   LINE      CODE     ---       #93
      01001C6FH   LINE      CODE     ---       #94
      01001C6FH   LINE      CODE     ---       #95
      01001C6FH   LINE      CODE     ---       #96
      01001C6FH   LINE      CODE     ---       #97
      01001C6FH   LINE      CODE     ---       #99
      01001C6FH   LINE      CODE     ---       #100
      01001C74H   LINE      CODE     ---       #101
      01001C74H   LINE      CODE     ---       #102
      01001C76H   LINE      CODE     ---       #103
      01001C7AH   LINE      CODE     ---       #104
      01001C83H   LINE      CODE     ---       #105
      01001C85H   LINE      CODE     ---       #107
      01001C88H   LINE      CODE     ---       #108
      01001C94H   LINE      CODE     ---       #109
      01001C94H   LINE      CODE     ---       #110
      01001C94H   LINE      CODE     ---       #111
      01001C94H   LINE      CODE     ---       #112
      01001C94H   LINE      CODE     ---       #113
      01001C97H   LINE      CODE     ---       #114
      01001C98H   LINE      CODE     ---       #115
      01001C98H   LINE      CODE     ---       #116
      01001C9BH   LINE      CODE     ---       #117
      01001C9CH   LINE      CODE     ---       #118
      01001C9CH   LINE      CODE     ---       #119
      01001C9FH   LINE      CODE     ---       #120
      01001C9FH   LINE      CODE     ---       #121
      01001C9FH   LINE      CODE     ---       #122
      01001C9FH   LINE      CODE     ---       #123
      01001C9FH   LINE      CODE     ---       #124
      01001C9FH   LINE      CODE     ---       #125
      ---         BLOCKEND  ---      ---       LVL=0

      01002204H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      01002204H   LINE      CODE     ---       #133
      01002204H   LINE      CODE     ---       #134
      01002204H   LINE      CODE     ---       #135
      0100220AH   LINE      CODE     ---       #136
      0100220AH   LINE      CODE     ---       #137
      0100220DH   LINE      CODE     ---       #138
      0100220DH   LINE      CODE     ---       #139
      01002213H   LINE      CODE     ---       #140
      01002213H   LINE      CODE     ---       #141
      01002216H   LINE      CODE     ---       #142
      01002216H   LINE      CODE     ---       #143
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 45


      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000007H   SYMBOL    DATA     BYTE      UARTn

      01002217H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      01002217H   LINE      CODE     ---       #280
      01002217H   LINE      CODE     ---       #281
      01002217H   LINE      CODE     ---       #282
      0100221DH   LINE      CODE     ---       #283
      0100221DH   LINE      CODE     ---       #284
      01002220H   LINE      CODE     ---       #285
      01002220H   LINE      CODE     ---       #286
      01002226H   LINE      CODE     ---       #287
      01002226H   LINE      CODE     ---       #288
      01002229H   LINE      CODE     ---       #289
      01002229H   LINE      CODE     ---       #290
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn

      0100223DH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      0100223DH   LINE      CODE     ---       #317
      0100223DH   LINE      CODE     ---       #318
      0100223DH   LINE      CODE     ---       #319
      01002243H   LINE      CODE     ---       #320
      01002243H   LINE      CODE     ---       #321
      01002245H   LINE      CODE     ---       #322
      01002245H   LINE      CODE     ---       #323
      0100224BH   LINE      CODE     ---       #324
      0100224BH   LINE      CODE     ---       #325
      0100224DH   LINE      CODE     ---       #326
      0100224DH   LINE      CODE     ---       #327
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn

      0100207FH   BLOCK     CODE     ---       LVL=0
      00000006H   SYMBOL    DATA     BYTE      UARTn
      0100207FH   LINE      CODE     ---       #353
      01002081H   LINE      CODE     ---       #354
      01002081H   LINE      CODE     ---       #355
      01002087H   LINE      CODE     ---       #356
      01002087H   LINE      CODE     ---       #357
      01002091H   LINE      CODE     ---       #358
      01002091H   LINE      CODE     ---       #359
      01002097H   LINE      CODE     ---       #360
      01002097H   LINE      CODE     ---       #361
      010020A1H   LINE      CODE     ---       #362
      010020A1H   LINE      CODE     ---       #363
      010020A3H   LINE      CODE     ---       #364
      ---         BLOCKEND  ---      ---       LVL=0

      01002166H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      01002166H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      temp
      ---         BLOCKEND  ---      ---       LVL=1
      01002166H   LINE      CODE     ---       #373
      01002166H   LINE      CODE     ---       #374
      01002166H   LINE      CODE     ---       #377
      0100216CH   LINE      CODE     ---       #378
      0100216CH   LINE      CODE     ---       #379
      0100216EH   LINE      CODE     ---       #380
      01002171H   LINE      CODE     ---       #381
      01002175H   LINE      CODE     ---       #382
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 46


      01002175H   LINE      CODE     ---       #383
      0100217BH   LINE      CODE     ---       #384
      0100217BH   LINE      CODE     ---       #385
      0100217DH   LINE      CODE     ---       #386
      01002180H   LINE      CODE     ---       #387
      01002184H   LINE      CODE     ---       #388
      01002184H   LINE      CODE     ---       #389
      ---         BLOCKEND  ---      ---       LVL=0
      00000006H   SYMBOL    DATA     BYTE      UARTn
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000006H   SYMBOL    DATA     BYTE      temp

      010021F0H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      010021F0H   LINE      CODE     ---       #443
      010021F0H   LINE      CODE     ---       #444
      010021F0H   LINE      CODE     ---       #445
      010021F6H   LINE      CODE     ---       #446
      010021F6H   LINE      CODE     ---       #447
      010021F9H   LINE      CODE     ---       #448
      010021F9H   LINE      CODE     ---       #449
      01002201H   LINE      CODE     ---       #450
      01002201H   LINE      CODE     ---       #451
      01002203H   LINE      CODE     ---       #452
      01002203H   LINE      CODE     ---       #453
      01002203H   LINE      CODE     ---       #454
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000005H   SYMBOL    DATA     BYTE      UARTSendValue
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000005H   SYMBOL    DATA     BYTE      UARTSendValue
      00000007H   SYMBOL    DATA     BYTE      UARTn

      01002272H   BLOCK     CODE     ---       LVL=0
      01002272H   LINE      CODE     ---       #534
      01002272H   LINE      CODE     ---       #535
      01002272H   LINE      CODE     ---       #536
      01002279H   LINE      CODE     ---       #537
      ---         BLOCKEND  ---      ---       LVL=0

      01002257H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      BRTClkDiv
      01002257H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      01002257H   LINE      CODE     ---       #544
      01002257H   LINE      CODE     ---       #545
      01002257H   LINE      CODE     ---       #546
      01002257H   LINE      CODE     ---       #548
      0100225BH   LINE      CODE     ---       #549
      0100225DH   LINE      CODE     ---       #550
      0100225EH   LINE      CODE     ---       #551
      0100225FH   LINE      CODE     ---       #552
      ---         BLOCKEND  ---      ---       LVL=0

      01002260H   BLOCK     CODE     ---       LVL=0
      00000006H   SYMBOL    DATA     WORD      BRTPeriod
      01002260H   LINE      CODE     ---       #560
      01002260H   LINE      CODE     ---       #561
      01002260H   LINE      CODE     ---       #562
      01002265H   LINE      CODE     ---       #563
      01002268H   LINE      CODE     ---       #564
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       WDT
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 47


      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 48


      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 49


      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      TsysCoefficient
      00000006H   SYMBOL    DATA     BYTE      Temp

      ---         MODULE    ---      ---       FLASH
      01001F5CH   PUBLIC    CODE     ---       _FLASH_Erase
      01001F28H   PUBLIC    CODE     ---       _FLASH_Read
      01001EF3H   PUBLIC    CODE     ---       _FLASH_Write
      0100001EH   PUBLIC    CODE     ---       FLASH_Lock
      01000016H   PUBLIC    CODE     ---       FLASH_UnLock
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 50


      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 51


      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 52


      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01000016H   BLOCK     CODE     ---       LVL=0
      01000016H   LINE      CODE     ---       #68
      01000016H   LINE      CODE     ---       #69
      01000016H   LINE      CODE     ---       #70
      01000019H   LINE      CODE     ---       #71
      ---         BLOCKEND  ---      ---       LVL=0

      0100001EH   BLOCK     CODE     ---       LVL=0
      0100001EH   LINE      CODE     ---       #79
      0100001EH   LINE      CODE     ---       #80
      0100001EH   LINE      CODE     ---       #81
      01000021H   LINE      CODE     ---       #82
      ---         BLOCKEND  ---      ---       LVL=0

      01001EF3H   BLOCK     CODE     ---       LVL=0
      00000002H   SYMBOL    DATA     BYTE      FLASHModule
      00000004H   SYMBOL    DATA     WORD      Addr
      00000003H   SYMBOL    DATA     BYTE      Data
      01001EF3H   LINE      CODE     ---       #95
      01001EF5H   LINE      CODE     ---       #96
      01001EF5H   LINE      CODE     ---       #97
      01001EF9H   LINE      CODE     ---       #98
      01001EFBH   LINE      CODE     ---       #99
      01001EFEH   LINE      CODE     ---       #101
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 53


      01001F01H   LINE      CODE     ---       #102
      01001F01H   LINE      CODE     ---       #103
      01001F03H   LINE      CODE     ---       #104
      01001F04H   LINE      CODE     ---       #105
      01001F09H   LINE      CODE     ---       #106
      01001F0AH   LINE      CODE     ---       #107
      01001F0BH   LINE      CODE     ---       #108
      01001F0CH   LINE      CODE     ---       #109
      01001F0DH   LINE      CODE     ---       #110
      01001F0EH   LINE      CODE     ---       #111
      01001F0FH   LINE      CODE     ---       #112
      01001F14H   LINE      CODE     ---       #113
      01001F16H   LINE      CODE     ---       #114
      01001F17H   LINE      CODE     ---       #116
      01001F17H   LINE      CODE     ---       #117
      01001F1CH   LINE      CODE     ---       #118
      01001F1DH   LINE      CODE     ---       #119
      01001F1EH   LINE      CODE     ---       #120
      01001F1FH   LINE      CODE     ---       #121
      01001F20H   LINE      CODE     ---       #122
      01001F21H   LINE      CODE     ---       #123
      01001F22H   LINE      CODE     ---       #124
      01001F27H   LINE      CODE     ---       #125
      01001F27H   LINE      CODE     ---       #126
      ---         BLOCKEND  ---      ---       LVL=0

      01001F28H   BLOCK     CODE     ---       LVL=0
      00000003H   SYMBOL    DATA     BYTE      FLASHModule
      00000004H   SYMBOL    DATA     WORD      Addr
      01001F28H   LINE      CODE     ---       #138
      01001F2AH   LINE      CODE     ---       #139
      01001F2AH   LINE      CODE     ---       #140
      01001F2CH   LINE      CODE     ---       #141
      01001F2FH   LINE      CODE     ---       #142
      01001F32H   LINE      CODE     ---       #143
      01001F32H   LINE      CODE     ---       #144
      01001F34H   LINE      CODE     ---       #145
      01001F35H   LINE      CODE     ---       #146
      01001F3AH   LINE      CODE     ---       #147
      01001F3BH   LINE      CODE     ---       #148
      01001F3CH   LINE      CODE     ---       #149
      01001F3DH   LINE      CODE     ---       #150
      01001F3EH   LINE      CODE     ---       #151
      01001F3FH   LINE      CODE     ---       #152
      01001F40H   LINE      CODE     ---       #153
      01001F45H   LINE      CODE     ---       #154
      01001F47H   LINE      CODE     ---       #155
      01001F49H   LINE      CODE     ---       #157
      01001F49H   LINE      CODE     ---       #158
      01001F4EH   LINE      CODE     ---       #159
      01001F4FH   LINE      CODE     ---       #160
      01001F50H   LINE      CODE     ---       #161
      01001F51H   LINE      CODE     ---       #162
      01001F52H   LINE      CODE     ---       #163
      01001F53H   LINE      CODE     ---       #164
      01001F54H   LINE      CODE     ---       #165
      01001F59H   LINE      CODE     ---       #166
      01001F59H   LINE      CODE     ---       #167
      01001F5BH   LINE      CODE     ---       #168
      ---         BLOCKEND  ---      ---       LVL=0

      01001F5CH   BLOCK     CODE     ---       LVL=0
      00000003H   SYMBOL    DATA     BYTE      FLASHModule
      00000004H   SYMBOL    DATA     WORD      Addr
      01001F5CH   LINE      CODE     ---       #179
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 54


      01001F5EH   LINE      CODE     ---       #180
      01001F5EH   LINE      CODE     ---       #181
      01001F60H   LINE      CODE     ---       #182
      01001F63H   LINE      CODE     ---       #183
      01001F66H   LINE      CODE     ---       #184
      01001F66H   LINE      CODE     ---       #185
      01001F68H   LINE      CODE     ---       #186
      01001F69H   LINE      CODE     ---       #187
      01001F6EH   LINE      CODE     ---       #188
      01001F6FH   LINE      CODE     ---       #189
      01001F70H   LINE      CODE     ---       #190
      01001F71H   LINE      CODE     ---       #191
      01001F72H   LINE      CODE     ---       #192
      01001F73H   LINE      CODE     ---       #193
      01001F74H   LINE      CODE     ---       #194
      01001F79H   LINE      CODE     ---       #195
      01001F7BH   LINE      CODE     ---       #196
      01001F7CH   LINE      CODE     ---       #198
      01001F7CH   LINE      CODE     ---       #199
      01001F81H   LINE      CODE     ---       #200
      01001F82H   LINE      CODE     ---       #201
      01001F83H   LINE      CODE     ---       #202
      01001F84H   LINE      CODE     ---       #203
      01001F85H   LINE      CODE     ---       #204
      01001F86H   LINE      CODE     ---       #205
      01001F87H   LINE      CODE     ---       #206
      01001F8CH   LINE      CODE     ---       #207
      01001F8CH   LINE      CODE     ---       #208
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ADC_INIT
      010021C0H   PUBLIC    CODE     ---       ADC_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 55


      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 56


      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 57


      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      010021C0H   BLOCK     CODE     ---       LVL=0
      010021C0H   LINE      CODE     ---       #65
      010021C0H   LINE      CODE     ---       #66
      010021C0H   LINE      CODE     ---       #68
      010021C7H   LINE      CODE     ---       #71
      010021CCH   LINE      CODE     ---       #72
      010021D2H   LINE      CODE     ---       #75
      010021D7H   LINE      CODE     ---       #78
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       DEFINE
      0200008CH   PUBLIC    XDATA    BYTE      Motor_Direction_Data
      0200008AH   PUBLIC    XDATA    WORD      Power_Off_Wait_Time
      02000088H   PUBLIC    XDATA    WORD      Battery_ADC_Wait_Time
      00000025H.5 PUBLIC    BIT      BIT       Delay_Over
      02000087H   PUBLIC    XDATA    BYTE      Get_String_Wait_Time
      02000085H   PUBLIC    XDATA    WORD      Delay_Time
      00000025H.4 PUBLIC    BIT      BIT       Get_String_Buff
      02000083H   PUBLIC    XDATA    INT       Count_Toggle
      02000081H   PUBLIC    XDATA    WORD      Delay_Time_Count
      0200007FH   PUBLIC    XDATA    WORD      Motor_Speed_Data
      0200007DH   PUBLIC    XDATA    INT       Num
      0200007CH   PUBLIC    XDATA    BYTE      K3_cnt
      0200007BH   PUBLIC    XDATA    BYTE      K2_cnt
      0200007AH   PUBLIC    XDATA    BYTE      K1_cnt
      02000078H   PUBLIC    XDATA    WORD      longhit_cnt
      00000025H.3 PUBLIC    BIT      BIT       Center_Line_Control
      00000025H.2 PUBLIC    BIT      BIT       Power_count_clean
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 58


      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 59


      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 60


      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      ---         MODULE    ---      ---       GPIO_INIT
      0100222AH   PUBLIC    CODE     ---       GPIO_Key_Interrupt_Config
      01001B6CH   PUBLIC    CODE     ---       GPIO_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 61


      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 62


      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 63


      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01001B6CH   BLOCK     CODE     ---       LVL=0
      01001B6CH   LINE      CODE     ---       #42
      01001B6CH   LINE      CODE     ---       #43
      01001B6CH   LINE      CODE     ---       #44
      01001B6FH   LINE      CODE     ---       #45
      01001B72H   LINE      CODE     ---       #46
      01001B75H   LINE      CODE     ---       #47
      01001B78H   LINE      CODE     ---       #48
      01001B7BH   LINE      CODE     ---       #49
      01001B7EH   LINE      CODE     ---       #50
      01001B81H   LINE      CODE     ---       #51
      01001B84H   LINE      CODE     ---       #77
      01001B89H   LINE      CODE     ---       #78
      01001B8CH   LINE      CODE     ---       #79
      01001B93H   LINE      CODE     ---       #81
      01001B98H   LINE      CODE     ---       #82
      01001B9BH   LINE      CODE     ---       #83
      01001BA2H   LINE      CODE     ---       #85
      01001BA7H   LINE      CODE     ---       #86
      01001BAAH   LINE      CODE     ---       #87
      01001BB1H   LINE      CODE     ---       #90
      01001BB6H   LINE      CODE     ---       #91
      01001BB9H   LINE      CODE     ---       #92
      01001BC0H   LINE      CODE     ---       #94
      01001BC5H   LINE      CODE     ---       #95
      01001BC8H   LINE      CODE     ---       #96
      01001BCFH   LINE      CODE     ---       #100
      01001BD4H   LINE      CODE     ---       #101
      01001BD7H   LINE      CODE     ---       #102
      01001BD9H   LINE      CODE     ---       #105
      01001BDBH   LINE      CODE     ---       #122
      ---         BLOCKEND  ---      ---       LVL=0

      0100222AH   BLOCK     CODE     ---       LVL=0
      0100222AH   LINE      CODE     ---       #131
      0100222AH   LINE      CODE     ---       #132
      0100222AH   LINE      CODE     ---       #134
      01002230H   LINE      CODE     ---       #135
      01002233H   LINE      CODE     ---       #138
      01002237H   LINE      CODE     ---       #139
      0100223AH   LINE      CODE     ---       #142
      0100223CH   LINE      CODE     ---       #143
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       TIMER_INIT
      01001FE4H   PUBLIC    CODE     ---       TMR1_Config
      01001FBAH   PUBLIC    CODE     ---       TMR0_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 64


      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 65


      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 66


      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01001FBAH   BLOCK     CODE     ---       LVL=0
      01001FBAH   LINE      CODE     ---       #11
      01001FBAH   LINE      CODE     ---       #12
      01001FBAH   LINE      CODE     ---       #16
      01001FC2H   LINE      CODE     ---       #20
      01001FC8H   LINE      CODE     ---       #24
      01001FD1H   LINE      CODE     ---       #29
      01001FD6H   LINE      CODE     ---       #34
      01001FDCH   LINE      CODE     ---       #35
      01001FDFH   LINE      CODE     ---       #40
      ---         BLOCKEND  ---      ---       LVL=0

      01001FE4H   BLOCK     CODE     ---       LVL=0
      01001FE4H   LINE      CODE     ---       #50
      01001FE4H   LINE      CODE     ---       #51
      01001FE4H   LINE      CODE     ---       #55
      01001FEDH   LINE      CODE     ---       #59
      01001FF4H   LINE      CODE     ---       #63
      01001FFDH   LINE      CODE     ---       #68
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 67


      01002002H   LINE      CODE     ---       #73
      01002005H   LINE      CODE     ---       #74
      01002008H   LINE      CODE     ---       #79
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       UART_INIT
      02000052H   PUBLIC    XDATA    BYTE      ?_UART_Send_String?BYTE
      01001CA0H   PUBLIC    CODE     ---       _UART_Send_String
      01001E3DH   PUBLIC    CODE     ---       UART_1_Config
      01001D58H   PUBLIC    CODE     ---       UART_0_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 68


      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 69


      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01001D58H   BLOCK     CODE     ---       LVL=0
      01001D58H   BLOCK     CODE     NEAR LAB  LVL=1
      02000044H   SYMBOL    XDATA    WORD      BRTValue
      02000046H   SYMBOL    XDATA    DWORD     BaudRateVlue
      ---         BLOCKEND  ---      ---       LVL=1
      01001D58H   LINE      CODE     ---       #42
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 70


      01001D58H   LINE      CODE     ---       #43
      01001D58H   LINE      CODE     ---       #44
      01001D62H   LINE      CODE     ---       #45
      01001D6AH   LINE      CODE     ---       #143
      01001D73H   LINE      CODE     ---       #144
      01001D78H   LINE      CODE     ---       #147
      01001D7DH   LINE      CODE     ---       #148
      01001D82H   LINE      CODE     ---       #152
      01001D8DH   LINE      CODE     ---       #153
      01001D90H   LINE      CODE     ---       #156
      01001D96H   LINE      CODE     ---       #157
      01001D9BH   LINE      CODE     ---       #159
      01001DA0H   LINE      CODE     ---       #160
      01001DA3H   LINE      CODE     ---       #161
      01001DA6H   LINE      CODE     ---       #162
      ---         BLOCKEND  ---      ---       LVL=0

      01001E3DH   BLOCK     CODE     ---       LVL=0
      01001E3DH   BLOCK     CODE     NEAR LAB  LVL=1
      02000044H   SYMBOL    XDATA    WORD      BRTValue
      02000046H   SYMBOL    XDATA    DWORD     BaudRateVlue
      ---         BLOCKEND  ---      ---       LVL=1
      01001E3DH   LINE      CODE     ---       #223
      01001E3DH   LINE      CODE     ---       #224
      01001E3DH   LINE      CODE     ---       #225
      01001E47H   LINE      CODE     ---       #226
      01001E4FH   LINE      CODE     ---       #324
      01001E58H   LINE      CODE     ---       #325
      01001E5DH   LINE      CODE     ---       #328
      01001E62H   LINE      CODE     ---       #329
      01001E67H   LINE      CODE     ---       #333
      01001E72H   LINE      CODE     ---       #334
      01001E75H   LINE      CODE     ---       #337
      01001E7BH   LINE      CODE     ---       #347
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     CHAR      ch
      00000001H   SYMBOL    DATA     ---       s

      01001CA0H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      02000053H   SYMBOL    XDATA    ---       String
      02000056H   SYMBOL    XDATA    BYTE      Length
      01001CABH   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Index
      ---         BLOCKEND  ---      ---       LVL=1
      01001CA0H   LINE      CODE     ---       #408
      01001CABH   LINE      CODE     ---       #409
      01001CABH   LINE      CODE     ---       #410
      01001CADH   LINE      CODE     ---       #411
      01001CB7H   LINE      CODE     ---       #412
      01001CB7H   LINE      CODE     ---       #413
      01001CBAH   LINE      CODE     ---       #414
      01001CBAH   LINE      CODE     ---       #415
      01001CCFH   LINE      CODE     ---       #416
      01001CD4H   LINE      CODE     ---       #417
      01001CD7H   LINE      CODE     ---       #418
      01001CD7H   LINE      CODE     ---       #419
      01001CDCH   LINE      CODE     ---       #420
      01001CDCH   LINE      CODE     ---       #421
      01001CF1H   LINE      CODE     ---       #422
      01001CF6H   LINE      CODE     ---       #423
      01001CF9H   LINE      CODE     ---       #424
      01001CF9H   LINE      CODE     ---       #425
      01001CFCH   LINE      CODE     ---       #426
      ---         BLOCKEND  ---      ---       LVL=0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 71



      ---         MODULE    ---      ---       ISR
      020000A0H   PUBLIC    XDATA    INT       Num_Reverse_Pulse
      0200009EH   PUBLIC    XDATA    INT       Num_Forward_Pulse
      0100003AH   PUBLIC    CODE     ---       SPI_IRQHandler
      01000039H   PUBLIC    CODE     ---       I2C_IRQHandler
      01000038H   PUBLIC    CODE     ---       WDT_IRQHandler
      01000037H   PUBLIC    CODE     ---       ADC_IRQHandler
      01000036H   PUBLIC    CODE     ---       EPWM_IRQHandler
      01000032H   PUBLIC    CODE     ---       Timer4_IRQHandler
      01000031H   PUBLIC    CODE     ---       Timer3_IRQHandler
      01000030H   PUBLIC    CODE     ---       ACMP_IRQHandler
      0100002FH   PUBLIC    CODE     ---       LSE_IRQHandler
      0100002EH   PUBLIC    CODE     ---       LVD_IRQHandler
      0100002AH   PUBLIC    CODE     ---       P3EI_IRQHandler
      01001A81H   PUBLIC    CODE     ---       P2EI_IRQHandler
      01001A09H   PUBLIC    CODE     ---       P1EI_IRQHandler
      01000029H   PUBLIC    CODE     ---       P0EI_IRQHandler
      01000022H   PUBLIC    CODE     ---       UART1_IRQHandler
      0100001AH   PUBLIC    CODE     ---       Timer2_IRQHandler
      01001CFDH   PUBLIC    CODE     ---       UART0_IRQHandler
      010020A4H   PUBLIC    CODE     ---       Timer1_IRQHandler
      01000012H   PUBLIC    CODE     ---       INT1_IRQHandler
      0100146FH   PUBLIC    CODE     ---       Timer0_IRQHandler
      0100000AH   PUBLIC    CODE     ---       INT0_IRQHandler
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 72


      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 73


      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 74


      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      0100000AH   BLOCK     CODE     ---       LVL=0
      0100000AH   LINE      CODE     ---       #73
      0100000AH   LINE      CODE     ---       #76
      ---         BLOCKEND  ---      ---       LVL=0

      0100146FH   BLOCK     CODE     ---       LVL=0
      0100146FH   LINE      CODE     ---       #85
      0100147EH   LINE      CODE     ---       #87
      01001481H   LINE      CODE     ---       #88
      01001484H   LINE      CODE     ---       #90
      01001486H   LINE      CODE     ---       #91
      01001494H   LINE      CODE     ---       #92
      010014A2H   LINE      CODE     ---       #93
      010014B7H   LINE      CODE     ---       #95
      010014D3H   LINE      CODE     ---       #96
      010014D3H   LINE      CODE     ---       #97
      010014D3H   LINE      CODE     ---       #98
      010014D3H   LINE      CODE     ---       #99
      010014DFH   LINE      CODE     ---       #100
      010014DFH   LINE      CODE     ---       #101
      010014DFH   LINE      CODE     ---       #102
      010014DFH   LINE      CODE     ---       #103
      010014DFH   LINE      CODE     ---       #104
      010014DFH   LINE      CODE     ---       #105
      010014E1H   LINE      CODE     ---       #106
      010014E1H   LINE      CODE     ---       #107
      010014E1H   LINE      CODE     ---       #108
      010014EDH   LINE      CODE     ---       #109
      010014EDH   LINE      CODE     ---       #110
      010014EDH   LINE      CODE     ---       #111
      010014EDH   LINE      CODE     ---       #112
      010014EDH   LINE      CODE     ---       #113
      010014EDH   LINE      CODE     ---       #114
      010014EFH   LINE      CODE     ---       #115
      010014EFH   LINE      CODE     ---       #116
      010014EFH   LINE      CODE     ---       #117
      010014FBH   LINE      CODE     ---       #118
      010014FBH   LINE      CODE     ---       #119
      010014FBH   LINE      CODE     ---       #120
      010014FBH   LINE      CODE     ---       #121
      010014FBH   LINE      CODE     ---       #122
      010014FBH   LINE      CODE     ---       #123
      010014FDH   LINE      CODE     ---       #124
      010014FDH   LINE      CODE     ---       #125
      010014FDH   LINE      CODE     ---       #126
      01001509H   LINE      CODE     ---       #127
      01001509H   LINE      CODE     ---       #128
      0100150BH   LINE      CODE     ---       #129
      01001511H   LINE      CODE     ---       #130
      01001511H   LINE      CODE     ---       #131
      01001511H   LINE      CODE     ---       #132
      01001511H   LINE      CODE     ---       #133
      01001511H   LINE      CODE     ---       #134
      01001511H   LINE      CODE     ---       #135
      01001511H   LINE      CODE     ---       #137
      01001514H   LINE      CODE     ---       #138
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 75


      01001514H   LINE      CODE     ---       #139
      0100151DH   LINE      CODE     ---       #140
      0100151FH   LINE      CODE     ---       #142
      0100151FH   LINE      CODE     ---       #143
      01001524H   LINE      CODE     ---       #144
      01001524H   LINE      CODE     ---       #146
      01001531H   LINE      CODE     ---       #147
      01001531H   LINE      CODE     ---       #148
      01001533H   LINE      CODE     ---       #149
      01001541H   LINE      CODE     ---       #150
      01001543H   LINE      CODE     ---       #152
      01001543H   LINE      CODE     ---       #153
      01001545H   LINE      CODE     ---       #154
      0100154CH   LINE      CODE     ---       #155
      0100154CH   LINE      CODE     ---       #157
      0100154FH   LINE      CODE     ---       #158
      0100154FH   LINE      CODE     ---       #159
      0100155DH   LINE      CODE     ---       #160
      01001572H   LINE      CODE     ---       #161
      01001572H   LINE      CODE     ---       #162
      01001574H   LINE      CODE     ---       #163
      01001574H   LINE      CODE     ---       #164
      01001574H   LINE      CODE     ---       #165
      01001576H   LINE      CODE     ---       #167
      01001576H   LINE      CODE     ---       #168
      0100157DH   LINE      CODE     ---       #169
      0100157DH   LINE      CODE     ---       #172
      ---         BLOCKEND  ---      ---       LVL=0

      01000012H   BLOCK     CODE     ---       LVL=0
      01000012H   LINE      CODE     ---       #181
      01000012H   LINE      CODE     ---       #184
      ---         BLOCKEND  ---      ---       LVL=0

      010020A4H   BLOCK     CODE     ---       LVL=0
      010020A4H   LINE      CODE     ---       #193
      010020AAH   LINE      CODE     ---       #196
      010020ADH   LINE      CODE     ---       #197
      010020B0H   LINE      CODE     ---       #200
      010020B3H   LINE      CODE     ---       #201
      010020B3H   LINE      CODE     ---       #202
      010020C1H   LINE      CODE     ---       #203
      010020C1H   LINE      CODE     ---       #204
      ---         BLOCKEND  ---      ---       LVL=0

      01001CFDH   BLOCK     CODE     ---       LVL=0
      01001CFDH   LINE      CODE     ---       #213
      01001D1AH   LINE      CODE     ---       #215
      01001D22H   LINE      CODE     ---       #216
      01001D22H   LINE      CODE     ---       #217
      01001D24H   LINE      CODE     ---       #218
      01001D29H   LINE      CODE     ---       #219
      01001D38H   LINE      CODE     ---       #220
      01001D3DH   LINE      CODE     ---       #221
      01001D3DH   LINE      CODE     ---       #222
      ---         BLOCKEND  ---      ---       LVL=0

      0100001AH   BLOCK     CODE     ---       LVL=0
      0100001AH   LINE      CODE     ---       #231
      0100001AH   LINE      CODE     ---       #234
      ---         BLOCKEND  ---      ---       LVL=0

      01000022H   BLOCK     CODE     ---       LVL=0
      01000022H   LINE      CODE     ---       #243
      01000022H   LINE      CODE     ---       #246
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 76


      ---         BLOCKEND  ---      ---       LVL=0

      01000029H   BLOCK     CODE     ---       LVL=0
      01000029H   LINE      CODE     ---       #255
      01000029H   LINE      CODE     ---       #258
      ---         BLOCKEND  ---      ---       LVL=0

      01001A09H   BLOCK     CODE     ---       LVL=0
      01001A09H   LINE      CODE     ---       #267
      01001A18H   LINE      CODE     ---       #270
      01001A1BH   LINE      CODE     ---       #273
      01001A1EH   LINE      CODE     ---       #274
      01001A1EH   LINE      CODE     ---       #276
      01001A21H   LINE      CODE     ---       #277
      01001A21H   LINE      CODE     ---       #279
      01001A30H   LINE      CODE     ---       #281
      01001A32H   LINE      CODE     ---       #283
      01001A34H   LINE      CODE     ---       #284
      01001A34H   LINE      CODE     ---       #285
      01001A36H   LINE      CODE     ---       #287
      01001A36H   LINE      CODE     ---       #289
      01001A39H   LINE      CODE     ---       #290
      01001A39H   LINE      CODE     ---       #292
      01001A54H   LINE      CODE     ---       #294
      01001A70H   LINE      CODE     ---       #295
      01001A70H   LINE      CODE     ---       #297
      01001A72H   LINE      CODE     ---       #298
      01001A72H   LINE      CODE     ---       #300
      01001A74H   LINE      CODE     ---       #301
      01001A74H   LINE      CODE     ---       #302
      01001A74H   LINE      CODE     ---       #303
      ---         BLOCKEND  ---      ---       LVL=0

      01001A81H   BLOCK     CODE     ---       LVL=0
      01001A81H   LINE      CODE     ---       #312
      01001A90H   LINE      CODE     ---       #314
      01001A93H   LINE      CODE     ---       #316
      01001A96H   LINE      CODE     ---       #317
      01001A96H   LINE      CODE     ---       #319
      01001A99H   LINE      CODE     ---       #320
      01001A99H   LINE      CODE     ---       #321
      01001AA8H   LINE      CODE     ---       #322
      01001AAAH   LINE      CODE     ---       #323
      01001AACH   LINE      CODE     ---       #324
      01001AACH   LINE      CODE     ---       #325
      01001AAEH   LINE      CODE     ---       #327
      01001AAEH   LINE      CODE     ---       #328
      01001AB1H   LINE      CODE     ---       #329
      01001AB1H   LINE      CODE     ---       #330
      01001ACCH   LINE      CODE     ---       #331
      01001AE8H   LINE      CODE     ---       #332
      01001AE8H   LINE      CODE     ---       #333
      01001AEAH   LINE      CODE     ---       #334
      01001AEAH   LINE      CODE     ---       #335
      01001AECH   LINE      CODE     ---       #336
      01001AECH   LINE      CODE     ---       #337
      01001AECH   LINE      CODE     ---       #338
      ---         BLOCKEND  ---      ---       LVL=0

      0100002AH   BLOCK     CODE     ---       LVL=0
      0100002AH   LINE      CODE     ---       #347
      0100002AH   LINE      CODE     ---       #350
      ---         BLOCKEND  ---      ---       LVL=0

      0100002EH   BLOCK     CODE     ---       LVL=0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 77


      0100002EH   LINE      CODE     ---       #359
      0100002EH   LINE      CODE     ---       #362
      ---         BLOCKEND  ---      ---       LVL=0

      0100002FH   BLOCK     CODE     ---       LVL=0
      0100002FH   LINE      CODE     ---       #371
      0100002FH   LINE      CODE     ---       #374
      ---         BLOCKEND  ---      ---       LVL=0

      01000030H   BLOCK     CODE     ---       LVL=0
      01000030H   LINE      CODE     ---       #383
      01000030H   LINE      CODE     ---       #386
      ---         BLOCKEND  ---      ---       LVL=0

      01000031H   BLOCK     CODE     ---       LVL=0
      01000031H   LINE      CODE     ---       #395
      01000031H   LINE      CODE     ---       #398
      ---         BLOCKEND  ---      ---       LVL=0

      01000032H   BLOCK     CODE     ---       LVL=0
      01000032H   LINE      CODE     ---       #407
      01000032H   LINE      CODE     ---       #410
      ---         BLOCKEND  ---      ---       LVL=0

      01000036H   BLOCK     CODE     ---       LVL=0
      01000036H   LINE      CODE     ---       #419
      01000036H   LINE      CODE     ---       #422
      ---         BLOCKEND  ---      ---       LVL=0

      01000037H   BLOCK     CODE     ---       LVL=0
      01000037H   LINE      CODE     ---       #431
      01000037H   LINE      CODE     ---       #434
      ---         BLOCKEND  ---      ---       LVL=0

      01000038H   BLOCK     CODE     ---       LVL=0
      01000038H   LINE      CODE     ---       #443
      01000038H   LINE      CODE     ---       #446
      ---         BLOCKEND  ---      ---       LVL=0

      01000039H   BLOCK     CODE     ---       LVL=0
      01000039H   LINE      CODE     ---       #455
      01000039H   LINE      CODE     ---       #458
      ---         BLOCKEND  ---      ---       LVL=0

      0100003AH   BLOCK     CODE     ---       LVL=0
      0100003AH   LINE      CODE     ---       #467
      0100003AH   LINE      CODE     ---       #470
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       UART_FUNCTION
      02000077H   PUBLIC    XDATA    BYTE      Data_Length
      02000057H   PUBLIC    XDATA    ---       UART_Get_String
      0100158AH   PUBLIC    CODE     ---       UART_Data_Process
      01001AF9H   PUBLIC    CODE     ---       _Function_UART_Send_CMD
      010020E8H   PUBLIC    CODE     ---       UART_Data_Init
      01002280H   PUBLIC    CODE     ---       Clean_UART_Data_Length
      0100227AH   PUBLIC    CODE     ---       Return_UART_Data_Length
      010020C8H   PUBLIC    CODE     ---       _UART_Data_Copy
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 78


      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 79


      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 80


      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      01002286H   SYMBOL    CONST    ---       _?ix1000
      01002289H   SYMBOL    CONST    ---       _?ix1001
      0100228CH   SYMBOL    CONST    ---       _?ix1002

      010020C8H   BLOCK     CODE     ---       LVL=0
      00000001H   SYMBOL    DATA     ---       Data_Point
      00000005H   SYMBOL    DATA     BYTE      Source_Data
      010020C8H   LINE      CODE     ---       #12
      010020C8H   LINE      CODE     ---       #13
      010020C8H   LINE      CODE     ---       #14
      010020D5H   LINE      CODE     ---       #15
      010020DBH   LINE      CODE     ---       #16
      010020E5H   LINE      CODE     ---       #17
      010020E5H   LINE      CODE     ---       #18
      010020E7H   LINE      CODE     ---       #19
      010020E7H   LINE      CODE     ---       #20
      ---         BLOCKEND  ---      ---       LVL=0

      0100227AH   BLOCK     CODE     ---       LVL=0
      0100227AH   LINE      CODE     ---       #24
      0100227AH   LINE      CODE     ---       #25
      0100227AH   LINE      CODE     ---       #26
      0100227FH   LINE      CODE     ---       #27
      ---         BLOCKEND  ---      ---       LVL=0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 81



      01002280H   BLOCK     CODE     ---       LVL=0
      01002280H   LINE      CODE     ---       #30
      01002280H   LINE      CODE     ---       #31
      01002280H   LINE      CODE     ---       #32
      01002285H   LINE      CODE     ---       #33
      ---         BLOCKEND  ---      ---       LVL=0

      010020E8H   BLOCK     CODE     ---       LVL=0
      010020E8H   BLOCK     CODE     NEAR LAB  LVL=1
      00000007H   SYMBOL    DATA     BYTE      i
      ---         BLOCKEND  ---      ---       LVL=1
      010020E8H   LINE      CODE     ---       #36
      010020E8H   LINE      CODE     ---       #37
      010020E8H   LINE      CODE     ---       #39
      010020EDH   LINE      CODE     ---       #40
      010020F8H   LINE      CODE     ---       #41
      010020F8H   LINE      CODE     ---       #42
      01002104H   LINE      CODE     ---       #43
      01002107H   LINE      CODE     ---       #44
      ---         BLOCKEND  ---      ---       LVL=0

      01001AF9H   BLOCK     CODE     ---       LVL=0
      02000044H   SYMBOL    XDATA    BYTE      CMD_No
      01001AFEH   BLOCK     CODE     NEAR LAB  LVL=1
      02000045H   SYMBOL    XDATA    ---       UART_PASS_Data
      02000048H   SYMBOL    XDATA    ---       UART_Error_Data
      0200004BH   SYMBOL    XDATA    ---       UART_Clean_Pair
      ---         BLOCKEND  ---      ---       LVL=1
      01001AF9H   LINE      CODE     ---       #61
      01001AFEH   LINE      CODE     ---       #62
      01001AFEH   LINE      CODE     ---       #63
      01001B11H   LINE      CODE     ---       #64
      01001B24H   LINE      CODE     ---       #65
      01001B37H   LINE      CODE     ---       #66
      01001B45H   LINE      CODE     ---       #67
      01001B45H   LINE      CODE     ---       #69
      01001B45H   LINE      CODE     ---       #70
      01001B45H   LINE      CODE     ---       #71
      01001B4BH   LINE      CODE     ---       #72
      01001B4BH   LINE      CODE     ---       #73
      01001B4DH   LINE      CODE     ---       #75
      01001B4DH   LINE      CODE     ---       #76
      01001B4DH   LINE      CODE     ---       #77
      01001B58H   LINE      CODE     ---       #78
      01001B58H   LINE      CODE     ---       #79
      01001B5AH   LINE      CODE     ---       #81
      01001B5AH   LINE      CODE     ---       #82
      01001B5AH   LINE      CODE     ---       #83
      01001B6BH   LINE      CODE     ---       #86
      01001B6BH   LINE      CODE     ---       #87
      01001B6BH   LINE      CODE     ---       #88
      01001B6BH   LINE      CODE     ---       #89
      ---         BLOCKEND  ---      ---       LVL=0

      0100158AH   BLOCK     CODE     ---       LVL=0
      0100158AH   BLOCK     CODE     NEAR LAB  LVL=1
      00000007H   SYMBOL    DATA     BYTE      i
      00000006H   SYMBOL    DATA     BYTE      Return_Data
      ---         BLOCKEND  ---      ---       LVL=1
      0100158AH   LINE      CODE     ---       #92
      0100158AH   LINE      CODE     ---       #93
      0100158AH   LINE      CODE     ---       #96
      0100158DH   LINE      CODE     ---       #98
      01001592H   LINE      CODE     ---       #99
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 82


      01001592H   LINE      CODE     ---       #100
      01001595H   LINE      CODE     ---       #101
      01001598H   LINE      CODE     ---       #102
      01001598H   LINE      CODE     ---       #104
      010015A3H   LINE      CODE     ---       #105
      010015A3H   LINE      CODE     ---       #106
      010015B2H   LINE      CODE     ---       #107
      010015B2H   LINE      CODE     ---       #109
      010015B4H   LINE      CODE     ---       #110
      010015B7H   LINE      CODE     ---       #111
      010015C5H   LINE      CODE     ---       #112
      010015C5H   LINE      CODE     ---       #114
      010015C7H   LINE      CODE     ---       #115
      010015CAH   LINE      CODE     ---       #116
      010015D8H   LINE      CODE     ---       #117
      010015D8H   LINE      CODE     ---       #119
      010015DAH   LINE      CODE     ---       #120
      010015DDH   LINE      CODE     ---       #121
      010015EEH   LINE      CODE     ---       #122
      010015EEH   LINE      CODE     ---       #124
      010015F0H   LINE      CODE     ---       #125
      010015F3H   LINE      CODE     ---       #126
      01001601H   LINE      CODE     ---       #127
      01001601H   LINE      CODE     ---       #129
      01001603H   LINE      CODE     ---       #130
      01001606H   LINE      CODE     ---       #131
      01001614H   LINE      CODE     ---       #132
      01001614H   LINE      CODE     ---       #134
      01001616H   LINE      CODE     ---       #135
      01001619H   LINE      CODE     ---       #136
      0100162AH   LINE      CODE     ---       #137
      0100162AH   LINE      CODE     ---       #139
      0100162CH   LINE      CODE     ---       #140
      0100162EH   LINE      CODE     ---       #141
      0100163CH   LINE      CODE     ---       #142
      0100163CH   LINE      CODE     ---       #144
      0100163EH   LINE      CODE     ---       #145
      01001640H   LINE      CODE     ---       #146
      0100164EH   LINE      CODE     ---       #147
      0100164EH   LINE      CODE     ---       #149
      01001650H   LINE      CODE     ---       #150
      01001652H   LINE      CODE     ---       #151
      01001663H   LINE      CODE     ---       #152
      01001663H   LINE      CODE     ---       #154
      01001665H   LINE      CODE     ---       #155
      01001667H   LINE      CODE     ---       #156
      01001675H   LINE      CODE     ---       #157
      01001675H   LINE      CODE     ---       #159
      01001677H   LINE      CODE     ---       #160
      01001679H   LINE      CODE     ---       #161
      01001687H   LINE      CODE     ---       #162
      01001687H   LINE      CODE     ---       #164
      01001689H   LINE      CODE     ---       #165
      0100168BH   LINE      CODE     ---       #166
      01001697H   LINE      CODE     ---       #167
      01001697H   LINE      CODE     ---       #169
      01001699H   LINE      CODE     ---       #170
      0100169BH   LINE      CODE     ---       #172
      0100169BH   LINE      CODE     ---       #174
      0100169DH   LINE      CODE     ---       #175
      0100169DH   LINE      CODE     ---       #176
      0100169DH   LINE      CODE     ---       #178
      010016A0H   LINE      CODE     ---       #180
      010016A2H   LINE      CODE     ---       #181
      ---         BLOCKEND  ---      ---       LVL=0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 83



      ---         MODULE    ---      ---       BATTERY_FUNCTION

      ---         MODULE    ---      ---       KEY
      00000025H.1 PUBLIC    BIT      BIT       K5_Press
      00000025H.0 PUBLIC    BIT      BIT       K4_Press
      00000024H.7 PUBLIC    BIT      BIT       K3_Press
      00000024H.6 PUBLIC    BIT      BIT       K2_Press
      00000024H.5 PUBLIC    BIT      BIT       K1_Press
      0200009DH   PUBLIC    XDATA    BYTE      K5_Count
      0200009CH   PUBLIC    XDATA    BYTE      K4_Count
      0200009BH   PUBLIC    XDATA    BYTE      K3_Count
      0200009AH   PUBLIC    XDATA    BYTE      K2_Count
      02000099H   PUBLIC    XDATA    BYTE      K1_Count
      02000098H   PUBLIC    XDATA    BYTE      Key_Buff
      01001EB9H   PUBLIC    CODE     ---       Key_Buff_Return
      01001345H   PUBLIC    CODE     ---       Key_Scan
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 84


      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 85


      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 86



      01001345H   BLOCK     CODE     ---       LVL=0
      01001345H   LINE      CODE     ---       #20
      01001345H   LINE      CODE     ---       #21
      01001345H   LINE      CODE     ---       #23
      01001348H   LINE      CODE     ---       #24
      01001348H   LINE      CODE     ---       #25
      0100134BH   LINE      CODE     ---       #26
      0100134BH   LINE      CODE     ---       #27
      01001358H   LINE      CODE     ---       #28
      01001358H   LINE      CODE     ---       #29
      0100135AH   LINE      CODE     ---       #30
      0100135CH   LINE      CODE     ---       #31
      0100135EH   LINE      CODE     ---       #43
      0100135EH   LINE      CODE     ---       #44
      01001361H   LINE      CODE     ---       #45
      01001361H   LINE      CODE     ---       #46
      0100136EH   LINE      CODE     ---       #47
      0100136EH   LINE      CODE     ---       #48
      01001370H   LINE      CODE     ---       #49
      01001372H   LINE      CODE     ---       #50
      01001374H   LINE      CODE     ---       #52
      01001374H   LINE      CODE     ---       #53
      0100137AH   LINE      CODE     ---       #54
      0100137AH   LINE      CODE     ---       #55
      0100137CH   LINE      CODE     ---       #57
      0100137CH   LINE      CODE     ---       #58
      01001381H   LINE      CODE     ---       #59
      01001381H   LINE      CODE     ---       #60
      01001381H   LINE      CODE     ---       #63
      01001384H   LINE      CODE     ---       #64
      01001384H   LINE      CODE     ---       #65
      01001387H   LINE      CODE     ---       #66
      01001387H   LINE      CODE     ---       #67
      01001394H   LINE      CODE     ---       #68
      01001394H   LINE      CODE     ---       #69
      01001396H   LINE      CODE     ---       #70
      01001398H   LINE      CODE     ---       #71
      0100139AH   LINE      CODE     ---       #83
      0100139AH   LINE      CODE     ---       #84
      0100139DH   LINE      CODE     ---       #85
      0100139DH   LINE      CODE     ---       #86
      010013AAH   LINE      CODE     ---       #87
      010013AAH   LINE      CODE     ---       #88
      010013ACH   LINE      CODE     ---       #89
      010013AEH   LINE      CODE     ---       #90
      010013B0H   LINE      CODE     ---       #92
      010013B0H   LINE      CODE     ---       #93
      010013B6H   LINE      CODE     ---       #94
      010013B6H   LINE      CODE     ---       #95
      010013B8H   LINE      CODE     ---       #97
      010013B8H   LINE      CODE     ---       #98
      010013BDH   LINE      CODE     ---       #99
      010013BDH   LINE      CODE     ---       #100
      010013BDH   LINE      CODE     ---       #103
      010013C0H   LINE      CODE     ---       #104
      010013C0H   LINE      CODE     ---       #105
      010013C3H   LINE      CODE     ---       #106
      010013C3H   LINE      CODE     ---       #107
      010013D0H   LINE      CODE     ---       #108
      010013D0H   LINE      CODE     ---       #109
      010013D2H   LINE      CODE     ---       #110
      010013D4H   LINE      CODE     ---       #111
      010013D6H   LINE      CODE     ---       #123
      010013D6H   LINE      CODE     ---       #124
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 87


      010013D9H   LINE      CODE     ---       #125
      010013D9H   LINE      CODE     ---       #126
      010013E6H   LINE      CODE     ---       #127
      010013E6H   LINE      CODE     ---       #128
      010013E8H   LINE      CODE     ---       #129
      010013EAH   LINE      CODE     ---       #130
      010013ECH   LINE      CODE     ---       #132
      010013ECH   LINE      CODE     ---       #133
      010013F2H   LINE      CODE     ---       #134
      010013F2H   LINE      CODE     ---       #135
      010013F4H   LINE      CODE     ---       #137
      010013F4H   LINE      CODE     ---       #138
      010013F9H   LINE      CODE     ---       #139
      010013F9H   LINE      CODE     ---       #140
      010013F9H   LINE      CODE     ---       #143
      010013FCH   LINE      CODE     ---       #144
      010013FCH   LINE      CODE     ---       #145
      010013FFH   LINE      CODE     ---       #146
      010013FFH   LINE      CODE     ---       #147
      0100140CH   LINE      CODE     ---       #148
      0100140CH   LINE      CODE     ---       #149
      0100140EH   LINE      CODE     ---       #150
      01001410H   LINE      CODE     ---       #151
      01001412H   LINE      CODE     ---       #163
      01001412H   LINE      CODE     ---       #164
      01001415H   LINE      CODE     ---       #165
      01001415H   LINE      CODE     ---       #166
      01001422H   LINE      CODE     ---       #167
      01001422H   LINE      CODE     ---       #168
      01001424H   LINE      CODE     ---       #169
      01001426H   LINE      CODE     ---       #170
      01001428H   LINE      CODE     ---       #172
      01001428H   LINE      CODE     ---       #173
      0100142EH   LINE      CODE     ---       #174
      0100142EH   LINE      CODE     ---       #175
      01001430H   LINE      CODE     ---       #177
      01001430H   LINE      CODE     ---       #178
      01001435H   LINE      CODE     ---       #179
      01001435H   LINE      CODE     ---       #180
      01001435H   LINE      CODE     ---       #183
      01001438H   LINE      CODE     ---       #184
      01001438H   LINE      CODE     ---       #185
      0100143BH   LINE      CODE     ---       #186
      0100143BH   LINE      CODE     ---       #187
      01001448H   LINE      CODE     ---       #188
      01001448H   LINE      CODE     ---       #189
      0100144AH   LINE      CODE     ---       #190
      0100144CH   LINE      CODE     ---       #191
      0100144DH   LINE      CODE     ---       #203
      0100144DH   LINE      CODE     ---       #204
      01001450H   LINE      CODE     ---       #205
      01001450H   LINE      CODE     ---       #206
      0100145DH   LINE      CODE     ---       #207
      0100145DH   LINE      CODE     ---       #208
      0100145FH   LINE      CODE     ---       #209
      01001461H   LINE      CODE     ---       #210
      01001462H   LINE      CODE     ---       #212
      01001462H   LINE      CODE     ---       #213
      01001468H   LINE      CODE     ---       #214
      01001468H   LINE      CODE     ---       #215
      01001469H   LINE      CODE     ---       #217
      01001469H   LINE      CODE     ---       #218
      0100146EH   LINE      CODE     ---       #219
      0100146EH   LINE      CODE     ---       #220
      0100146EH   LINE      CODE     ---       #221
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 88


      ---         BLOCKEND  ---      ---       LVL=0

      01001EB9H   BLOCK     CODE     ---       LVL=0
      01001EB9H   LINE      CODE     ---       #224
      01001EB9H   LINE      CODE     ---       #225
      01001EB9H   LINE      CODE     ---       #226
      01001EBEH   LINE      CODE     ---       #228
      01001EC5H   LINE      CODE     ---       #229
      01001ECFH   LINE      CODE     ---       #230
      01001ED9H   LINE      CODE     ---       #231
      01001EE3H   LINE      CODE     ---       #232
      01001EEDH   LINE      CODE     ---       #234
      01001EF2H   LINE      CODE     ---       #235
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ADC_USED
      01000026H   PUBLIC    CODE     ---       ADC_GetResult
      0100003EH   PUBLIC    CODE     ---       ADC_ClearConvertIntFlag
      01000086H   PUBLIC    CODE     ---       ADC_GetConvertIntFlag
      01002269H   PUBLIC    CODE     ---       _ADC_StartConvert
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 89


      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 90


      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 91


      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      ADC_Channel
      0200008DH   SYMBOL    XDATA    ---       filter_buffer
      02000097H   SYMBOL    XDATA    BYTE      filter_index

      01002269H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADC_Channel
      01002269H   LINE      CODE     ---       #43
      01002269H   LINE      CODE     ---       #44
      01002269H   LINE      CODE     ---       #45
      0100226BH   LINE      CODE     ---       #46
      0100226EH   LINE      CODE     ---       #47
      01002271H   LINE      CODE     ---       #48
      ---         BLOCKEND  ---      ---       LVL=0

      01000086H   BLOCK     CODE     ---       LVL=0
      01000086H   LINE      CODE     ---       #50
      01000086H   LINE      CODE     ---       #51
      01000086H   LINE      CODE     ---       #52
      0100008FH   LINE      CODE     ---       #53
      ---         BLOCKEND  ---      ---       LVL=0

      0100003EH   BLOCK     CODE     ---       LVL=0
      0100003EH   LINE      CODE     ---       #55
      0100003EH   LINE      CODE     ---       #56
      0100003EH   LINE      CODE     ---       #58
      ---         BLOCKEND  ---      ---       LVL=0

      01000026H   BLOCK     CODE     ---       LVL=0
      01000026H   LINE      CODE     ---       #60
      01000026H   LINE      CODE     ---       #61
      01000026H   LINE      CODE     ---       #62
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       MAIN
      00000024H.4 PUBLIC    BIT      BIT       speedup
      00000024H.3 PUBLIC    BIT      BIT       longhit
      02000032H   PUBLIC    XDATA    BYTE      ledonoff_cnt
      00000024H.2 PUBLIC    BIT      BIT       key_short_press_mode
      00000024H.1 PUBLIC    BIT      BIT       direction_changed
      00000024H.0 PUBLIC    BIT      BIT       Bit_N_ms_Buff
      02000030H   PUBLIC    XDATA    WORD      precise_k2_timer
      0200002EH   PUBLIC    XDATA    WORD      BatV
      00000023H.7 PUBLIC    BIT      BIT       key3_long_started
      00000023H.6 PUBLIC    BIT      BIT       key1_long_started
      00000023H.5 PUBLIC    BIT      BIT       k2_long_press_detected
      00000023H.4 PUBLIC    BIT      BIT       Bit_Toggle
      00000023H.3 PUBLIC    BIT      BIT       Delay_Open
      0200002DH   PUBLIC    XDATA    BYTE      batlow1_cnt
      00000023H.2 PUBLIC    BIT      BIT       charge_flash
      0200002CH   PUBLIC    XDATA    BYTE      last_direction
      00000023H.1 PUBLIC    BIT      BIT       key3_pressed
      00000023H.0 PUBLIC    BIT      BIT       Bit_1_ms_Buff
      00000022H.7 PUBLIC    BIT      BIT       key1_pressed
      0200002BH   PUBLIC    XDATA    BYTE      System_Mode_Before_Charge
      00000022H.6 PUBLIC    BIT      BIT       Charge_Was_Connected
      00000022H.5 PUBLIC    BIT      BIT       ledonoff
      02000029H   PUBLIC    XDATA    WORD      original_speed
      00000022H.4 PUBLIC    BIT      BIT       key3_handle
      02000028H   PUBLIC    XDATA    BYTE      System_Mode_Data
      02000026H   PUBLIC    XDATA    INT       Self_Check
      00000022H.3 PUBLIC    BIT      BIT       key1_handle
      02000024H   PUBLIC    XDATA    WORD      key3_duration
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 92


      02000022H   PUBLIC    XDATA    WORD      dly
      00000022H.2 PUBLIC    BIT      BIT       k3_released
      02000020H   PUBLIC    XDATA    WORD      key1_duration
      00000022H.1 PUBLIC    BIT      BIT       k2_released
      0200001EH   PUBLIC    XDATA    WORD      k2_long_press_timer
      00000022H.0 PUBLIC    BIT      BIT       batlow1
      0200001CH   PUBLIC    XDATA    INT       Count_1_Degree_Pulse
      00000021H.7 PUBLIC    BIT      BIT       auto_rotate_mode
      0200001BH   PUBLIC    XDATA    BYTE      batlow_cnt
      00000021H.6 PUBLIC    BIT      BIT       Charg_State_Buff
      0200001AH   PUBLIC    XDATA    BYTE      battery_check_divider
      00000021H.5 PUBLIC    BIT      BIT       led_flash_state
      02000018H   PUBLIC    XDATA    WORD      led_flash_timer
      02000017H   PUBLIC    XDATA    BYTE      ledonoff1_cnt
      02000016H   PUBLIC    XDATA    BYTE      main_loop_counter
      00000021H.4 PUBLIC    BIT      BIT       need_led_flash
      02000014H   PUBLIC    XDATA    WORD      auto_rotate_flash_timer
      02000012H   PUBLIC    XDATA    WORD      timer_1ms_count
      00000021H.3 PUBLIC    BIT      BIT       use_precise_timer
      02000010H   PUBLIC    XDATA    WORD      speedup_cnt
      0200000EH   PUBLIC    XDATA    WORD      key3_press_time
      00000021H.2 PUBLIC    BIT      BIT       auto_rotate_flash
      0200000CH   PUBLIC    XDATA    WORD      key1_press_time
      0200000BH   PUBLIC    XDATA    BYTE      key_scan_divider
      00000021H.1 PUBLIC    BIT      BIT       K3_cnt_EN
      02000007H   PUBLIC    XDATA    DWORD     Systemclock
      00000021H.0 PUBLIC    BIT      BIT       K2_cnt_EN
      00000020H.7 PUBLIC    BIT      BIT       K1_cnt_EN
      00000020H.6 PUBLIC    BIT      BIT       auto_rotate_running
      00000020H.5 PUBLIC    BIT      BIT       MOTOR_RUNNING_FLAG
      00000020H.4 PUBLIC    BIT      BIT       key_control_active
      00000020H.3 PUBLIC    BIT      BIT       Key_Long_Press
      00000020H.2 PUBLIC    BIT      BIT       batlow
      02000005H   PUBLIC    XDATA    WORD      charge_flash_cnt
      00000020H.1 PUBLIC    BIT      BIT       auto_rotate_entry_complete
      00000020H.0 PUBLIC    BIT      BIT       ledonoff1
      010011DAH   PUBLIC    CODE     ---       Key_Interrupt_Process
      010016A3H   PUBLIC    CODE     ---       LED_Control
      01002034H   PUBLIC    CODE     ---       Restore_dly
      01001F8DH   PUBLIC    CODE     ---       _Store_dly
      010018A4H   PUBLIC    CODE     ---       Battery_Check
      01001E7CH   PUBLIC    CODE     ---       _Key_Function_Switch_System
      01001BDCH   PUBLIC    CODE     ---       _Motor_Step_Control
      01002108H   PUBLIC    CODE     ---       _Delay1ms
      010000B6H   PUBLIC    CODE     ---       main
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 93


      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 94


      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 95


      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      010000B6H   BLOCK     CODE     ---       LVL=0
      010000B6H   BLOCK     CODE     NEAR LAB  LVL=1
      00000025H.6 SYMBOL    BIT      BIT       Delay_Open_Buff
      02000033H   SYMBOL    XDATA    INT       Key_Input
      02000035H   SYMBOL    XDATA    INT       Charge_Input
      02000037H   SYMBOL    XDATA    INT       Key_State
      02000039H   SYMBOL    XDATA    INT       Key_State_Save
      0200003BH   SYMBOL    XDATA    INT       Charge_State_Save
      0200003DH   SYMBOL    XDATA    INT       Key_Keep_Time_For_System_Open
      00000025H.7 SYMBOL    BIT      BIT       Long_Press_To_Open
      00000026H.0 SYMBOL    BIT      BIT       Blue_Teeth_Long_Press
      0200003FH   SYMBOL    XDATA    INT       Charge_Keep_Time_For_System_Open
      02000041H   SYMBOL    XDATA    BYTE      UART_Get_CMD
      00000026H.1 SYMBOL    BIT      BIT       Voltage_Low
      02000042H   SYMBOL    XDATA    WORD      k2k3_press_time
      ---         BLOCKEND  ---      ---       LVL=1
      010000B6H   LINE      CODE     ---       #143
      010000B6H   LINE      CODE     ---       #144
      010000B6H   LINE      CODE     ---       #150
      010000B8H   LINE      CODE     ---       #153
      010000BAH   LINE      CODE     ---       #154
      010000C1H   LINE      CODE     ---       #157
      010000C4H   LINE      CODE     ---       #158
      010000CBH   LINE      CODE     ---       #160
      010000CEH   LINE      CODE     ---       #162
      010000D1H   LINE      CODE     ---       #163
      010000D4H   LINE      CODE     ---       #165
      010000D7H   LINE      CODE     ---       #166
      010000DAH   LINE      CODE     ---       #170
      010000DCH   LINE      CODE     ---       #171
      010000E3H   LINE      CODE     ---       #172
      010000E7H   LINE      CODE     ---       #173
      010000E9H   LINE      CODE     ---       #174
      010000EFH   LINE      CODE     ---       #175
      010000F5H   LINE      CODE     ---       #176
      010000FBH   LINE      CODE     ---       #177
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 96


      010000FDH   LINE      CODE     ---       #178
      01000108H   LINE      CODE     ---       #179
      0100011CH   LINE      CODE     ---       #180
      0100011CH   LINE      CODE     ---       #181
      01000125H   LINE      CODE     ---       #182
      0100012BH   LINE      CODE     ---       #183
      0100012BH   LINE      CODE     ---       #184
      0100012DH   LINE      CODE     ---       #187
      01000130H   LINE      CODE     ---       #189
      01000136H   LINE      CODE     ---       #190
      01000136H   LINE      CODE     ---       #191
      01000139H   LINE      CODE     ---       #192
      0100013BH   LINE      CODE     ---       #194
      0100013EH   LINE      CODE     ---       #195
      01000149H   LINE      CODE     ---       #198
      0100014FH   LINE      CODE     ---       #199
      0100014FH   LINE      CODE     ---       #200
      0100015DH   LINE      CODE     ---       #201
      0100016FH   LINE      CODE     ---       #202
      0100016FH   LINE      CODE     ---       #203
      01000173H   LINE      CODE     ---       #204
      01000175H   LINE      CODE     ---       #205
      0100017BH   LINE      CODE     ---       #206
      0100017DH   LINE      CODE     ---       #207
      0100017FH   LINE      CODE     ---       #208
      01000181H   LINE      CODE     ---       #209
      01000188H   LINE      CODE     ---       #210
      0100018EH   LINE      CODE     ---       #211
      01000190H   LINE      CODE     ---       #212
      01000192H   LINE      CODE     ---       #213
      0100019AH   LINE      CODE     ---       #214
      0100019CH   LINE      CODE     ---       #215
      0100019EH   LINE      CODE     ---       #216
      010001A0H   LINE      CODE     ---       #217
      010001A0H   LINE      CODE     ---       #218
      010001A3H   LINE      CODE     ---       #220
      010001A3H   LINE      CODE     ---       #221
      010001AAH   LINE      CODE     ---       #223
      010001C6H   LINE      CODE     ---       #224
      010001C6H   LINE      CODE     ---       #225
      010001CCH   LINE      CODE     ---       #226
      010001CCH   LINE      CODE     ---       #227
      010001DAH   LINE      CODE     ---       #228
      010001EBH   LINE      CODE     ---       #229
      010001EBH   LINE      CODE     ---       #230
      010001EFH   LINE      CODE     ---       #231
      010001F1H   LINE      CODE     ---       #232
      010001F3H   LINE      CODE     ---       #233
      010001F8H   LINE      CODE     ---       #234
      010001F8H   LINE      CODE     ---       #235
      010001FAH   LINE      CODE     ---       #236
      01000205H   LINE      CODE     ---       #237
      01000205H   LINE      CODE     ---       #238
      01000213H   LINE      CODE     ---       #239
      01000224H   LINE      CODE     ---       #240
      01000224H   LINE      CODE     ---       #241
      01000228H   LINE      CODE     ---       #242
      0100022AH   LINE      CODE     ---       #243
      0100022EH   LINE      CODE     ---       #244
      01000230H   LINE      CODE     ---       #245
      01000236H   LINE      CODE     ---       #246
      01000236H   LINE      CODE     ---       #247
      01000238H   LINE      CODE     ---       #248
      01000242H   LINE      CODE     ---       #249
      01000242H   LINE      CODE     ---       #250
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 97


      01000248H   LINE      CODE     ---       #251
      0100024AH   LINE      CODE     ---       #252
      0100024EH   LINE      CODE     ---       #253
      01000254H   LINE      CODE     ---       #254
      01000256H   LINE      CODE     ---       #255
      01000256H   LINE      CODE     ---       #256
      01000258H   LINE      CODE     ---       #258
      01000262H   LINE      CODE     ---       #259
      01000262H   LINE      CODE     ---       #260
      01000267H   LINE      CODE     ---       #261
      01000267H   LINE      CODE     ---       #262
      01000267H   LINE      CODE     ---       #264
      01000276H   LINE      CODE     ---       #265
      0100028EH   LINE      CODE     ---       #266
      01000291H   LINE      CODE     ---       #268
      01000293H   LINE      CODE     ---       #269
      01000299H   LINE      CODE     ---       #270
      010002A1H   LINE      CODE     ---       #271
      010002A4H   LINE      CODE     ---       #274
      010002ADH   LINE      CODE     ---       #275
      010002ADH   LINE      CODE     ---       #277
      010002B3H   LINE      CODE     ---       #278
      010002B3H   LINE      CODE     ---       #280
      010002B3H   LINE      CODE     ---       #282
      010002B3H   LINE      CODE     ---       #284
      010002B3H   LINE      CODE     ---       #285
      010002B3H   LINE      CODE     ---       #286
      010002B6H   LINE      CODE     ---       #287
      010002B8H   LINE      CODE     ---       #289
      010002BEH   LINE      CODE     ---       #292
      010002CDH   LINE      CODE     ---       #293
      010002CDH   LINE      CODE     ---       #294
      010002CFH   LINE      CODE     ---       #295
      010002D2H   LINE      CODE     ---       #296
      010002D2H   LINE      CODE     ---       #297
      010002DDH   LINE      CODE     ---       #299
      010002EEH   LINE      CODE     ---       #300
      01000306H   LINE      CODE     ---       #303
      01000315H   LINE      CODE     ---       #304
      01000315H   LINE      CODE     ---       #305
      01000317H   LINE      CODE     ---       #306
      0100031AH   LINE      CODE     ---       #307
      0100031AH   LINE      CODE     ---       #310
      0100031DH   LINE      CODE     ---       #313
      0100032AH   LINE      CODE     ---       #314
      0100032AH   LINE      CODE     ---       #316
      0100032DH   LINE      CODE     ---       #317
      0100032DH   LINE      CODE     ---       #319
      01000330H   LINE      CODE     ---       #320
      01000330H   LINE      CODE     ---       #321
      01000338H   LINE      CODE     ---       #322
      0100033AH   LINE      CODE     ---       #323
      0100033AH   LINE      CODE     ---       #324
      0100033AH   LINE      CODE     ---       #325
      0100033CH   LINE      CODE     ---       #326
      0100033EH   LINE      CODE     ---       #327
      01000344H   LINE      CODE     ---       #328
      01000344H   LINE      CODE     ---       #330
      01000347H   LINE      CODE     ---       #331
      01000347H   LINE      CODE     ---       #333
      0100034AH   LINE      CODE     ---       #334
      0100034AH   LINE      CODE     ---       #335
      01000352H   LINE      CODE     ---       #336
      01000354H   LINE      CODE     ---       #337
      01000354H   LINE      CODE     ---       #338
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 98


      01000354H   LINE      CODE     ---       #339
      01000356H   LINE      CODE     ---       #340
      01000358H   LINE      CODE     ---       #342
      01000358H   LINE      CODE     ---       #344
      0100035EH   LINE      CODE     ---       #345
      0100035EH   LINE      CODE     ---       #347
      01000366H   LINE      CODE     ---       #348
      01000368H   LINE      CODE     ---       #352
      01000368H   LINE      CODE     ---       #353
      0100036AH   LINE      CODE     ---       #354
      0100036AH   LINE      CODE     ---       #357
      01000375H   LINE      CODE     ---       #358
      01000375H   LINE      CODE     ---       #360
      01000378H   LINE      CODE     ---       #361
      01000378H   LINE      CODE     ---       #363
      0100037BH   LINE      CODE     ---       #364
      0100037BH   LINE      CODE     ---       #365
      0100037DH   LINE      CODE     ---       #366
      0100037DH   LINE      CODE     ---       #368
      01000380H   LINE      CODE     ---       #369
      01000380H   LINE      CODE     ---       #370
      01000382H   LINE      CODE     ---       #371
      01000382H   LINE      CODE     ---       #374
      0100038BH   LINE      CODE     ---       #375
      0100038BH   LINE      CODE     ---       #376
      0100038DH   LINE      CODE     ---       #377
      0100038FH   LINE      CODE     ---       #378
      01000391H   LINE      CODE     ---       #379
      01000399H   LINE      CODE     ---       #380
      0100039BH   LINE      CODE     ---       #381
      0100039BH   LINE      CODE     ---       #382
      0100039DH   LINE      CODE     ---       #384
      0100039DH   LINE      CODE     ---       #387
      010003A9H   LINE      CODE     ---       #388
      010003A9H   LINE      CODE     ---       #389
      010003ACH   LINE      CODE     ---       #390
      010003ACH   LINE      CODE     ---       #392
      010003AEH   LINE      CODE     ---       #393
      010003B4H   LINE      CODE     ---       #395
      010003B9H   LINE      CODE     ---       #396
      010003C2H   LINE      CODE     ---       #397
      010003C7H   LINE      CODE     ---       #398
      010003C7H   LINE      CODE     ---       #401
      010003D6H   LINE      CODE     ---       #402
      010003D6H   LINE      CODE     ---       #403
      010003D8H   LINE      CODE     ---       #404
      010003DDH   LINE      CODE     ---       #405
      010003DFH   LINE      CODE     ---       #406
      010003E1H   LINE      CODE     ---       #407
      010003E3H   LINE      CODE     ---       #408
      010003EBH   LINE      CODE     ---       #409
      010003EDH   LINE      CODE     ---       #410
      010003EFH   LINE      CODE     ---       #411
      010003F4H   LINE      CODE     ---       #412
      010003F4H   LINE      CODE     ---       #413
      010003F6H   LINE      CODE     ---       #415
      010003F6H   LINE      CODE     ---       #417
      010003F9H   LINE      CODE     ---       #418
      010003F9H   LINE      CODE     ---       #419
      010003FBH   LINE      CODE     ---       #420
      01000400H   LINE      CODE     ---       #421
      01000407H   LINE      CODE     ---       #422
      01000407H   LINE      CODE     ---       #423
      01000409H   LINE      CODE     ---       #424
      01000409H   LINE      CODE     ---       #425
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 99


      01000409H   LINE      CODE     ---       #428
      0100040FH   LINE      CODE     ---       #429
      0100040FH   LINE      CODE     ---       #430
      0100042CH   LINE      CODE     ---       #431
      0100042CH   LINE      CODE     ---       #432
      0100042FH   LINE      CODE     ---       #433
      0100042FH   LINE      CODE     ---       #435
      0100042FH   LINE      CODE     ---       #436
      0100042FH   LINE      CODE     ---       #437
      0100042FH   LINE      CODE     ---       #438
      0100042FH   LINE      CODE     ---       #439
      0100042FH   LINE      CODE     ---       #440
      0100042FH   LINE      CODE     ---       #441
      01000431H   LINE      CODE     ---       #442
      0100043CH   LINE      CODE     ---       #443
      0100043CH   LINE      CODE     ---       #445
      0100043CH   LINE      CODE     ---       #446
      0100043CH   LINE      CODE     ---       #447
      0100043CH   LINE      CODE     ---       #448
      0100043CH   LINE      CODE     ---       #450
      0100043EH   LINE      CODE     ---       #451
      01000444H   LINE      CODE     ---       #452
      01000444H   LINE      CODE     ---       #454
      01000449H   LINE      CODE     ---       #455
      01000449H   LINE      CODE     ---       #456
      01000449H   LINE      CODE     ---       #457
      01000449H   LINE      CODE     ---       #458
      01000449H   LINE      CODE     ---       #459
      01000449H   LINE      CODE     ---       #460
      01000449H   LINE      CODE     ---       #461
      0100044BH   LINE      CODE     ---       #462
      01000468H   LINE      CODE     ---       #463
      01000468H   LINE      CODE     ---       #464
      0100046BH   LINE      CODE     ---       #465
      0100046BH   LINE      CODE     ---       #467
      01000470H   LINE      CODE     ---       #468
      01000470H   LINE      CODE     ---       #469
      01000470H   LINE      CODE     ---       #470
      01000470H   LINE      CODE     ---       #471
      01000470H   LINE      CODE     ---       #472
      01000470H   LINE      CODE     ---       #473
      01000472H   LINE      CODE     ---       #474
      0100047DH   LINE      CODE     ---       #475
      0100047DH   LINE      CODE     ---       #477
      0100047FH   LINE      CODE     ---       #478
      01000481H   LINE      CODE     ---       #479
      01000489H   LINE      CODE     ---       #480
      0100048BH   LINE      CODE     ---       #481
      0100048DH   LINE      CODE     ---       #482
      01000493H   LINE      CODE     ---       #483
      01000493H   LINE      CODE     ---       #485
      01000499H   LINE      CODE     ---       #486
      010004A2H   LINE      CODE     ---       #487
      010004A4H   LINE      CODE     ---       #488
      010004A6H   LINE      CODE     ---       #489
      010004A8H   LINE      CODE     ---       #490
      010004AFH   LINE      CODE     ---       #491
      010004AFH   LINE      CODE     ---       #492
      010004AFH   LINE      CODE     ---       #493
      010004AFH   LINE      CODE     ---       #496
      010004B5H   LINE      CODE     ---       #497
      010004B5H   LINE      CODE     ---       #498
      010004B7H   LINE      CODE     ---       #499
      010004C5H   LINE      CODE     ---       #501
      010004D6H   LINE      CODE     ---       #502
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 100


      010004D6H   LINE      CODE     ---       #503
      010004DEH   LINE      CODE     ---       #504
      010004E0H   LINE      CODE     ---       #506
      010004E0H   LINE      CODE     ---       #507
      010004E7H   LINE      CODE     ---       #508
      010004E7H   LINE      CODE     ---       #509
      010004E7H   LINE      CODE     ---       #512
      010004EAH   LINE      CODE     ---       #513
      010004EAH   LINE      CODE     ---       #515
      010004F8H   LINE      CODE     ---       #516
      0100050AH   LINE      CODE     ---       #517
      0100050AH   LINE      CODE     ---       #518
      0100050EH   LINE      CODE     ---       #519
      01000510H   LINE      CODE     ---       #520
      01000510H   LINE      CODE     ---       #521
      01000513H   LINE      CODE     ---       #523
      01000513H   LINE      CODE     ---       #525
      01000515H   LINE      CODE     ---       #526
      01000519H   LINE      CODE     ---       #527
      01000519H   LINE      CODE     ---       #528
      0100051CH   LINE      CODE     ---       #531
      0100051CH   LINE      CODE     ---       #532
      0100051FH   LINE      CODE     ---       #534
      0100053FH   LINE      CODE     ---       #535
      0100053FH   LINE      CODE     ---       #536
      01000541H   LINE      CODE     ---       #537
      01000544H   LINE      CODE     ---       #538
      01000561H   LINE      CODE     ---       #539
      01000561H   LINE      CODE     ---       #540
      01000561H   LINE      CODE     ---       #541
      01000561H   LINE      CODE     ---       #542
      01000563H   LINE      CODE     ---       #543
      01000580H   LINE      CODE     ---       #544
      01000580H   LINE      CODE     ---       #545
      01000588H   LINE      CODE     ---       #546
      0100058EH   LINE      CODE     ---       #547
      01000590H   LINE      CODE     ---       #548
      0100059FH   LINE      CODE     ---       #549
      0100059FH   LINE      CODE     ---       #551
      010005A2H   LINE      CODE     ---       #552
      010005A2H   LINE      CODE     ---       #553
      010005A4H   LINE      CODE     ---       #554
      010005AAH   LINE      CODE     ---       #555
      010005AFH   LINE      CODE     ---       #556
      010005B8H   LINE      CODE     ---       #557
      010005BDH   LINE      CODE     ---       #558
      010005BDH   LINE      CODE     ---       #560
      010005CCH   LINE      CODE     ---       #561
      010005CCH   LINE      CODE     ---       #562
      010005CEH   LINE      CODE     ---       #563
      010005D3H   LINE      CODE     ---       #564
      010005DCH   LINE      CODE     ---       #565
      010005DEH   LINE      CODE     ---       #566
      010005E0H   LINE      CODE     ---       #567
      010005E5H   LINE      CODE     ---       #568
      010005E5H   LINE      CODE     ---       #569
      010005E5H   LINE      CODE     ---       #570
      010005F4H   LINE      CODE     ---       #571
      010005F4H   LINE      CODE     ---       #573
      010005F6H   LINE      CODE     ---       #574
      010005FBH   LINE      CODE     ---       #575
      01000602H   LINE      CODE     ---       #576
      01000602H   LINE      CODE     ---       #577
      01000602H   LINE      CODE     ---       #579
      01000611H   LINE      CODE     ---       #582
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 101


      0100061CH   LINE      CODE     ---       #583
      0100061CH   LINE      CODE     ---       #584
      01000622H   LINE      CODE     ---       #585
      01000622H   LINE      CODE     ---       #586
      0100062FH   LINE      CODE     ---       #587
      0100062FH   LINE      CODE     ---       #588
      01000631H   LINE      CODE     ---       #589
      01000633H   LINE      CODE     ---       #590
      0100063BH   LINE      CODE     ---       #591
      0100063DH   LINE      CODE     ---       #593
      0100063DH   LINE      CODE     ---       #594
      01000643H   LINE      CODE     ---       #595
      01000643H   LINE      CODE     ---       #597
      0100064BH   LINE      CODE     ---       #598
      0100064BH   LINE      CODE     ---       #599
      0100064CH   LINE      CODE     ---       #600
      01000650H   LINE      CODE     ---       #601
      01000653H   LINE      CODE     ---       #602
      0100065CH   LINE      CODE     ---       #603
      0100065CH   LINE      CODE     ---       #604
      01000661H   LINE      CODE     ---       #606
      010006AAH   LINE      CODE     ---       #607
      010006AAH   LINE      CODE     ---       #608
      010006AAH   LINE      CODE     ---       #609
      010006AAH   LINE      CODE     ---       #610
      010006B8H   LINE      CODE     ---       #611
      010006C5H   LINE      CODE     ---       #612
      010006C5H   LINE      CODE     ---       #613
      010006CCH   LINE      CODE     ---       #614
      010006CEH   LINE      CODE     ---       #616
      010006CEH   LINE      CODE     ---       #617
      010006D0H   LINE      CODE     ---       #618
      010006D7H   LINE      CODE     ---       #619
      010006D7H   LINE      CODE     ---       #620
      010006D7H   LINE      CODE     ---       #621
      010006D7H   LINE      CODE     ---       #622
      010006D7H   LINE      CODE     ---       #623
      010006D9H   LINE      CODE     ---       #624
      010006D9H   LINE      CODE     ---       #625
      010006E2H   LINE      CODE     ---       #626
      010006EDH   LINE      CODE     ---       #627
      010006EDH   LINE      CODE     ---       #628
      010006EFH   LINE      CODE     ---       #629
      010006EFH   LINE      CODE     ---       #630
      010006EFH   LINE      CODE     ---       #631
      010006EFH   LINE      CODE     ---       #632
      010006EFH   LINE      CODE     ---       #633
      010006EFH   LINE      CODE     ---       #634
      010006F2H   LINE      CODE     ---       #635
      010006F2H   LINE      CODE     ---       #636
      010006F2H   LINE      CODE     ---       #637
      01000700H   LINE      CODE     ---       #638
      0100070DH   LINE      CODE     ---       #639
      0100070DH   LINE      CODE     ---       #640
      01000713H   LINE      CODE     ---       #641
      01000715H   LINE      CODE     ---       #643
      01000715H   LINE      CODE     ---       #644
      01000717H   LINE      CODE     ---       #645
      0100071EH   LINE      CODE     ---       #646
      0100071EH   LINE      CODE     ---       #647
      01000729H   LINE      CODE     ---       #648
      0100072EH   LINE      CODE     ---       #649
      0100072EH   LINE      CODE     ---       #650
      01000731H   LINE      CODE     ---       #651
      01000731H   LINE      CODE     ---       #652
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 102


      0100073AH   LINE      CODE     ---       #653
      01000742H   LINE      CODE     ---       #654
      01000742H   LINE      CODE     ---       #655
      01000744H   LINE      CODE     ---       #656
      01000744H   LINE      CODE     ---       #657
      01000744H   LINE      CODE     ---       #658
      01000744H   LINE      CODE     ---       #659
      01000744H   LINE      CODE     ---       #660
      01000744H   LINE      CODE     ---       #661
      01000746H   LINE      CODE     ---       #662
      01000746H   LINE      CODE     ---       #663
      0100074FH   LINE      CODE     ---       #664
      01000757H   LINE      CODE     ---       #665
      01000757H   LINE      CODE     ---       #666
      01000759H   LINE      CODE     ---       #667
      01000759H   LINE      CODE     ---       #668
      0100075EH   LINE      CODE     ---       #669
      0100075EH   LINE      CODE     ---       #670
      0100075EH   LINE      CODE     ---       #671
      0100075EH   LINE      CODE     ---       #672
      01000760H   LINE      CODE     ---       #673
      01000760H   LINE      CODE     ---       #674
      01000769H   LINE      CODE     ---       #675
      01000771H   LINE      CODE     ---       #676
      01000771H   LINE      CODE     ---       #677
      01000773H   LINE      CODE     ---       #678
      01000773H   LINE      CODE     ---       #679
      01000779H   LINE      CODE     ---       #680
      0100077BH   LINE      CODE     ---       #681
      0100077BH   LINE      CODE     ---       #682
      0100077BH   LINE      CODE     ---       #683
      0100077EH   LINE      CODE     ---       #684
      0100077EH   LINE      CODE     ---       #685
      01000784H   LINE      CODE     ---       #686
      01000786H   LINE      CODE     ---       #687
      0100078EH   LINE      CODE     ---       #688
      01000790H   LINE      CODE     ---       #689
      01000793H   LINE      CODE     ---       #690
      01000793H   LINE      CODE     ---       #691
      01000793H   LINE      CODE     ---       #692
      0100079CH   LINE      CODE     ---       #693
      010007A4H   LINE      CODE     ---       #694
      010007A4H   LINE      CODE     ---       #695
      010007A6H   LINE      CODE     ---       #696
      010007A6H   LINE      CODE     ---       #697
      010007ACH   LINE      CODE     ---       #698
      010007AEH   LINE      CODE     ---       #699
      010007AEH   LINE      CODE     ---       #700
      010007AEH   LINE      CODE     ---       #701
      010007AEH   LINE      CODE     ---       #702
      010007B0H   LINE      CODE     ---       #703
      010007B0H   LINE      CODE     ---       #704
      010007B0H   LINE      CODE     ---       #705
      010007B9H   LINE      CODE     ---       #706
      010007C1H   LINE      CODE     ---       #707
      010007C1H   LINE      CODE     ---       #708
      010007C3H   LINE      CODE     ---       #709
      010007C3H   LINE      CODE     ---       #710
      010007C9H   LINE      CODE     ---       #711
      010007CBH   LINE      CODE     ---       #712
      010007CBH   LINE      CODE     ---       #713
      010007CBH   LINE      CODE     ---       #714
      010007CBH   LINE      CODE     ---       #715
      010007CDH   LINE      CODE     ---       #716
      010007CDH   LINE      CODE     ---       #717
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 103


      010007CDH   LINE      CODE     ---       #718
      010007D6H   LINE      CODE     ---       #719
      010007DEH   LINE      CODE     ---       #720
      010007DEH   LINE      CODE     ---       #721
      010007E0H   LINE      CODE     ---       #722
      010007E0H   LINE      CODE     ---       #723
      010007E6H   LINE      CODE     ---       #724
      010007E8H   LINE      CODE     ---       #725
      010007E8H   LINE      CODE     ---       #726
      010007E8H   LINE      CODE     ---       #727
      010007E8H   LINE      CODE     ---       #728
      010007EAH   LINE      CODE     ---       #729
      010007EAH   LINE      CODE     ---       #730
      010007EAH   LINE      CODE     ---       #731
      010007F3H   LINE      CODE     ---       #732
      010007FBH   LINE      CODE     ---       #733
      010007FBH   LINE      CODE     ---       #734
      010007FDH   LINE      CODE     ---       #735
      010007FDH   LINE      CODE     ---       #736
      01000803H   LINE      CODE     ---       #737
      01000805H   LINE      CODE     ---       #738
      01000807H   LINE      CODE     ---       #739
      0100080DH   LINE      CODE     ---       #740
      0100080DH   LINE      CODE     ---       #741
      0100080FH   LINE      CODE     ---       #742
      0100080FH   LINE      CODE     ---       #743
      0100080FH   LINE      CODE     ---       #744
      01000815H   LINE      CODE     ---       #745
      01000817H   LINE      CODE     ---       #746
      01000817H   LINE      CODE     ---       #747
      01000817H   LINE      CODE     ---       #748
      01000817H   LINE      CODE     ---       #749
      01000817H   LINE      CODE     ---       #750
      01000817H   LINE      CODE     ---       #751
      01000817H   LINE      CODE     ---       #752
      01000817H   LINE      CODE     ---       #753
      01000817H   LINE      CODE     ---       #756
      01000828H   LINE      CODE     ---       #757
      01000828H   LINE      CODE     ---       #758
      0100082AH   LINE      CODE     ---       #759
      0100082CH   LINE      CODE     ---       #760
      0100083AH   LINE      CODE     ---       #762
      01000846H   LINE      CODE     ---       #763
      01000846H   LINE      CODE     ---       #764
      01000848H   LINE      CODE     ---       #765
      0100084AH   LINE      CODE     ---       #766
      0100084CH   LINE      CODE     ---       #767
      0100084CH   LINE      CODE     ---       #768
      01000852H   LINE      CODE     ---       #769
      01000852H   LINE      CODE     ---       #770
      01000855H   LINE      CODE     ---       #771
      0100085CH   LINE      CODE     ---       #772
      0100085EH   LINE      CODE     ---       #773
      0100085EH   LINE      CODE     ---       #774
      01000861H   LINE      CODE     ---       #775
      01000861H   LINE      CODE     ---       #776
      01000869H   LINE      CODE     ---       #777
      01000869H   LINE      CODE     ---       #778
      0100086CH   LINE      CODE     ---       #779
      0100086CH   LINE      CODE     ---       #781
      0100086FH   LINE      CODE     ---       #782
      0100086FH   LINE      CODE     ---       #783
      01000873H   LINE      CODE     ---       #784
      01000875H   LINE      CODE     ---       #786
      01000875H   LINE      CODE     ---       #788
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 104


      01000882H   LINE      CODE     ---       #789
      01000882H   LINE      CODE     ---       #790
      0100088AH   LINE      CODE     ---       #791
      0100088CH   LINE      CODE     ---       #792
      01000892H   LINE      CODE     ---       #793
      01000892H   LINE      CODE     ---       #794
      0100089AH   LINE      CODE     ---       #795
      0100089CH   LINE      CODE     ---       #796
      010008A2H   LINE      CODE     ---       #797
      010008A2H   LINE      CODE     ---       #798
      010008AAH   LINE      CODE     ---       #799
      010008AAH   LINE      CODE     ---       #800
      010008AAH   LINE      CODE     ---       #801
      010008AAH   LINE      CODE     ---       #802
      010008ADH   LINE      CODE     ---       #803
      010008ADH   LINE      CODE     ---       #804
      010008B3H   LINE      CODE     ---       #805
      010008B3H   LINE      CODE     ---       #806
      010008C4H   LINE      CODE     ---       #807
      010008C4H   LINE      CODE     ---       #808
      010008C4H   LINE      CODE     ---       #809
      010008C6H   LINE      CODE     ---       #811
      010008C6H   LINE      CODE     ---       #812
      010008CEH   LINE      CODE     ---       #813
      010008D5H   LINE      CODE     ---       #814
      010008DBH   LINE      CODE     ---       #815
      010008DDH   LINE      CODE     ---       #816
      010008DFH   LINE      CODE     ---       #817
      010008E1H   LINE      CODE     ---       #818
      010008E3H   LINE      CODE     ---       #819
      010008E5H   LINE      CODE     ---       #820
      010008E5H   LINE      CODE     ---       #821
      010008E8H   LINE      CODE     ---       #823
      010008E8H   LINE      CODE     ---       #824
      010008F0H   LINE      CODE     ---       #825
      010008F7H   LINE      CODE     ---       #826
      010008FDH   LINE      CODE     ---       #827
      010008FFH   LINE      CODE     ---       #828
      01000901H   LINE      CODE     ---       #829
      01000903H   LINE      CODE     ---       #830
      01000905H   LINE      CODE     ---       #831
      01000905H   LINE      CODE     ---       #832
      01000908H   LINE      CODE     ---       #833
      01000919H   LINE      CODE     ---       #834
      01000919H   LINE      CODE     ---       #835
      01000921H   LINE      CODE     ---       #836
      01000924H   LINE      CODE     ---       #838
      01000924H   LINE      CODE     ---       #839
      0100092AH   LINE      CODE     ---       #840
      0100092AH   LINE      CODE     ---       #841
      0100092AH   LINE      CODE     ---       #842
      0100092AH   LINE      CODE     ---       #843
      0100092AH   LINE      CODE     ---       #844
      0100092AH   LINE      CODE     ---       #845
      0100092AH   LINE      CODE     ---       #846
      0100092AH   LINE      CODE     ---       #847
      0100092CH   LINE      CODE     ---       #849
      0100092CH   LINE      CODE     ---       #850
      01000932H   LINE      CODE     ---       #851
      01000932H   LINE      CODE     ---       #852
      01000939H   LINE      CODE     ---       #853
      0100093CH   LINE      CODE     ---       #855
      0100093CH   LINE      CODE     ---       #856
      01000944H   LINE      CODE     ---       #857
      0100094BH   LINE      CODE     ---       #858
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 105


      01000951H   LINE      CODE     ---       #859
      01000953H   LINE      CODE     ---       #860
      01000955H   LINE      CODE     ---       #861
      01000957H   LINE      CODE     ---       #862
      01000957H   LINE      CODE     ---       #863
      01000957H   LINE      CODE     ---       #864
      01000957H   LINE      CODE     ---       #865
      0100095AH   LINE      CODE     ---       #866
      01000965H   LINE      CODE     ---       #867
      01000965H   LINE      CODE     ---       #868
      01000967H   LINE      CODE     ---       #869
      01000969H   LINE      CODE     ---       #870
      0100096BH   LINE      CODE     ---       #871
      0100096DH   LINE      CODE     ---       #872
      0100096FH   LINE      CODE     ---       #873
      01000972H   LINE      CODE     ---       #874
      01000979H   LINE      CODE     ---       #875
      01000979H   LINE      CODE     ---       #876
      0100097BH   LINE      CODE     ---       #877
      0100097DH   LINE      CODE     ---       #878
      0100097FH   LINE      CODE     ---       #879
      01000981H   LINE      CODE     ---       #880
      01000983H   LINE      CODE     ---       #881
      01000985H   LINE      CODE     ---       #882
      01000987H   LINE      CODE     ---       #885
      01000989H   LINE      CODE     ---       #886
      0100098BH   LINE      CODE     ---       #887
      0100098DH   LINE      CODE     ---       #888
      01000993H   LINE      CODE     ---       #889
      01000999H   LINE      CODE     ---       #890
      0100099BH   LINE      CODE     ---       #891
      0100099DH   LINE      CODE     ---       #892
      0100099FH   LINE      CODE     ---       #893
      010009A1H   LINE      CODE     ---       #895
      010009A4H   LINE      CODE     ---       #897
      010009A7H   LINE      CODE     ---       #899
      010009AAH   LINE      CODE     ---       #901
      010009ADH   LINE      CODE     ---       #902
      010009B0H   LINE      CODE     ---       #903
      010009B3H   LINE      CODE     ---       #904
      010009B6H   LINE      CODE     ---       #905
      010009B9H   LINE      CODE     ---       #906
      010009BCH   LINE      CODE     ---       #907
      010009BFH   LINE      CODE     ---       #909
      010009C5H   LINE      CODE     ---       #910
      010009C5H   LINE      CODE     ---       #911
      ---         BLOCKEND  ---      ---       LVL=0

      01002108H   BLOCK     CODE     ---       LVL=0
      00000006H   SYMBOL    DATA     WORD      z
      01002108H   BLOCK     CODE     NEAR LAB  LVL=1
      00000002H   SYMBOL    DATA     WORD      x
      00000004H   SYMBOL    DATA     WORD      y
      ---         BLOCKEND  ---      ---       LVL=1
      01002108H   LINE      CODE     ---       #915
      01002108H   LINE      CODE     ---       #916
      01002108H   LINE      CODE     ---       #918
      01002112H   LINE      CODE     ---       #919
      01002127H   LINE      CODE     ---       #920
      ---         BLOCKEND  ---      ---       LVL=0

      01001BDCH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Direction
      01001BDCH   BLOCK     CODE     NEAR LAB  LVL=1
      02000000H   SYMBOL    XDATA    BYTE      Step_No
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 106


      ---         BLOCKEND  ---      ---       LVL=1
      01001BDCH   LINE      CODE     ---       #922
      01001BDCH   LINE      CODE     ---       #923
      01001BDCH   LINE      CODE     ---       #926
      01001BE1H   LINE      CODE     ---       #927
      01001BE1H   LINE      CODE     ---       #928
      01001BE7H   LINE      CODE     ---       #929
      01001BF1H   LINE      CODE     ---       #930
      01001BF1H   LINE      CODE     ---       #931
      01001BF3H   LINE      CODE     ---       #932
      01001BF3H   LINE      CODE     ---       #933
      01001BF5H   LINE      CODE     ---       #935
      01001BF5H   LINE      CODE     ---       #936
      01001C02H   LINE      CODE     ---       #937
      01001C02H   LINE      CODE     ---       #938
      01001C05H   LINE      CODE     ---       #939
      01001C05H   LINE      CODE     ---       #940
      01001C0BH   LINE      CODE     ---       #941
      01001C0BH   LINE      CODE     ---       #943
      01001C1CH   LINE      CODE     ---       #944
      01001C1CH   LINE      CODE     ---       #945
      01001C20H   LINE      CODE     ---       #946
      01001C26H   LINE      CODE     ---       #947
      01001C2FH   LINE      CODE     ---       #948
      01001C38H   LINE      CODE     ---       #949
      01001C40H   LINE      CODE     ---       #950
      01001C40H   LINE      CODE     ---       #951
      ---         BLOCKEND  ---      ---       LVL=0

      01001E7CH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Key_Input
      01001E7CH   LINE      CODE     ---       #953
      01001E7CH   LINE      CODE     ---       #954
      01001E7CH   LINE      CODE     ---       #955
      01001E89H   LINE      CODE     ---       #956
      01001E89H   LINE      CODE     ---       #957
      01001E89H   LINE      CODE     ---       #958
      01001E92H   LINE      CODE     ---       #959
      01001E98H   LINE      CODE     ---       #960
      01001E99H   LINE      CODE     ---       #961
      01001E99H   LINE      CODE     ---       #962
      01001E9CH   LINE      CODE     ---       #963
      01001E9CH   LINE      CODE     ---       #964
      01001EA1H   LINE      CODE     ---       #965
      01001EA2H   LINE      CODE     ---       #967
      01001EA2H   LINE      CODE     ---       #968
      01001EA8H   LINE      CODE     ---       #969
      01001EA8H   LINE      CODE     ---       #970
      01001EA9H   LINE      CODE     ---       #971
      01001EA9H   LINE      CODE     ---       #972
      01001EB2H   LINE      CODE     ---       #973
      01001EB8H   LINE      CODE     ---       #974
      01001EB8H   LINE      CODE     ---       #975
      01001EB8H   LINE      CODE     ---       #976
      01001EB8H   LINE      CODE     ---       #977
      01001EB8H   LINE      CODE     ---       #978
      ---         BLOCKEND  ---      ---       LVL=0

      010018A4H   BLOCK     CODE     ---       LVL=0
      010018A4H   BLOCK     CODE     NEAR LAB  LVL=1
      02000001H   SYMBOL    XDATA    BYTE      adc_state
      02000002H   SYMBOL    XDATA    BYTE      sample_count
      02000003H   SYMBOL    XDATA    WORD      adc_sum
      ---         BLOCKEND  ---      ---       LVL=1
      010018A4H   LINE      CODE     ---       #981
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 107


      010018A4H   LINE      CODE     ---       #982
      010018A4H   LINE      CODE     ---       #988
      010018B5H   LINE      CODE     ---       #989
      010018B5H   LINE      CODE     ---       #990
      010018B5H   LINE      CODE     ---       #991
      010018BAH   LINE      CODE     ---       #992
      010018C0H   LINE      CODE     ---       #993
      010018C1H   LINE      CODE     ---       #995
      010018C1H   LINE      CODE     ---       #996
      010018CAH   LINE      CODE     ---       #997
      010018CAH   LINE      CODE     ---       #998
      010018D9H   LINE      CODE     ---       #999
      010018DFH   LINE      CODE     ---       #1000
      010018E2H   LINE      CODE     ---       #1002
      010018EFH   LINE      CODE     ---       #1003
      010018EFH   LINE      CODE     ---       #1004
      01001906H   LINE      CODE     ---       #1005
      0100190DH   LINE      CODE     ---       #1006
      01001911H   LINE      CODE     ---       #1007
      01001917H   LINE      CODE     ---       #1008
      01001918H   LINE      CODE     ---       #1010
      01001918H   LINE      CODE     ---       #1011
      01001918H   LINE      CODE     ---       #1012
      01001918H   LINE      CODE     ---       #1013
      01001918H   LINE      CODE     ---       #1014
      0100191AH   LINE      CODE     ---       #1016
      0100191AH   LINE      CODE     ---       #1017
      0100192AH   LINE      CODE     ---       #1018
      0100192AH   LINE      CODE     ---       #1019
      01001930H   LINE      CODE     ---       #1020
      0100193BH   LINE      CODE     ---       #1021
      0100193DH   LINE      CODE     ---       #1023
      0100193DH   LINE      CODE     ---       #1024
      0100193FH   LINE      CODE     ---       #1025
      01001944H   LINE      CODE     ---       #1026
      01001944H   LINE      CODE     ---       #1028
      0100194DH   LINE      CODE     ---       #1029
      0100194DH   LINE      CODE     ---       #1030
      01001953H   LINE      CODE     ---       #1031
      0100195EH   LINE      CODE     ---       #1032
      01001960H   LINE      CODE     ---       #1034
      01001960H   LINE      CODE     ---       #1035
      01001965H   LINE      CODE     ---       #1036
      01001967H   LINE      CODE     ---       #1037
      01001967H   LINE      CODE     ---       #1038
      01001967H   LINE      CODE     ---       #1039
      01001969H   LINE      CODE     ---       #1041
      01001969H   LINE      CODE     ---       #1042
      0100196EH   LINE      CODE     ---       #1043
      0100196EH   LINE      CODE     ---       #1044
      0100196EH   LINE      CODE     ---       #1045
      ---         BLOCKEND  ---      ---       LVL=0

      01001F8DH   BLOCK     CODE     ---       LVL=0
      02000044H   SYMBOL    XDATA    WORD      dly1
      01001F8DH   LINE      CODE     ---       #1047
      01001F95H   LINE      CODE     ---       #1048
      01001F95H   LINE      CODE     ---       #1049
      01001F98H   LINE      CODE     ---       #1050
      01001F9BH   LINE      CODE     ---       #1051
      01001FA4H   LINE      CODE     ---       #1052
      01001FB3H   LINE      CODE     ---       #1053
      01001FB6H   LINE      CODE     ---       #1054
      01001FB9H   LINE      CODE     ---       #1055
      ---         BLOCKEND  ---      ---       LVL=0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 108



      01002034H   BLOCK     CODE     ---       LVL=0
      01002034H   BLOCK     CODE     NEAR LAB  LVL=1
      02000044H   SYMBOL    XDATA    WORD      temp
      ---         BLOCKEND  ---      ---       LVL=1
      01002034H   LINE      CODE     ---       #1057
      01002034H   LINE      CODE     ---       #1058
      01002034H   LINE      CODE     ---       #1061
      01002037H   LINE      CODE     ---       #1062
      0100203AH   LINE      CODE     ---       #1063
      0100204BH   LINE      CODE     ---       #1064
      0100204EH   LINE      CODE     ---       #1065
      01002051H   LINE      CODE     ---       #1067
      01002059H   LINE      CODE     ---       #1068
      ---         BLOCKEND  ---      ---       LVL=0

      010016A3H   BLOCK     CODE     ---       LVL=0
      010016A3H   LINE      CODE     ---       #1070
      010016A3H   LINE      CODE     ---       #1071
      010016A3H   LINE      CODE     ---       #1073
      010016AEH   LINE      CODE     ---       #1074
      010016AEH   LINE      CODE     ---       #1076
      010016B1H   LINE      CODE     ---       #1077
      010016B1H   LINE      CODE     ---       #1079
      010016B3H   LINE      CODE     ---       #1080
      010016B6H   LINE      CODE     ---       #1081
      010016B6H   LINE      CODE     ---       #1083
      010016BDH   LINE      CODE     ---       #1084
      010016BFH   LINE      CODE     ---       #1085
      010016C1H   LINE      CODE     ---       #1087
      010016C1H   LINE      CODE     ---       #1088
      010016C3H   LINE      CODE     ---       #1089
      010016C3H   LINE      CODE     ---       #1090
      010016C5H   LINE      CODE     ---       #1092
      010016C5H   LINE      CODE     ---       #1094
      010016C7H   LINE      CODE     ---       #1095
      010016CAH   LINE      CODE     ---       #1096
      010016CAH   LINE      CODE     ---       #1098
      010016CDH   LINE      CODE     ---       #1099
      010016CDH   LINE      CODE     ---       #1100
      010016CFH   LINE      CODE     ---       #1101
      010016D1H   LINE      CODE     ---       #1103
      010016D1H   LINE      CODE     ---       #1104
      010016D3H   LINE      CODE     ---       #1105
      010016D3H   LINE      CODE     ---       #1106
      010016D5H   LINE      CODE     ---       #1108
      010016D5H   LINE      CODE     ---       #1110
      010016D7H   LINE      CODE     ---       #1111
      010016D7H   LINE      CODE     ---       #1112
      010016D7H   LINE      CODE     ---       #1113
      010016D9H   LINE      CODE     ---       #1115
      010016D9H   LINE      CODE     ---       #1117
      010016DCH   LINE      CODE     ---       #1118
      010016DCH   LINE      CODE     ---       #1119
      010016DEH   LINE      CODE     ---       #1120
      010016E1H   LINE      CODE     ---       #1121
      010016E1H   LINE      CODE     ---       #1123
      010016E8H   LINE      CODE     ---       #1124
      010016EAH   LINE      CODE     ---       #1125
      010016ECH   LINE      CODE     ---       #1127
      010016ECH   LINE      CODE     ---       #1128
      010016EEH   LINE      CODE     ---       #1129
      010016EEH   LINE      CODE     ---       #1130
      010016F0H   LINE      CODE     ---       #1132
      010016F0H   LINE      CODE     ---       #1133
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 109


      010016F3H   LINE      CODE     ---       #1134
      010016F3H   LINE      CODE     ---       #1135
      010016F5H   LINE      CODE     ---       #1136
      010016FFH   LINE      CODE     ---       #1138
      010016FFH   LINE      CODE     ---       #1139
      01001706H   LINE      CODE     ---       #1140
      01001708H   LINE      CODE     ---       #1141
      01001708H   LINE      CODE     ---       #1142
      0100170AH   LINE      CODE     ---       #1144
      0100170AH   LINE      CODE     ---       #1145
      0100170CH   LINE      CODE     ---       #1146
      01001716H   LINE      CODE     ---       #1148
      01001716H   LINE      CODE     ---       #1149
      0100171DH   LINE      CODE     ---       #1150
      0100171FH   LINE      CODE     ---       #1151
      0100171FH   LINE      CODE     ---       #1152
      0100171FH   LINE      CODE     ---       #1153
      0100171FH   LINE      CODE     ---       #1154
      0100171FH   LINE      CODE     ---       #1157
      01001722H   LINE      CODE     ---       #1158
      01001722H   LINE      CODE     ---       #1159
      01001730H   LINE      CODE     ---       #1160
      0100173FH   LINE      CODE     ---       #1161
      0100173FH   LINE      CODE     ---       #1162
      01001741H   LINE      CODE     ---       #1163
      01001743H   LINE      CODE     ---       #1165
      01001743H   LINE      CODE     ---       #1166
      01001745H   LINE      CODE     ---       #1167
      01001747H   LINE      CODE     ---       #1168
      0100174EH   LINE      CODE     ---       #1169
      0100174EH   LINE      CODE     ---       #1170
      0100174EH   LINE      CODE     ---       #1173
      01001754H   LINE      CODE     ---       #1174
      0100175EH   LINE      CODE     ---       #1175
      0100175EH   LINE      CODE     ---       #1176
      01001760H   LINE      CODE     ---       #1177
      01001765H   LINE      CODE     ---       #1178
      01001765H   LINE      CODE     ---       #1181
      01001773H   LINE      CODE     ---       #1182
      01001782H   LINE      CODE     ---       #1183
      01001782H   LINE      CODE     ---       #1184
      01001786H   LINE      CODE     ---       #1185
      0100178BH   LINE      CODE     ---       #1186
      0100178BH   LINE      CODE     ---       #1189
      01001793H   LINE      CODE     ---       #1190
      01001793H   LINE      CODE     ---       #1191
      01001799H   LINE      CODE     ---       #1192
      010017A3H   LINE      CODE     ---       #1193
      010017A3H   LINE      CODE     ---       #1194
      010017A5H   LINE      CODE     ---       #1195
      010017AAH   LINE      CODE     ---       #1196
      010017AAH   LINE      CODE     ---       #1197
      010017ABH   LINE      CODE     ---       #1199
      010017ABH   LINE      CODE     ---       #1200
      010017B0H   LINE      CODE     ---       #1201
      010017B2H   LINE      CODE     ---       #1202
      010017B2H   LINE      CODE     ---       #1203
      ---         BLOCKEND  ---      ---       LVL=0

      010011DAH   BLOCK     CODE     ---       LVL=0
      010011DAH   LINE      CODE     ---       #1212
      010011DAH   LINE      CODE     ---       #1213
      010011DAH   LINE      CODE     ---       #1215
      010011E6H   LINE      CODE     ---       #1216
      010011E6H   LINE      CODE     ---       #1217
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 110


      010011E9H   LINE      CODE     ---       #1218
      010011E9H   LINE      CODE     ---       #1219
      010011EBH   LINE      CODE     ---       #1220
      010011EDH   LINE      CODE     ---       #1221
      010011EFH   LINE      CODE     ---       #1222
      010011F1H   LINE      CODE     ---       #1223
      010011F3H   LINE      CODE     ---       #1224
      010011F9H   LINE      CODE     ---       #1225
      010011F9H   LINE      CODE     ---       #1226
      010011F9H   LINE      CODE     ---       #1229
      0100121AH   LINE      CODE     ---       #1230
      0100121AH   LINE      CODE     ---       #1232
      01001220H   LINE      CODE     ---       #1233
      01001220H   LINE      CODE     ---       #1234
      01001229H   LINE      CODE     ---       #1235
      0100122FH   LINE      CODE     ---       #1236
      01001231H   LINE      CODE     ---       #1237
      01001233H   LINE      CODE     ---       #1238
      01001235H   LINE      CODE     ---       #1239
      01001237H   LINE      CODE     ---       #1240
      0100123BH   LINE      CODE     ---       #1241
      0100123BH   LINE      CODE     ---       #1242
      0100123BH   LINE      CODE     ---       #1245
      0100123EH   LINE      CODE     ---       #1246
      0100123EH   LINE      CODE     ---       #1247
      01001240H   LINE      CODE     ---       #1249
      0100125BH   LINE      CODE     ---       #1250
      0100125BH   LINE      CODE     ---       #1252
      0100125DH   LINE      CODE     ---       #1253
      0100125FH   LINE      CODE     ---       #1254
      01001261H   LINE      CODE     ---       #1257
      0100126AH   LINE      CODE     ---       #1258
      01001270H   LINE      CODE     ---       #1259
      01001272H   LINE      CODE     ---       #1260
      01001274H   LINE      CODE     ---       #1261
      01001278H   LINE      CODE     ---       #1262
      0100127FH   LINE      CODE     ---       #1263
      01001281H   LINE      CODE     ---       #1264
      01001290H   LINE      CODE     ---       #1265
      01001290H   LINE      CODE     ---       #1267
      01001296H   LINE      CODE     ---       #1268
      01001298H   LINE      CODE     ---       #1269
      0100129AH   LINE      CODE     ---       #1270
      0100129CH   LINE      CODE     ---       #1271
      0100129EH   LINE      CODE     ---       #1272
      0100129EH   LINE      CODE     ---       #1273
      0100129EH   LINE      CODE     ---       #1276
      010012BFH   LINE      CODE     ---       #1277
      010012BFH   LINE      CODE     ---       #1279
      010012C5H   LINE      CODE     ---       #1280
      010012C5H   LINE      CODE     ---       #1281
      010012CEH   LINE      CODE     ---       #1282
      010012D4H   LINE      CODE     ---       #1283
      010012D6H   LINE      CODE     ---       #1284
      010012D8H   LINE      CODE     ---       #1285
      010012DAH   LINE      CODE     ---       #1286
      010012DCH   LINE      CODE     ---       #1287
      010012E1H   LINE      CODE     ---       #1288
      010012E1H   LINE      CODE     ---       #1289
      010012E1H   LINE      CODE     ---       #1292
      010012E4H   LINE      CODE     ---       #1293
      010012E4H   LINE      CODE     ---       #1294
      010012E6H   LINE      CODE     ---       #1296
      01001301H   LINE      CODE     ---       #1297
      01001301H   LINE      CODE     ---       #1299
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 111


      01001303H   LINE      CODE     ---       #1300
      01001305H   LINE      CODE     ---       #1301
      01001307H   LINE      CODE     ---       #1304
      01001310H   LINE      CODE     ---       #1305
      01001316H   LINE      CODE     ---       #1306
      01001318H   LINE      CODE     ---       #1307
      0100131AH   LINE      CODE     ---       #1308
      0100131FH   LINE      CODE     ---       #1309
      01001326H   LINE      CODE     ---       #1310
      01001327H   LINE      CODE     ---       #1311
      01001336H   LINE      CODE     ---       #1312
      01001336H   LINE      CODE     ---       #1314
      0100133CH   LINE      CODE     ---       #1315
      0100133EH   LINE      CODE     ---       #1316
      01001340H   LINE      CODE     ---       #1317
      01001342H   LINE      CODE     ---       #1318
      01001344H   LINE      CODE     ---       #1319
      01001344H   LINE      CODE     ---       #1320
      01001344H   LINE      CODE     ---       #1321
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ?C?FPMUL
      010009C8H   PUBLIC    CODE     ---       ?C?FPMUL

      ---         MODULE    ---      ---       ?C?FPDIV
      01000AD1H   PUBLIC    CODE     ---       ?C?FPDIV

      ---         MODULE    ---      ---       ?C?FCAST
      01000B78H   PUBLIC    CODE     ---       ?C?FCASTC
      01000B73H   PUBLIC    CODE     ---       ?C?FCASTI
      01000B6EH   PUBLIC    CODE     ---       ?C?FCASTL

      ---         MODULE    ---      ---       PRINTF

      ---         MODULE    ---      ---       ?C?FPGETOPN
      01000BACH   PUBLIC    CODE     ---       ?C?FPGETOPN2
      01000BE1H   PUBLIC    CODE     ---       ?C?FPNANRESULT
      01000BEBH   PUBLIC    CODE     ---       ?C?FPOVERFLOW
      01000BC3H   PUBLIC    CODE     ---       ?C?FPRESULT
      01000BD7H   PUBLIC    CODE     ---       ?C?FPRESULT2
      01000BE8H   PUBLIC    CODE     ---       ?C?FPUNDERFLOW

      ---         MODULE    ---      ---       ?C?FPROUND
      01000BF6H   PUBLIC    CODE     ---       ?C?FPROUND

      ---         MODULE    ---      ---       ?C?FPCONVERT
      01000C33H   PUBLIC    CODE     ---       ?C?FPCONVERT

      ---         MODULE    ---      ---       ?C?FPADD
      01000D3FH   PUBLIC    CODE     ---       ?C?FPADD
      01000D3BH   PUBLIC    CODE     ---       ?C?FPSUB

      ---         MODULE    ---      ---       ?C?FTNPWR
      01000E60H   PUBLIC    CODE     ---       ?C?FTNPWR

      ---         MODULE    ---      ---       ?C_INIT
      010019C4H   PUBLIC    CODE     ---       ?C_START

      ---         MODULE    ---      ---       ?C?COPY
      01000F70H   PUBLIC    CODE     ---       ?C?COPY

      ---         MODULE    ---      ---       ?C?CLDPTR
      01000F96H   PUBLIC    CODE     ---       ?C?CLDPTR

      ---         MODULE    ---      ---       ?C?CLDOPTR
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  13:16:50  PAGE 112


      01000FAFH   PUBLIC    CODE     ---       ?C?CLDOPTR

      ---         MODULE    ---      ---       ?C?CSTPTR
      01000FDCH   PUBLIC    CODE     ---       ?C?CSTPTR

      ---         MODULE    ---      ---       ?C?CSTOPTR
      01000FEEH   PUBLIC    CODE     ---       ?C?CSTOPTR

      ---         MODULE    ---      ---       ?C?UIDIV
      01001010H   PUBLIC    CODE     ---       ?C?UIDIV

      ---         MODULE    ---      ---       ?C?ILDIX
      01001065H   PUBLIC    CODE     ---       ?C?ILDIX

      ---         MODULE    ---      ---       ?C?ULDIV
      010010B7H   PUBLIC    CODE     ---       ?C?ULDIV

      ---         MODULE    ---      ---       ?C?LNEG
      01001149H   PUBLIC    CODE     ---       ?C?LNEG

      ---         MODULE    ---      ---       ?C?LSTXDATA
      01001157H   PUBLIC    CODE     ---       ?C?LSTXDATA

      ---         MODULE    ---      ---       ?C?LSTKXDATA
      01001163H   PUBLIC    CODE     ---       ?C?LSTKXDATA

      ---         MODULE    ---      ---       ?C?PLDIXDATA
      01001194H   PUBLIC    CODE     ---       ?C?PLDIXDATA

      ---         MODULE    ---      ---       ?C?PSTXDATA
      010011ABH   PUBLIC    CODE     ---       ?C?PSTXDATA

      ---         MODULE    ---      ---       ?C?CCASE
      010011B4H   PUBLIC    CODE     ---       ?C?CCASE

Program Size: data=15.2 xdata=162 const=13 code=8780
LX51 RUN COMPLETE.  0 WARNING(S),  0 ERROR(S)
