LX51 LINKER/LOCATER V4.66.97.0                                                          07/23/2025  14:09:31  PAGE 1


LX51 LINKER/LOCATER V4.66.97.0, INVOKED BY:
D:\KEILC51\C51\BIN\LX51.EXE .\Objects\startup_cms8s6990.obj, .\Objects\adc.obj, .\Objects\epwm.obj, .\Objects\gpio.obj, 
>> .\Objects\system.obj, .\Objects\timer.obj, .\Objects\uart.obj, .\Objects\wdt.obj, .\Objects\flash.obj, .\Objects\ADC_
>> Init.obj, .\Objects\define.obj, .\Objects\GPIO_Init.obj, .\Objects\Timer_Init.obj, .\Objects\UART_Init.obj, .\Objects
>> \isr.obj, .\Objects\UART_Function.obj, .\Objects\Battery_Function.obj, .\Objects\Key.obj, .\Objects\ADC_Used.obj, .\O
>> bjects\main.obj TO .\Objects\Project PRINT (.\Listings\Project.map) REMOVEUNUSED


CPU MODE:     8051 MODE
MEMORY MODEL: LARGE WITH FLOATING POINT ARITHMETIC


INPUT MODULES INCLUDED:
  .\Objects\startup_cms8s6990.obj (?C_STARTUP)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  .\Objects\adc.obj (ADC)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\epwm.obj (EPWM)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\gpio.obj (GPIO)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\system.obj (SYSTEM)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\timer.obj (TIMER)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\uart.obj (UART)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\wdt.obj (WDT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\flash.obj (FLASH)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\ADC_Init.obj (ADC_INIT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\define.obj (DEFINE)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\GPIO_Init.obj (GPIO_INIT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\Timer_Init.obj (TIMER_INIT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\UART_Init.obj (UART_INIT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\isr.obj (ISR)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\UART_Function.obj (UART_FUNCTION)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\Battery_Function.obj (BATTERY_FUNCTION)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\Key.obj (KEY)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\ADC_Used.obj (ADC_USED)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\main.obj (MAIN)
         COMMENT TYPE 0: C51 V9.60.0.0
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPMUL)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPDIV)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FCAST)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (PRINTF)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPGETOPN)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPROUND)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPCONVERT)
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 2


         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPADD)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FTNPWR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C_INIT)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?COPY)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CLDPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CLDOPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CSTPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CSTOPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?UIDIV)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?ILDIX)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?ULDIV)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?LNEG)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?LSTXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?LSTKXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?PLDIXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?PSTXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CCASE)
         COMMENT TYPE 1: A51 / ASM51 Assembler


ACTIVE MEMORY CLASSES OF MODULE:  .\Objects\Project (?C_STARTUP)

BASE        START       END         USED      MEMORY CLASS
==========================================================
C:000000H   C:000000H   C:00FFFFH   002252H   CODE
I:000000H   I:000000H   I:0000FFH   000001H   IDATA
X:000000H   X:000000H   X:00FFFFH   0000A2H   XDATA
I:000020H.0 I:000020H.0 I:00002FH.7 000006H.2 BIT
C:000000H   C:000000H   C:00FFFFH   00000DH   CONST
I:000000H   I:000000H   I:00007FH   000008H   DATA


MEMORY MAP OF MODULE:  .\Objects\Project (?C_STARTUP)


START     STOP      LENGTH    ALIGN  RELOC    MEMORY CLASS   SEGMENT NAME
=========================================================================

* * * * * * * * * * *   D A T A   M E M O R Y   * * * * * * * * * * * * *
000000H   000007H   000008H   ---    AT..     DATA           "REG BANK 0"
000008H.0 00001FH.7 000018H.0 ---    ---      **GAP**
000020H.0 000024H.4 000004H.5 BIT    UNIT     BIT            ?BI?MAIN
000024H.5 000025H.1 000000H.5 BIT    UNIT     BIT            ?BI?KEY
000025H.2 000025H.5 000000H.4 BIT    UNIT     BIT            ?BI?DEFINE
000025H.6 000026H.1 000000H.4 BIT    UNIT     BIT            _BIT_GROUP_
000026H.2 000026H   000000H.6 ---    ---      **GAP**
000027H   000027H   000001H   BYTE   UNIT     IDATA          ?STACK

LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 3


* * * * * * * * * * *   C O D E   M E M O R Y   * * * * * * * * * * * * *
000000H   000002H   000003H   ---    OFFS..   CODE           ?CO??C_STARTUP?0
000003H   000005H   000003H   BYTE   OFFS..   CODE           ?ISR?00003
000006H   000009H   000004H   BYTE   UNIT     CODE           ?PR?ADC_START?ADC
00000AH   00000AH   000001H   BYTE   UNIT     CODE           ?PR?INT0_IRQHANDLER?ISR
00000BH   00000DH   000003H   BYTE   OFFS..   CODE           ?ISR?0000B
00000EH   000011H   000004H   BYTE   UNIT     CODE           ?PR?SYS_ENABLEWAKEUP?SYSTEM
000012H   000012H   000001H   BYTE   UNIT     CODE           ?PR?INT1_IRQHANDLER?ISR
000013H   000015H   000003H   BYTE   OFFS..   CODE           ?ISR?00013
000016H   000019H   000004H   BYTE   UNIT     CODE           ?PR?FLASH_UNLOCK?FLASH
00001AH   00001AH   000001H   BYTE   UNIT     CODE           ?PR?TIMER2_IRQHANDLER?ISR
00001BH   00001DH   000003H   BYTE   OFFS..   CODE           ?ISR?0001B
00001EH   000021H   000004H   BYTE   UNIT     CODE           ?PR?FLASH_LOCK?FLASH
000022H   000022H   000001H   BYTE   UNIT     CODE           ?PR?UART1_IRQHANDLER?ISR
000023H   000025H   000003H   BYTE   OFFS..   CODE           ?ISR?00023
000026H   000028H   000003H   BYTE   UNIT     CODE           ?PR?ADC_GETRESULT?ADC_USED
000029H   000029H   000001H   BYTE   UNIT     CODE           ?PR?P0EI_IRQHANDLER?ISR
00002AH   00002AH   000001H   BYTE   UNIT     CODE           ?PR?P3EI_IRQHANDLER?ISR
00002BH   00002DH   000003H   BYTE   OFFS..   CODE           ?ISR?0002B
00002EH   00002EH   000001H   BYTE   UNIT     CODE           ?PR?LVD_IRQHANDLER?ISR
00002FH   00002FH   000001H   BYTE   UNIT     CODE           ?PR?LSE_IRQHANDLER?ISR
000030H   000030H   000001H   BYTE   UNIT     CODE           ?PR?ACMP_IRQHANDLER?ISR
000031H   000031H   000001H   BYTE   UNIT     CODE           ?PR?TIMER3_IRQHANDLER?ISR
000032H   000032H   000001H   BYTE   UNIT     CODE           ?PR?TIMER4_IRQHANDLER?ISR
000033H   000035H   000003H   BYTE   OFFS..   CODE           ?ISR?00033
000036H   000036H   000001H   BYTE   UNIT     CODE           ?PR?EPWM_IRQHANDLER?ISR
000037H   000037H   000001H   BYTE   UNIT     CODE           ?PR?ADC_IRQHANDLER?ISR
000038H   000038H   000001H   BYTE   UNIT     CODE           ?PR?WDT_IRQHANDLER?ISR
000039H   000039H   000001H   BYTE   UNIT     CODE           ?PR?I2C_IRQHANDLER?ISR
00003AH   00003AH   000001H   BYTE   UNIT     CODE           ?PR?SPI_IRQHANDLER?ISR
00003BH   00003DH   000003H   BYTE   OFFS..   CODE           ?ISR?0003B
00003EH   00003EH   000001H   BYTE   UNIT     CODE           ?PR?ADC_CLEARCONVERTINTFLAG?ADC_USED
00003FH   000042H   000004H   ---    ---      **GAP**
000043H   000045H   000003H   BYTE   OFFS..   CODE           ?ISR?00043
000046H   00004AH   000005H   ---    ---      **GAP**
00004BH   00004DH   000003H   BYTE   OFFS..   CODE           ?ISR?0004B
00004EH   000052H   000005H   ---    ---      **GAP**
000053H   000055H   000003H   BYTE   OFFS..   CODE           ?ISR?00053
000056H   000061H   00000CH   BYTE   UNIT     CODE           ?PR?SYS_ENTERSTOP?SYSTEM
000062H   000062H   000001H   ---    ---      **GAP**
000063H   000065H   000003H   BYTE   OFFS..   CODE           ?ISR?00063
000066H   00006AH   000005H   ---    ---      **GAP**
00006BH   00006DH   000003H   BYTE   OFFS..   CODE           ?ISR?0006B
00006EH   000072H   000005H   ---    ---      **GAP**
000073H   000075H   000003H   BYTE   OFFS..   CODE           ?ISR?00073
000076H   00007AH   000005H   ---    ---      **GAP**
00007BH   00007DH   000003H   BYTE   OFFS..   CODE           ?ISR?0007B
00007EH   000082H   000005H   ---    ---      **GAP**
000083H   000085H   000003H   BYTE   OFFS..   CODE           ?ISR?00083
000086H   00008FH   00000AH   BYTE   UNIT     CODE           ?PR?ADC_GETCONVERTINTFLAG?ADC_USED
000090H   000092H   000003H   ---    ---      **GAP**
000093H   000095H   000003H   BYTE   OFFS..   CODE           ?ISR?00093
000096H   00009AH   000005H   ---    ---      **GAP**
00009BH   00009DH   000003H   BYTE   OFFS..   CODE           ?ISR?0009B
00009EH   0000A2H   000005H   ---    ---      **GAP**
0000A3H   0000A5H   000003H   BYTE   OFFS..   CODE           ?ISR?000A3
0000A6H   0000AAH   000005H   ---    ---      **GAP**
0000ABH   0000ADH   000003H   BYTE   OFFS..   CODE           ?ISR?000AB
0000AEH   0000B2H   000005H   ---    ---      **GAP**
0000B3H   0000B5H   000003H   BYTE   OFFS..   CODE           ?ISR?000B3
0000B6H   0009CCH   000917H   BYTE   UNIT     CODE           ?PR?MAIN?MAIN
0009CDH   0011DEH   000812H   BYTE   UNIT     CODE           ?C?LIB_CODE
0011DFH   001349H   00016BH   BYTE   UNIT     CODE           ?PR?KEY_INTERRUPT_PROCESS?MAIN
00134AH   001473H   00012AH   BYTE   UNIT     CODE           ?PR?KEY_SCAN?KEY
001474H   00158EH   00011BH   BYTE   UNIT     CODE           ?PR?TIMER0_IRQHANDLER?ISR
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 4


00158FH   0016A7H   000119H   BYTE   UNIT     CODE           ?PR?UART_DATA_PROCESS?UART_FUNCTION
0016A8H   0017B7H   000110H   BYTE   UNIT     CODE           ?PR?LED_CONTROL?MAIN
0017B8H   0018A8H   0000F1H   BYTE   UNIT     CODE           ?C_INITSEG
0018A9H   001973H   0000CBH   BYTE   UNIT     CODE           ?PR?BATTERY_CHECK?MAIN
001974H   001A0DH   00009AH   BYTE   UNIT     CODE           ?C_C51STARTUP
001A0EH   001A85H   000078H   BYTE   UNIT     CODE           ?PR?P1EI_IRQHANDLER?ISR
001A86H   001AFDH   000078H   BYTE   UNIT     CODE           ?PR?P2EI_IRQHANDLER?ISR
001AFEH   001B70H   000073H   BYTE   UNIT     CODE           ?PR?_FUNCTION_UART_SEND_CMD?UART_FUNCTION
001B71H   001BE0H   000070H   BYTE   UNIT     CODE           ?PR?GPIO_CONFIG?GPIO_INIT
001BE1H   001C46H   000066H   BYTE   UNIT     CODE           ?PR?_MOTOR_STEP_CONTROL?MAIN
001C47H   001CA5H   00005FH   BYTE   UNIT     CODE           ?PR?_UART_CONFIGRUNMODE?UART
001CA6H   001D02H   00005DH   BYTE   UNIT     CODE           ?PR?_UART_SEND_STRING?UART_INIT
001D03H   001D5DH   00005BH   BYTE   UNIT     CODE           ?PR?UART0_IRQHANDLER?ISR
001D5EH   001DACH   00004FH   BYTE   UNIT     CODE           ?PR?UART_0_CONFIG?UART_INIT
001DADH   001DF8H   00004CH   BYTE   UNIT     CODE           ?PR?_TMR_CONFIGRUNMODE?TIMER
001DF9H   001E42H   00004AH   BYTE   UNIT     CODE           ?PR?_TMR_CONFIGTIMERCLK?TIMER
001E43H   001E81H   00003FH   BYTE   UNIT     CODE           ?PR?UART_1_CONFIG?UART_INIT
001E82H   001EBEH   00003DH   BYTE   UNIT     CODE           ?PR?_KEY_FUNCTION_SWITCH_SYSTEM?MAIN
001EBFH   001EF8H   00003AH   BYTE   UNIT     CODE           ?PR?KEY_BUFF_RETURN?KEY
001EF9H   001F2DH   000035H   BYTE   UNIT     CODE           ?PR?_FLASH_WRITE?FLASH
001F2EH   001F61H   000034H   BYTE   UNIT     CODE           ?PR?_FLASH_READ?FLASH
001F62H   001F92H   000031H   BYTE   UNIT     CODE           ?PR?_FLASH_ERASE?FLASH
001F93H   001FBFH   00002DH   BYTE   UNIT     CODE           ?PR?_STORE_DLY?MAIN
001FC0H   001FE9H   00002AH   BYTE   UNIT     CODE           ?PR?TMR0_CONFIG?TIMER_INIT
001FEAH   002012H   000029H   BYTE   UNIT     CODE           ?PR?TMR1_CONFIG?TIMER_INIT
002013H   002039H   000027H   BYTE   UNIT     CODE           ?PR?_TMR_CONFIGTIMERPERIOD?TIMER
00203AH   00205FH   000026H   BYTE   UNIT     CODE           ?PR?RESTORE_DLY?MAIN
002060H   002084H   000025H   BYTE   UNIT     CODE           ?PR?ADC_GETADCRESULT?ADC
002085H   0020A9H   000025H   BYTE   UNIT     CODE           ?PR?_UART_GETRECEIVEINTFLAG?UART
0020AAH   0020CDH   000024H   BYTE   UNIT     CODE           ?PR?TIMER1_IRQHANDLER?ISR
0020CEH   0020EDH   000020H   BYTE   UNIT     CODE           ?PR?_UART_DATA_COPY?UART_FUNCTION
0020EEH   00210DH   000020H   BYTE   UNIT     CODE           ?PR?UART_DATA_INIT?UART_FUNCTION
00210EH   00212DH   000020H   BYTE   UNIT     CODE           ?PR?_DELAY1MS?MAIN
00212EH   00214CH   00001FH   BYTE   UNIT     CODE           ?PR?_TMR_START?TIMER
00214DH   00216BH   00001FH   BYTE   UNIT     CODE           ?PR?_TMR_STOP?TIMER
00216CH   00218AH   00001FH   BYTE   UNIT     CODE           ?PR?_UART_CLEARRECEIVEINTFLAG?UART
00218BH   0021A8H   00001EH   BYTE   UNIT     CODE           ?PR?_ADC_ENABLECHANNEL?ADC
0021A9H   0021C5H   00001DH   BYTE   UNIT     CODE           ?PR?_TMR_ENABLEOVERFLOWINT?TIMER
0021C6H   0021DFH   00001AH   BYTE   UNIT     CODE           ?PR?ADC_CONFIG?ADC_INIT
0021E0H   0021F5H   000016H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGRUNMODE?ADC
0021F6H   002209H   000014H   BYTE   UNIT     CODE           ?PR?_UART_GETBUFF?UART
00220AH   00221CH   000013H   BYTE   UNIT     CODE           ?PR?_UART_ENABLEDOUBLEFREQUENCY?UART
00221DH   00222FH   000013H   BYTE   UNIT     CODE           ?PR?_UART_ENABLERECEIVE?UART
002230H   002242H   000013H   BYTE   UNIT     CODE           ?PR?GPIO_KEY_INTERRUPT_CONFIG?GPIO_INIT
002243H   002253H   000011H   BYTE   UNIT     CODE           ?PR?_UART_ENABLEINT?UART
002254H   00225CH   000009H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGADCVREF?ADC
00225DH   002265H   000009H   BYTE   UNIT     CODE           ?PR?_UART_CONFIGBRTCLK?UART
002266H   00226EH   000009H   BYTE   UNIT     CODE           ?PR?_UART_CONFIGBRTPERIOD?UART
00226FH   002277H   000009H   BYTE   UNIT     CODE           ?PR?_ADC_STARTCONVERT?ADC_USED
002278H   00227FH   000008H   BYTE   UNIT     CODE           ?PR?UART_ENABLEBRT?UART
002280H   002285H   000006H   BYTE   UNIT     CODE           ?PR?RETURN_UART_DATA_LENGTH?UART_FUNCTION
002286H   00228BH   000006H   BYTE   UNIT     CODE           ?PR?CLEAN_UART_DATA_LENGTH?UART_FUNCTION
00228CH   002298H   00000DH   BYTE   UNIT     CONST          ?CO?UART_FUNCTION

* * * * * * * * * * *  X D A T A   M E M O R Y  * * * * * * * * * * * * *
000000H   000032H   000033H   BYTE   UNIT     XDATA          ?XD?MAIN
000033H   000056H   000024H   BYTE   UNIT     XDATA          _XDATA_GROUP_
000057H   000077H   000021H   BYTE   UNIT     XDATA          ?XD?UART_FUNCTION
000078H   00008CH   000015H   BYTE   UNIT     XDATA          ?XD?DEFINE
00008DH   000097H   00000BH   BYTE   UNIT     XDATA          ?XD?ADC_USED
000098H   00009DH   000006H   BYTE   UNIT     XDATA          ?XD?KEY
00009EH   0000A1H   000004H   BYTE   UNIT     XDATA          ?XD?ISR

* * * * * * * * *   R E M O V E D     S E G M E N T S   * * * * * * * *
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_STOP?ADC
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 5


   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_ENABLEHARDWARETRIG?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_DISABLEHARDWARETRIG?ADC
   *DEL*:           000015H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGHARDWARETRIG?ADC
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGAN31?ADC
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_ADC_SETTRIGDELAYTIME?ADC
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGADCBRAKE?ADC
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGCOMPAREVALUE?ADC
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?ADC_GETCMPRESULT?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_ENABLEINT?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_DISABLEINT?ADC
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?ADC_GETINTFLAG?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_CLEARINTFLAG?ADC
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?ADC_ENABLELDO?ADC
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?ADC_DISABLELDO?ADC
   *DEL*:           000006H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGRUNMODE?EPWM
   *DEL*:           00005CH   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELCLK?EPWM
   *DEL*:           000080H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELPERIOD?EPWM
   *DEL*:           000080H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELSYMDUTY?EPWM
   *DEL*:           0000C2H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELASYMDUTY?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEONESHOTMODE?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEAUTOLOADMODE?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_START?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_STOP?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEOUTPUT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEOUTPUT?EPWM
   *DEL*:           000033H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEFAULTBRAKE?EPWM
   *DEL*:           00002BH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEFAULTBRAKE?EPWM
   *DEL*:           000015H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELBRAKELEVEL?EPWM
   *DEL*:           000039H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEDEADZONE?EPWM
   *DEL*:           00002CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEDEADZONE?EPWM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEMASKCONTROL?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEMASKCONTROL?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEUPCMPINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEUPCMPINT?EPWM
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_EPWM_GETUPCMPINTFLAG?EPWM
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_EPWM_CLEARUPCMPINTFLAG?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEDOWNCMPINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEDOWNCMPINT?EPWM
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_EPWM_GETDOWNCMPINTFLAG?EPWM
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_EPWM_CLEARDOWNCMPINTFLAG?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEPERIODINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEPERIODINT?EPWM
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_EPWM_CLEARPERIODINTFLAG?EPWM
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_EPWM_GETPERIODINTFLAG?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEZEROINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEZEROINT?EPWM
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_EPWM_CLEARZEROINTFLAG?EPWM
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_EPWM_GETZEROINTFLAG?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?EPWM_ENABLEFAULTBRAKEINT?EPWM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?EPWM_DISABLEFAULTBRAKEINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?EPWM_GETFAULTBRAKEINTFLAG?EPWM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?EPWM_CLEARFAULTBRAKEINTFLAG?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEREVERSEOUTPUT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEREVERSEOUTPUT?EPWM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?EPWM_TRIGSOFTWAREBRAKE?EPWM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?EPWM_DISABLESOFTWAREBRAKE?EPWM
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGFBBRAKE?EPWM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?EPWM_ALLINTENABLE?EPWM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?EPWM_ALLINTDISABLE?EPWM
   *DEL*:           0000DEH   BYTE   UNIT     CODE           ?PR?_GPIO_CONFIGGPIOMODE?GPIO
   *DEL*:           00001EH   BYTE   UNIT     CODE           ?PR?_GPIO_ENABLEINT?GPIO
   *DEL*:           000022H   BYTE   UNIT     CODE           ?PR?_GPIO_DISABLEINT?GPIO
   *DEL*:           000058H   BYTE   UNIT     CODE           ?PR?_GPIO_GETINTFLAG?GPIO
   *DEL*:           00004CH   BYTE   UNIT     CODE           ?PR?_GPIO_CLEARINTFLAG?GPIO
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_ENABLELVD?SYSTEM
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 6


   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_DISABLELVD?SYSTEM
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_SYS_CONFIGLVD?SYSTEM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_ENABLELVDINT?SYSTEM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_DISABLELVDINT?SYSTEM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?SYS_GETLVDINTFLAG?SYSTEM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_CLEARLVDINTFLAG?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_ENABLEWDTRESET?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_DISABLEWDTRESET?SYSTEM
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?SYS_GETWDTRESETFLAG?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_CLEARWDTRESETFLAG?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_ENABLESOFTWARERESET?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_DISABLESOFTWARERESET?SYSTEM
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?SYS_GETPOWERONRESETFLAG?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_CLEARPOWERONRESETFLAG?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_DISABLEWAKEUP?SYSTEM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?SYS_ENTERIDLE?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_ENABLEWAKEUPTRIG?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_DISABLEWAKEUPTRIG?SYSTEM
   *DEL*:           00000EH   BYTE   UNIT     CODE           ?PR?_SYS_CONFIGWUTCLK?SYSTEM
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_SYS_CONFIGWUTTIME?SYSTEM
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_TMR_ENABLEGATE?TIMER
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_TMR_DISABLEGATE?TIMER
   *DEL*:           000044H   BYTE   UNIT     CODE           ?PR?_TMR_GETCOUNTVALUE?TIMER
   *DEL*:           00001DH   BYTE   UNIT     CODE           ?PR?_TMR_DISABLEOVERFLOWINT?TIMER
   *DEL*:           000033H   BYTE   UNIT     CODE           ?PR?_TMR_GETOVERFLOWINTFLAG?TIMER
   *DEL*:           00001DH   BYTE   UNIT     CODE           ?PR?_TMR_CLEAROVERFLOWINTFLAG?TIMER
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGRUNMODE?TIMER
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGTIMERCLK?TIMER
   *DEL*:           000021H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGTIMERPERIOD?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_ENABLEGATE?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_DISABLEGATE?TIMER
   *DEL*:           00003DH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECOMPARE?TIMER
   *DEL*:           000025H   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECOMPARE?TIMER
   *DEL*:           000029H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGCOMPAREVALUE?TIMER
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGCOMPAREINTMODE?TIMER
   *DEL*:           00007BH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECAPTURE?TIMER
   *DEL*:           000025H   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECAPTURE?TIMER
   *DEL*:           00003CH   BYTE   UNIT     CODE           ?PR?_TMR2_GETCAPTUREVALUE?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_ENABLEOVERFLOWINT?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_DISABLEOVERFLOWINT?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_GETOVERFLOWINTFLAG?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_CLEAROVERFLOWINTFLAG?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_ENABLET2EXINT?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_DISABLET2EXINT?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_GETT2EXINTFLAG?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_CLEART2EXINTFLAG?TIMER
   *DEL*:           00000EH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECOMPAREINT?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECOMPAREINT?TIMER
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?_TMR2_GETCOMPAREINTFLAG?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_CLEARCOMPAREINTFLAG?TIMER
   *DEL*:           00000EH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECAPTUREINT?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECAPTUREINT?TIMER
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?_TMR2_GETCAPTUREINTFLAG?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_CLEARCAPTUREINTFLAG?TIMER
   *DEL*:           000003H   BYTE   UNIT     CODE           ?PR?TMR2_ALLINTENABLE?TIMER
   *DEL*:           000003H   BYTE   UNIT     CODE           ?PR?TMR2_ALLINTDISABLE?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_START?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_STOP?TIMER
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_UART_DISABLEDOUBLEFREQUENCY?UART
   *DEL*:           000010H   BYTE   UNIT     CODE           ?PR?_UART_CONFIGBAUDRATE?UART
   *DEL*:           000005H   BYTE   UNIT     XDATA          ?XD?_UART_CONFIGBAUDRATE?UART
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_UART_DISABLERECEIVE?UART
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_UART_DISABLEINT?UART
   *DEL*:           000025H   BYTE   UNIT     CODE           ?PR?_UART_GETSENDINTFLAG?UART
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_UART_CLEARSENDINTFLAG?UART
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 7


   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_UART_SENDBUFF?UART
   *DEL*:           000022H   BYTE   UNIT     CODE           ?PR?_UART_SENDNINTHBIT?UART
   *DEL*:           000017H   BYTE   UNIT     CODE           ?PR?_UART_GETNINTHBIT?UART
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?UART_DISABLEBRT?UART
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_WDT_CONFIGOVERFLOWTIME?WDT
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?WDT_CLEARWDT?WDT
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?WDT_ENABLEOVERFLOWINT?WDT
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?WDT_DISABLEOVERFLOWINT?WDT
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?WDT_GETOVERFLOWINTFLAG?WDT
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?WDT_CLEAROVERFLOWINTFLAG?WDT
   *DEL*:           00001AH   BYTE   UNIT     CODE           ?PR?INIT_RAM_VARIANT?DEFINE
   *DEL*:           00000DH   BYTE   UNIT     CODE           ?PR?_PUTCHAR?UART_INIT
   *DEL*:           00000BH   BYTE   UNIT     CODE           ?PR?GETCHAR?UART_INIT
   *DEL*:           000016H   BYTE   UNIT     CODE           ?PR?_PUTS?UART_INIT
   *DEL*:           0000ADH   BYTE   UNIT     CODE           ?PR?_FUNCTION_STRCAT_PLUS_ASSIGN?UART_FUNCTION
   *DEL*:           00000EH   BYTE   UNIT     XDATA          ?XD?_FUNCTION_STRCAT_PLUS_ASSIGN?UART_FUNCTION
   *DEL*:           00013BH   BYTE   UNIT     CODE           ?PR?_ADC?ADC_USED
   *DEL*:           00000FH   BYTE   UNIT     XDATA          ?XD?_ADC?ADC_USED
   *DEL*:           00001CH   BYTE   UNIT     CONST          ?CO?ADC_USED
   *DEL*:           000483H   BYTE   UNIT     CODE           ?PR?PRINTF?PRINTF
   *DEL*:           000005H   BYTE   UNIT     DATA           ?DT?PRINTF?PRINTF
   *DEL*:           000001H.1 BIT    UNIT     BIT            ?BI?PRINTF?PRINTF
   *DEL*:           000030H   BYTE   UNIT     XDATA          ?XD?PRINTF?PRINTF



OVERLAY MAP OF MODULE:   .\Objects\Project (?C_STARTUP)


FUNCTION/MODULE                                BIT_GROUP   XDATA_GROUP
--> CALLED FUNCTION/MODULE                    START  STOP  START  STOP
======================================================================
?C_C51STARTUP                                 ----- -----  ----- -----
  +--> MAIN/MAIN
  +--> ?C_INITSEG

MAIN/MAIN                                     25H.6 26H.1  0033H 0043H
  +--> GPIO_CONFIG/GPIO_INIT
  +--> _DELAY1MS/MAIN
  +--> ADC_CONFIG/ADC_INIT
  +--> UART_1_CONFIG/UART_INIT
  +--> UART_0_CONFIG/UART_INIT
  +--> TMR0_CONFIG/TIMER_INIT
  +--> TMR1_CONFIG/TIMER_INIT
  +--> RESTORE_DLY/MAIN
  +--> _STORE_DLY/MAIN
  +--> GPIO_KEY_INTERRUPT_CONFIG/GPIO_INIT
  +--> KEY_SCAN/KEY
  +--> KEY_BUFF_RETURN/KEY
  +--> _FUNCTION_UART_SEND_CMD/UART_FUNCTION
  +--> UART_DATA_INIT/UART_FUNCTION
  +--> BATTERY_CHECK/MAIN
  +--> LED_CONTROL/MAIN
  +--> _TMR_STOP/TIMER
  +--> _TMR_CONFIGTIMERPERIOD/TIMER
  +--> _TMR_START/TIMER
  +--> _MOTOR_STEP_CONTROL/MAIN
  +--> KEY_INTERRUPT_PROCESS/MAIN
  +--> _KEY_FUNCTION_SWITCH_SYSTEM/MAIN
  +--> UART_DATA_PROCESS/UART_FUNCTION
  +--> SYS_ENABLEWAKEUP/SYSTEM
  +--> SYS_ENTERSTOP/SYSTEM

GPIO_CONFIG/GPIO_INIT                         ----- -----  ----- -----

LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 8


_DELAY1MS/MAIN                                ----- -----  ----- -----

ADC_CONFIG/ADC_INIT                           ----- -----  ----- -----
  +--> _ADC_CONFIGRUNMODE/ADC
  +--> _ADC_ENABLECHANNEL/ADC
  +--> _ADC_CONFIGADCVREF/ADC
  +--> ADC_START/ADC

_ADC_CONFIGRUNMODE/ADC                        ----- -----  ----- -----

_ADC_ENABLECHANNEL/ADC                        ----- -----  ----- -----

_ADC_CONFIGADCVREF/ADC                        ----- -----  ----- -----

ADC_START/ADC                                 ----- -----  ----- -----

UART_1_CONFIG/UART_INIT                       ----- -----  0044H 0049H
  +--> _UART_CONFIGRUNMODE/UART
  +--> _UART_ENABLERECEIVE/UART
  +--> _UART_CONFIGBRTCLK/UART
  +--> _UART_ENABLEDOUBLEFREQUENCY/UART
  +--> _UART_CONFIGBRTPERIOD/UART
  +--> UART_ENABLEBRT/UART

_UART_CONFIGRUNMODE/UART                      ----- -----  ----- -----

_UART_ENABLERECEIVE/UART                      ----- -----  ----- -----

_UART_CONFIGBRTCLK/UART                       ----- -----  ----- -----

_UART_ENABLEDOUBLEFREQUENCY/UART              ----- -----  ----- -----

_UART_CONFIGBRTPERIOD/UART                    ----- -----  ----- -----

UART_ENABLEBRT/UART                           ----- -----  ----- -----

UART_0_CONFIG/UART_INIT                       ----- -----  0044H 0049H
  +--> _UART_CONFIGRUNMODE/UART
  +--> _UART_ENABLERECEIVE/UART
  +--> _UART_CONFIGBRTCLK/UART
  +--> _UART_ENABLEDOUBLEFREQUENCY/UART
  +--> _UART_CONFIGBRTPERIOD/UART
  +--> UART_ENABLEBRT/UART
  +--> _UART_ENABLEINT/UART

_UART_ENABLEINT/UART                          ----- -----  ----- -----

TMR0_CONFIG/TIMER_INIT                        ----- -----  ----- -----
  +--> _TMR_CONFIGRUNMODE/TIMER
  +--> _TMR_CONFIGTIMERCLK/TIMER
  +--> _TMR_CONFIGTIMERPERIOD/TIMER
  +--> _TMR_ENABLEOVERFLOWINT/TIMER
  +--> _TMR_START/TIMER

_TMR_CONFIGRUNMODE/TIMER                      ----- -----  ----- -----

_TMR_CONFIGTIMERCLK/TIMER                     ----- -----  ----- -----

_TMR_CONFIGTIMERPERIOD/TIMER                  ----- -----  ----- -----

_TMR_ENABLEOVERFLOWINT/TIMER                  ----- -----  ----- -----

_TMR_START/TIMER                              ----- -----  ----- -----

TMR1_CONFIG/TIMER_INIT                        ----- -----  ----- -----
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 9


  +--> _TMR_CONFIGRUNMODE/TIMER
  +--> _TMR_CONFIGTIMERCLK/TIMER
  +--> _TMR_CONFIGTIMERPERIOD/TIMER
  +--> _TMR_ENABLEOVERFLOWINT/TIMER
  +--> _TMR_START/TIMER

RESTORE_DLY/MAIN                              ----- -----  0044H 0045H
  +--> FLASH_UNLOCK/FLASH
  +--> _FLASH_READ/FLASH
  +--> FLASH_LOCK/FLASH

FLASH_UNLOCK/FLASH                            ----- -----  ----- -----

_FLASH_READ/FLASH                             ----- -----  ----- -----

FLASH_LOCK/FLASH                              ----- -----  ----- -----

_STORE_DLY/MAIN                               ----- -----  0044H 0045H
  +--> FLASH_UNLOCK/FLASH
  +--> _FLASH_ERASE/FLASH
  +--> _FLASH_WRITE/FLASH
  +--> FLASH_LOCK/FLASH

_FLASH_ERASE/FLASH                            ----- -----  ----- -----

_FLASH_WRITE/FLASH                            ----- -----  ----- -----

GPIO_KEY_INTERRUPT_CONFIG/GPIO_INIT           ----- -----  ----- -----

KEY_SCAN/KEY                                  ----- -----  ----- -----

KEY_BUFF_RETURN/KEY                           ----- -----  ----- -----

_FUNCTION_UART_SEND_CMD/UART_FUNCTION         ----- -----  0044H 0051H
  +--> _UART_SEND_STRING/UART_INIT

_UART_SEND_STRING/UART_INIT                   ----- -----  0052H 0056H

UART_DATA_INIT/UART_FUNCTION                  ----- -----  ----- -----

BATTERY_CHECK/MAIN                            ----- -----  ----- -----
  +--> _ADC_STARTCONVERT/ADC_USED
  +--> ADC_GETCONVERTINTFLAG/ADC_USED
  +--> ADC_GETRESULT/ADC_USED
  +--> ADC_CLEARCONVERTINTFLAG/ADC_USED

_ADC_STARTCONVERT/ADC_USED                    ----- -----  ----- -----
  +--> _ADC_ENABLECHANNEL/ADC

ADC_GETCONVERTINTFLAG/ADC_USED                ----- -----  ----- -----

ADC_GETRESULT/ADC_USED                        ----- -----  ----- -----
  +--> ADC_GETADCRESULT/ADC

ADC_GETADCRESULT/ADC                          ----- -----  ----- -----

ADC_CLEARCONVERTINTFLAG/ADC_USED              ----- -----  ----- -----

LED_CONTROL/MAIN                              ----- -----  ----- -----

_TMR_STOP/TIMER                               ----- -----  ----- -----

_MOTOR_STEP_CONTROL/MAIN                      ----- -----  ----- -----

KEY_INTERRUPT_PROCESS/MAIN                    ----- -----  ----- -----
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 10


  +--> _KEY_FUNCTION_SWITCH_SYSTEM/MAIN

_KEY_FUNCTION_SWITCH_SYSTEM/MAIN              ----- -----  ----- -----

UART_DATA_PROCESS/UART_FUNCTION               ----- -----  ----- -----
  +--> RETURN_UART_DATA_LENGTH/UART_FUNCTION
  +--> CLEAN_UART_DATA_LENGTH/UART_FUNCTION

RETURN_UART_DATA_LENGTH/UART_FUNCTION         ----- -----  ----- -----

CLEAN_UART_DATA_LENGTH/UART_FUNCTION          ----- -----  ----- -----

SYS_ENABLEWAKEUP/SYSTEM                       ----- -----  ----- -----

SYS_ENTERSTOP/SYSTEM                          ----- -----  ----- -----

?C_INITSEG                                    ----- -----  ----- -----

*** NEW ROOT ********************************

INT0_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER0_IRQHANDLER/ISR                         ----- -----  ----- -----

*** NEW ROOT ********************************

INT1_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER1_IRQHANDLER/ISR                         ----- -----  ----- -----

*** NEW ROOT ********************************

UART0_IRQHANDLER/ISR                          ----- -----  ----- -----
  +--> _UART_GETRECEIVEINTFLAG/UART
  +--> _UART_GETBUFF/UART
  +--> _UART_DATA_COPY/UART_FUNCTION
  +--> _UART_CLEARRECEIVEINTFLAG/UART

_UART_GETRECEIVEINTFLAG/UART                  ----- -----  ----- -----

_UART_GETBUFF/UART                            ----- -----  ----- -----

_UART_DATA_COPY/UART_FUNCTION                 ----- -----  ----- -----

_UART_CLEARRECEIVEINTFLAG/UART                ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER2_IRQHANDLER/ISR                         ----- -----  ----- -----

*** NEW ROOT ********************************

UART1_IRQHANDLER/ISR                          ----- -----  ----- -----

*** NEW ROOT ********************************

P0EI_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

P1EI_IRQHANDLER/ISR                           ----- -----  ----- -----
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 11



*** NEW ROOT ********************************

P2EI_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

P3EI_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

LVD_IRQHANDLER/ISR                            ----- -----  ----- -----

*** NEW ROOT ********************************

LSE_IRQHANDLER/ISR                            ----- -----  ----- -----

*** NEW ROOT ********************************

ACMP_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER3_IRQHANDLER/ISR                         ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER4_IRQHANDLER/ISR                         ----- -----  ----- -----

*** NEW ROOT ********************************

EPWM_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

ADC_IRQHANDLER/ISR                            ----- -----  ----- -----

*** NEW ROOT ********************************

WDT_IRQHANDLER/ISR                            ----- -----  ----- -----

*** NEW ROOT ********************************

I2C_IRQHANDLER/ISR                            ----- -----  ----- -----

*** NEW ROOT ********************************

SPI_IRQHANDLER/ISR                            ----- -----  ----- -----



PUBLIC SYMBOLS OF MODULE:  .\Objects\Project (?C_STARTUP)


      VALUE       CLASS    TYPE      PUBLIC SYMBOL NAME
      =================================================
*DEL*:00000000H   XDATA    BYTE      ?_Function_Strcat_Plus_Assign?BYTE
*DEL*:00000000H   XDATA    ---       ?_PRINTF?BYTE
*DEL*:00000000H   XDATA    ---       ?_SPRINTF?BYTE
*DEL*:00000000H   XDATA    BYTE      ?_UART_ConfigBaudRate?BYTE
      02000052H   XDATA    BYTE      ?_UART_Send_String?BYTE
      010011B9H   CODE     ---       ?C?CCASE
      01000FB4H   CODE     ---       ?C?CLDOPTR
      01000F9BH   CODE     ---       ?C?CLDPTR
      00000000H   NUMBER   ---       ?C?CODESEG
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 12


      01000F75H   CODE     ---       ?C?COPY
      01000FF3H   CODE     ---       ?C?CSTOPTR
      01000FE1H   CODE     ---       ?C?CSTPTR
      01000B7DH   CODE     ---       ?C?FCASTC
      01000B78H   CODE     ---       ?C?FCASTI
      01000B73H   CODE     ---       ?C?FCASTL
      01000D44H   CODE     ---       ?C?FPADD
      01000C38H   CODE     ---       ?C?FPCONVERT
      01000AD6H   CODE     ---       ?C?FPDIV
      01000BB1H   CODE     ---       ?C?FPGETOPN2
      010009CDH   CODE     ---       ?C?FPMUL
      01000BE6H   CODE     ---       ?C?FPNANRESULT
      01000BF0H   CODE     ---       ?C?FPOVERFLOW
      01000BC8H   CODE     ---       ?C?FPRESULT
      01000BDCH   CODE     ---       ?C?FPRESULT2
      01000BFBH   CODE     ---       ?C?FPROUND
      01000D40H   CODE     ---       ?C?FPSUB
      01000BEDH   CODE     ---       ?C?FPUNDERFLOW
      01000E65H   CODE     ---       ?C?FTNPWR
      0100106AH   CODE     ---       ?C?ILDIX
      0100114EH   CODE     ---       ?C?LNEG
      01001168H   CODE     ---       ?C?LSTKXDATA
      0100115CH   CODE     ---       ?C?LSTXDATA
      01001199H   CODE     ---       ?C?PLDIXDATA
      010011B0H   CODE     ---       ?C?PSTXDATA
      01001015H   CODE     ---       ?C?UIDIV
      010010BCH   CODE     ---       ?C?ULDIV
      00000000H   NUMBER   ---       ?C?XDATASEG
      010019C9H   CODE     ---       ?C_START
      01000000H   CODE     ---       ?C_STARTUP
*DEL*:00000000H   CODE     ---       _ADC
*DEL*:00000000H   CODE     ---       _ADC_ConfigADCBrake
      01002254H   CODE     ---       _ADC_ConfigADCVref
*DEL*:00000000H   CODE     ---       _ADC_ConfigAN31
*DEL*:00000000H   CODE     ---       _ADC_ConfigCompareValue
*DEL*:00000000H   CODE     ---       _ADC_ConfigHardwareTrig
      010021E0H   CODE     ---       _ADC_ConfigRunMode
      0100218BH   CODE     ---       _ADC_EnableChannel
*DEL*:00000000H   CODE     ---       _ADC_SetTrigDelayTime
      0100226FH   CODE     ---       _ADC_StartConvert
      0100210EH   CODE     ---       _Delay1ms
*DEL*:00000000H   CODE     ---       _EPWM_ClearDownCmpIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_ClearPeriodIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_ClearUpCmpIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_ClearZeroIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelAsymDuty
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelBrakeLevel
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelClk
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelPeriod
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelSymDuty
*DEL*:00000000H   CODE     ---       _EPWM_ConfigFBBrake
*DEL*:00000000H   CODE     ---       _EPWM_ConfigRunMode
*DEL*:00000000H   CODE     ---       _EPWM_DisableDeadZone
*DEL*:00000000H   CODE     ---       _EPWM_DisableDownCmpInt
*DEL*:00000000H   CODE     ---       _EPWM_DisableFaultBrake
*DEL*:00000000H   CODE     ---       _EPWM_DisableMaskControl
*DEL*:00000000H   CODE     ---       _EPWM_DisableOutput
*DEL*:00000000H   CODE     ---       _EPWM_DisablePeriodInt
*DEL*:00000000H   CODE     ---       _EPWM_DisableReverseOutput
*DEL*:00000000H   CODE     ---       _EPWM_DisableUpCmpInt
*DEL*:00000000H   CODE     ---       _EPWM_DisableZeroInt
*DEL*:00000000H   CODE     ---       _EPWM_EnableAutoLoadMode
*DEL*:00000000H   CODE     ---       _EPWM_EnableDeadZone
*DEL*:00000000H   CODE     ---       _EPWM_EnableDownCmpInt
*DEL*:00000000H   CODE     ---       _EPWM_EnableFaultBrake
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 13


*DEL*:00000000H   CODE     ---       _EPWM_EnableMaskControl
*DEL*:00000000H   CODE     ---       _EPWM_EnableOneShotMode
*DEL*:00000000H   CODE     ---       _EPWM_EnableOutput
*DEL*:00000000H   CODE     ---       _EPWM_EnablePeriodInt
*DEL*:00000000H   CODE     ---       _EPWM_EnableReverseOutput
*DEL*:00000000H   CODE     ---       _EPWM_EnableUpCmpInt
*DEL*:00000000H   CODE     ---       _EPWM_EnableZeroInt
*DEL*:00000000H   CODE     ---       _EPWM_GetDownCmpIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_GetPeriodIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_GetUpCmpIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_GetZeroIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_Start
*DEL*:00000000H   CODE     ---       _EPWM_Stop
      01001F62H   CODE     ---       _FLASH_Erase
      01001F2EH   CODE     ---       _FLASH_Read
      01001EF9H   CODE     ---       _FLASH_Write
*DEL*:00000000H   CODE     ---       _Function_Strcat_Plus_Assign
      01001AFEH   CODE     ---       _Function_UART_Send_CMD
*DEL*:00000000H   CODE     ---       _GPIO_ClearIntFlag
*DEL*:00000000H   CODE     ---       _GPIO_ConfigGPIOMode
*DEL*:00000000H   CODE     ---       _GPIO_DisableInt
*DEL*:00000000H   CODE     ---       _GPIO_EnableInt
*DEL*:00000000H   CODE     ---       _GPIO_GetIntFlag
      01001E82H   CODE     ---       _Key_Function_Switch_System
      01001BE1H   CODE     ---       _Motor_Step_Control
*DEL*:0000006BH   CODE     ---       _PRINTF
*DEL*:00000000H   CODE     ---       _putchar
*DEL*:00000000H   CODE     ---       _puts
*DEL*:00000065H   CODE     ---       _SPRINTF
      01001F93H   CODE     ---       _Store_dly
*DEL*:00000000H   CODE     ---       _SYS_ConfigLVD
*DEL*:00000000H   CODE     ---       _SYS_ConfigWUTCLK
*DEL*:00000000H   CODE     ---       _SYS_ConfigWUTTime
*DEL*:00000000H   CODE     ---       _TMR2_ClearCaptureIntFlag
*DEL*:00000000H   CODE     ---       _TMR2_ClearCompareIntFlag
*DEL*:00000000H   CODE     ---       _TMR2_ConfigCompareIntMode
*DEL*:00000000H   CODE     ---       _TMR2_ConfigCompareValue
*DEL*:00000000H   CODE     ---       _TMR2_ConfigRunMode
*DEL*:00000000H   CODE     ---       _TMR2_ConfigTimerClk
*DEL*:00000000H   CODE     ---       _TMR2_ConfigTimerPeriod
*DEL*:00000000H   CODE     ---       _TMR2_DisableCapture
*DEL*:00000000H   CODE     ---       _TMR2_DisableCaptureInt
*DEL*:00000000H   CODE     ---       _TMR2_DisableCompare
*DEL*:00000000H   CODE     ---       _TMR2_DisableCompareInt
*DEL*:00000000H   CODE     ---       _TMR2_EnableCapture
*DEL*:00000000H   CODE     ---       _TMR2_EnableCaptureInt
*DEL*:00000000H   CODE     ---       _TMR2_EnableCompare
*DEL*:00000000H   CODE     ---       _TMR2_EnableCompareInt
*DEL*:00000000H   CODE     ---       _TMR2_GetCaptureIntFlag
*DEL*:00000000H   CODE     ---       _TMR2_GetCaptureValue
*DEL*:00000000H   CODE     ---       _TMR2_GetCompareIntFlag
*DEL*:00000000H   CODE     ---       _TMR_ClearOverflowIntFlag
      01001DADH   CODE     ---       _TMR_ConfigRunMode
      01001DF9H   CODE     ---       _TMR_ConfigTimerClk
      01002013H   CODE     ---       _TMR_ConfigTimerPeriod
*DEL*:00000000H   CODE     ---       _TMR_DisableGATE
*DEL*:00000000H   CODE     ---       _TMR_DisableOverflowInt
*DEL*:00000000H   CODE     ---       _TMR_EnableGATE
      010021A9H   CODE     ---       _TMR_EnableOverflowInt
*DEL*:00000000H   CODE     ---       _TMR_GetCountValue
*DEL*:00000000H   CODE     ---       _TMR_GetOverflowIntFlag
      0100212EH   CODE     ---       _TMR_Start
      0100214DH   CODE     ---       _TMR_Stop
      0100216CH   CODE     ---       _UART_ClearReceiveIntFlag
*DEL*:00000000H   CODE     ---       _UART_ClearSendIntFlag
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 14


*DEL*:00000000H   CODE     ---       _UART_ConfigBaudRate
      0100225DH   CODE     ---       _UART_ConfigBRTClk
      01002266H   CODE     ---       _UART_ConfigBRTPeriod
      01001C47H   CODE     ---       _UART_ConfigRunMode
      010020CEH   CODE     ---       _UART_Data_Copy
*DEL*:00000000H   CODE     ---       _UART_DisableDoubleFrequency
*DEL*:00000000H   CODE     ---       _UART_DisableInt
*DEL*:00000000H   CODE     ---       _UART_DisableReceive
      0100220AH   CODE     ---       _UART_EnableDoubleFrequency
      01002243H   CODE     ---       _UART_EnableInt
      0100221DH   CODE     ---       _UART_EnableReceive
      010021F6H   CODE     ---       _UART_GetBuff
*DEL*:00000000H   CODE     ---       _UART_GetNinthBit
      01002085H   CODE     ---       _UART_GetReceiveIntFlag
*DEL*:00000000H   CODE     ---       _UART_GetSendIntFlag
      01001CA6H   CODE     ---       _UART_Send_String
*DEL*:00000000H   CODE     ---       _UART_SendBuff
*DEL*:00000000H   CODE     ---       _UART_SendNinthBit
*DEL*:00000000H   CODE     ---       _WDT_ConfigOverflowTime
*SFR* 000000D0H.6 DATA     BIT       AC
*SFR* 000000E0H   DATA     BYTE      ACC
      01000030H   CODE     ---       ACMP_IRQHandler
      0100003EH   CODE     ---       ADC_ClearConvertIntFlag
*DEL*:00000000H   CODE     ---       ADC_ClearIntFlag
      010021C6H   CODE     ---       ADC_Config
*DEL*:00000000H   CODE     ---       ADC_DisableHardwareTrig
*DEL*:00000000H   CODE     ---       ADC_DisableInt
*DEL*:00000000H   CODE     ---       ADC_DisableLDO
*DEL*:00000000H   CODE     ---       ADC_EnableHardwareTrig
*DEL*:00000000H   CODE     ---       ADC_EnableInt
*DEL*:00000000H   CODE     ---       ADC_EnableLDO
      01002060H   CODE     ---       ADC_GetADCResult
*DEL*:00000000H   CODE     ---       ADC_GetCmpResult
      01000086H   CODE     ---       ADC_GetConvertIntFlag
*DEL*:00000000H   CODE     ---       ADC_GetIntFlag
      01000026H   CODE     ---       ADC_GetResult
      01000037H   CODE     ---       ADC_IRQHandler
      01000006H   CODE     ---       ADC_Start
*DEL*:00000000H   CODE     ---       ADC_Stop
*SFR* 000000D1H   DATA     BYTE      ADCMPC
*SFR* 000000D5H   DATA     BYTE      ADCMPH
*SFR* 000000D4H   DATA     BYTE      ADCMPL
*SFR* 000000DFH   DATA     BYTE      ADCON0
*SFR* 000000DEH   DATA     BYTE      ADCON1
*SFR* 000000E9H   DATA     BYTE      ADCON2
*SFR* 000000D3H   DATA     BYTE      ADDLYL
*SFR* 000000DDH   DATA     BYTE      ADRESH
*SFR* 000000DCH   DATA     BYTE      ADRESL
      00000020H.1 BIT      BIT       auto_rotate_entry_complete
      00000021H.2 BIT      BIT       auto_rotate_flash
      02000014H   XDATA    WORD      auto_rotate_flash_timer
      00000021H.7 BIT      BIT       auto_rotate_mode
      00000020H.6 BIT      BIT       auto_rotate_running
*SFR* 000000F0H   DATA     BYTE      B
      00000020H.2 BIT      BIT       batlow
      00000022H.0 BIT      BIT       batlow1
      0200002DH   XDATA    BYTE      batlow1_cnt
      0200001BH   XDATA    BYTE      batlow_cnt
      02000088H   XDATA    WORD      Battery_ADC_Wait_Time
      010018A9H   CODE     ---       Battery_Check
      0200001AH   XDATA    BYTE      battery_check_divider
      0200002EH   XDATA    WORD      BatV
      00000023H.0 BIT      BIT       Bit_1_ms_Buff
      00000024H.0 BIT      BIT       Bit_N_ms_Buff
      00000023H.4 BIT      BIT       Bit_Toggle
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 15


*SFR* 000000BFH   DATA     BYTE      BUZCON
*SFR* 000000BEH   DATA     BYTE      BUZDIV
*SFR* 000000C8H.5 DATA     BIT       CAPES
*SFR* 000000CEH   DATA     BYTE      CCEN
*SFR* 000000C3H   DATA     BYTE      CCH1
*SFR* 000000C5H   DATA     BYTE      CCH2
*SFR* 000000C7H   DATA     BYTE      CCH3
*SFR* 000000C2H   DATA     BYTE      CCL1
*SFR* 000000C4H   DATA     BYTE      CCL2
*SFR* 000000C6H   DATA     BYTE      CCL3
      00000025H.3 BIT      BIT       Center_Line_Control
      00000021H.6 BIT      BIT       Charg_State_Buff
      00000023H.2 BIT      BIT       charge_flash
      02000005H   XDATA    WORD      charge_flash_cnt
      00000022H.6 BIT      BIT       Charge_Was_Connected
*SFR* 0000008EH   DATA     BYTE      CKCON
      01002286H   CODE     ---       Clean_UART_Data_Length
*SFR* 0000008FH   DATA     BYTE      CLKDIV
      0200001CH   XDATA    INT       Count_1_Degree_Pulse
      02000083H   XDATA    INT       Count_Toggle
*SFR* 000000D0H.7 DATA     BIT       CY
      02000077H   XDATA    BYTE      Data_Length
      00000023H.3 BIT      BIT       Delay_Open
      00000025H.5 BIT      BIT       Delay_Over
      02000085H   XDATA    WORD      Delay_Time
      02000081H   XDATA    WORD      Delay_Time_Count
      00000024H.1 BIT      BIT       direction_changed
      02000022H   XDATA    WORD      dly
*SFR* 00000083H   DATA     BYTE      DPH0
*SFR* 00000085H   DATA     BYTE      DPH1
*SFR* 00000082H   DATA     BYTE      DPL0
*SFR* 00000084H   DATA     BYTE      DPL1
*SFR* 00000086H   DATA     BYTE      DPS
*SFR* 00000093H   DATA     BYTE      DPX0
*SFR* 00000095H   DATA     BYTE      DPX1
*SFR* 000000A8H.7 DATA     BIT       EA
*SFR* 000000AAH   DATA     BYTE      EIE2
*SFR* 000000B2H   DATA     BYTE      EIF2
*SFR* 000000B9H   DATA     BYTE      EIP1
*SFR* 000000BAH   DATA     BYTE      EIP2
*DEL*:00000000H   CODE     ---       EPWM_AllIntDisable
*DEL*:00000000H   CODE     ---       EPWM_AllIntEnable
*DEL*:00000000H   CODE     ---       EPWM_ClearFaultBrakeIntFlag
*DEL*:00000000H   CODE     ---       EPWM_DisableFaultBrakeInt
*DEL*:00000000H   CODE     ---       EPWM_DisableSoftwareBrake
*DEL*:00000000H   CODE     ---       EPWM_EnableFaultBrakeInt
*DEL*:00000000H   CODE     ---       EPWM_GetFaultBrakeIntFlag
      01000036H   CODE     ---       EPWM_IRQHandler
*DEL*:00000000H   CODE     ---       EPWM_TrigSoftwareBrake
*SFR* 000000A8H.4 DATA     BIT       ES0
*SFR* 000000A8H.6 DATA     BIT       ES1
*SFR* 000000A8H.1 DATA     BIT       ET0
*SFR* 000000A8H.3 DATA     BIT       ET1
*SFR* 000000A8H.5 DATA     BIT       ET2
*SFR* 000000A8H.0 DATA     BIT       EX0
*SFR* 000000A8H.2 DATA     BIT       EX1
*SFR* 000000D0H.5 DATA     BIT       F0
      0100001EH   CODE     ---       FLASH_Lock
      01000016H   CODE     ---       FLASH_UnLock
*SFR* 00000091H   DATA     BYTE      FUNCCR
      00000025H.4 BIT      BIT       Get_String_Buff
      02000087H   XDATA    BYTE      Get_String_Wait_Time
*DEL*:00000000H   CODE     ---       getchar
      01001B71H   CODE     ---       GPIO_Config
      01002230H   CODE     ---       GPIO_Key_Interrupt_Config
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 16


      01000039H   CODE     ---       I2C_IRQHandler
*SFR* 000000F6H   DATA     BYTE      I2CMBUF
*SFR* 000000F5H   DATA     BYTE      I2CMCR
*SFR* 000000F4H   DATA     BYTE      I2CMSA
*SFR* 000000F5H   DATA     BYTE      I2CMSR
*SFR* 000000F7H   DATA     BYTE      I2CMTP
*SFR* 000000F1H   DATA     BYTE      I2CSADR
*SFR* 000000F3H   DATA     BYTE      I2CSBUF
*SFR* 000000F2H   DATA     BYTE      I2CSCR
*SFR* 000000F2H   DATA     BYTE      I2CSSR
*SFR* 000000C8H.6 DATA     BIT       I3FR
*SFR* 000000A8H   DATA     BYTE      IE
*SFR* 00000088H.1 DATA     BIT       IE0
*SFR* 00000088H.3 DATA     BIT       IE1
*DEL*:00000000H   CODE     ---       Init_RAM_Variant
      0100000AH   CODE     ---       INT0_IRQHandler
      01000012H   CODE     ---       INT1_IRQHandler
*SFR* 000000B8H   DATA     BYTE      IP
*SFR* 00000088H.0 DATA     BIT       IT0
*SFR* 00000088H.2 DATA     BIT       IT1
      0200007AH   XDATA    BYTE      K1_cnt
      00000020H.7 BIT      BIT       K1_cnt_EN
      02000099H   XDATA    BYTE      K1_Count
      00000024H.5 BIT      BIT       K1_Press
      0200007BH   XDATA    BYTE      K2_cnt
      00000021H.0 BIT      BIT       K2_cnt_EN
      0200009AH   XDATA    BYTE      K2_Count
      00000023H.5 BIT      BIT       k2_long_press_detected
      0200001EH   XDATA    WORD      k2_long_press_timer
      00000024H.6 BIT      BIT       K2_Press
      00000022H.1 BIT      BIT       k2_released
      0200007CH   XDATA    BYTE      K3_cnt
      00000021H.1 BIT      BIT       K3_cnt_EN
      0200009BH   XDATA    BYTE      K3_Count
      00000024H.7 BIT      BIT       K3_Press
      00000022H.2 BIT      BIT       k3_released
      0200009CH   XDATA    BYTE      K4_Count
      00000025H.0 BIT      BIT       K4_Press
      0200009DH   XDATA    BYTE      K5_Count
      00000025H.1 BIT      BIT       K5_Press
      02000020H   XDATA    WORD      key1_duration
      00000022H.3 BIT      BIT       key1_handle
      00000023H.6 BIT      BIT       key1_long_started
      0200000CH   XDATA    WORD      key1_press_time
      00000022H.7 BIT      BIT       key1_pressed
      02000024H   XDATA    WORD      key3_duration
      00000022H.4 BIT      BIT       key3_handle
      00000023H.7 BIT      BIT       key3_long_started
      0200000EH   XDATA    WORD      key3_press_time
      00000023H.1 BIT      BIT       key3_pressed
      02000098H   XDATA    BYTE      Key_Buff
      01001EBFH   CODE     ---       Key_Buff_Return
      00000020H.4 BIT      BIT       key_control_active
      010011DFH   CODE     ---       Key_Interrupt_Process
      00000020H.3 BIT      BIT       Key_Long_Press
      0100134AH   CODE     ---       Key_Scan
      0200000BH   XDATA    BYTE      key_scan_divider
      00000024H.2 BIT      BIT       key_short_press_mode
      0200002CH   XDATA    BYTE      last_direction
      010016A8H   CODE     ---       LED_Control
      00000021H.5 BIT      BIT       led_flash_state
      02000018H   XDATA    WORD      led_flash_timer
      00000022H.5 BIT      BIT       ledonoff
      00000020H.0 BIT      BIT       ledonoff1
      02000017H   XDATA    BYTE      ledonoff1_cnt
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 17


      02000032H   XDATA    BYTE      ledonoff_cnt
      00000024H.3 BIT      BIT       longhit
      02000078H   XDATA    WORD      longhit_cnt
      0100002FH   CODE     ---       LSE_IRQHandler
      0100002EH   CODE     ---       LVD_IRQHandler
*SFR* 000000FDH   DATA     BYTE      MADRH
*SFR* 000000FCH   DATA     BYTE      MADRL
      010000B6H   CODE     ---       main
      02000016H   XDATA    BYTE      main_loop_counter
*SFR* 000000FFH   DATA     BYTE      MCTRL
*SFR* 000000FEH   DATA     BYTE      MDATA
*SFR* 000000FBH   DATA     BYTE      MLOCK
      0200008CH   XDATA    BYTE      Motor_Direction_Data
      00000020H.5 BIT      BIT       MOTOR_RUNNING_FLAG
      0200007FH   XDATA    WORD      Motor_Speed_Data
      00000021H.4 BIT      BIT       need_led_flash
      0200007DH   XDATA    INT       Num
      0200009EH   XDATA    INT       Num_Forward_Pulse
      020000A0H   XDATA    INT       Num_Reverse_Pulse
      02000029H   XDATA    WORD      original_speed
*SFR* 000000D0H.2 DATA     BIT       OV
*SFR* 000000D0H.0 DATA     BIT       P
*SFR* 00000080H   DATA     BYTE      P0
*SFR* 00000080H.0 DATA     BIT       P00
*SFR* 00000080H.1 DATA     BIT       P01
*SFR* 00000080H.2 DATA     BIT       P02
*SFR* 00000080H.3 DATA     BIT       P03
*SFR* 00000080H.4 DATA     BIT       P04
*SFR* 00000080H.5 DATA     BIT       P05
*SFR* 00000080H.6 DATA     BIT       P06
*SFR* 00000080H.7 DATA     BIT       P07
      01000029H   CODE     ---       P0EI_IRQHandler
*SFR* 000000ACH   DATA     BYTE      P0EXTIE
*SFR* 000000B4H   DATA     BYTE      P0EXTIF
*SFR* 0000009AH   DATA     BYTE      P0TRIS
*SFR* 00000090H   DATA     BYTE      P1
*SFR* 00000090H.0 DATA     BIT       P10
*SFR* 00000090H.1 DATA     BIT       P11
*SFR* 00000090H.2 DATA     BIT       P12
*SFR* 00000090H.3 DATA     BIT       P13
*SFR* 00000090H.4 DATA     BIT       P14
*SFR* 00000090H.5 DATA     BIT       P15
*SFR* 00000090H.6 DATA     BIT       P16
*SFR* 00000090H.7 DATA     BIT       P17
      01001A0EH   CODE     ---       P1EI_IRQHandler
*SFR* 000000ADH   DATA     BYTE      P1EXTIE
*SFR* 000000B5H   DATA     BYTE      P1EXTIF
*SFR* 000000A1H   DATA     BYTE      P1TRIS
*SFR* 000000A0H   DATA     BYTE      P2
*SFR* 000000A0H.0 DATA     BIT       P20
*SFR* 000000A0H.1 DATA     BIT       P21
*SFR* 000000A0H.2 DATA     BIT       P22
*SFR* 000000A0H.3 DATA     BIT       P23
*SFR* 000000A0H.4 DATA     BIT       P24
*SFR* 000000A0H.5 DATA     BIT       P25
*SFR* 000000A0H.6 DATA     BIT       P26
*SFR* 000000A0H.7 DATA     BIT       P27
      01001A86H   CODE     ---       P2EI_IRQHandler
*SFR* 000000AEH   DATA     BYTE      P2EXTIE
*SFR* 000000B6H   DATA     BYTE      P2EXTIF
*SFR* 000000A2H   DATA     BYTE      P2TRIS
*SFR* 000000B0H   DATA     BYTE      P3
*SFR* 000000B0H.0 DATA     BIT       P30
*SFR* 000000B0H.1 DATA     BIT       P31
*SFR* 000000B0H.2 DATA     BIT       P32
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 18


*SFR* 000000B0H.3 DATA     BIT       P33
*SFR* 000000B0H.4 DATA     BIT       P34
*SFR* 000000B0H.5 DATA     BIT       P35
*SFR* 000000B0H.6 DATA     BIT       P36
*SFR* 000000B0H.7 DATA     BIT       P37
      0100002AH   CODE     ---       P3EI_IRQHandler
*SFR* 000000AFH   DATA     BYTE      P3EXTIE
*SFR* 000000B7H   DATA     BYTE      P3EXTIF
*SFR* 000000A3H   DATA     BYTE      P3TRIS
*SFR* 00000087H   DATA     BYTE      PCON
      00000025H.2 BIT      BIT       Power_count_clean
      0200008AH   XDATA    WORD      Power_Off_Wait_Time
      02000030H   XDATA    WORD      precise_k2_timer
*SFR* 000000B8H.4 DATA     BIT       PS0
*SFR* 000000B8H.6 DATA     BIT       PS1
*SFR* 000000D0H   DATA     BYTE      PSW
*SFR* 000000B8H.1 DATA     BIT       PT0
*SFR* 000000B8H.3 DATA     BIT       PT1
*SFR* 000000B8H.5 DATA     BIT       PT2
*SFR* 000000B8H.0 DATA     BIT       PX0
*SFR* 000000B8H.2 DATA     BIT       PX1
      0100203AH   CODE     ---       Restore_dly
      01002280H   CODE     ---       Return_UART_Data_Length
*SFR* 00000098H.0 DATA     BIT       RI0
*SFR* 000000CBH   DATA     BYTE      RLDH
*SFR* 000000CAH   DATA     BYTE      RLDL
*SFR* 000000D0H.3 DATA     BIT       RS0
*SFR* 000000D0H.4 DATA     BIT       RS1
*SFR* 00000099H   DATA     BYTE      SBUF
*SFR* 00000099H   DATA     BYTE      SBUF0
*SFR* 000000EBH   DATA     BYTE      SBUF1
*SFR* 00000098H   DATA     BYTE      SCON0
*SFR* 000000EAH   DATA     BYTE      SCON1
      02000026H   XDATA    INT       Self_Check
*SFR* 00000081H   DATA     BYTE      SP
*SFR* 000000ECH   DATA     BYTE      SPCR
*SFR* 000000EEH   DATA     BYTE      SPDR
      00000024H.4 BIT      BIT       speedup
      02000010H   XDATA    WORD      speedup_cnt
      0100003AH   CODE     ---       SPI_IRQHandler
*SFR* 000000EDH   DATA     BYTE      SPSR
*SFR* 000000EFH   DATA     BYTE      SSCR
*DEL*:00000000H   CODE     ---       SYS_ClearLVDIntFlag
*DEL*:00000000H   CODE     ---       SYS_ClearPowerOnResetFlag
*DEL*:00000000H   CODE     ---       SYS_ClearWDTResetFlag
*DEL*:00000000H   CODE     ---       SYS_DisableLVD
*DEL*:00000000H   CODE     ---       SYS_DisableLVDInt
*DEL*:00000000H   CODE     ---       SYS_DisableSoftwareReset
*DEL*:00000000H   CODE     ---       SYS_DisableWakeUp
*DEL*:00000000H   CODE     ---       SYS_DisableWakeUpTrig
*DEL*:00000000H   CODE     ---       SYS_DisableWDTReset
*DEL*:00000000H   CODE     ---       SYS_EnableLVD
*DEL*:00000000H   CODE     ---       SYS_EnableLVDInt
*DEL*:00000000H   CODE     ---       SYS_EnableSoftwareReset
      0100000EH   CODE     ---       SYS_EnableWakeUp
*DEL*:00000000H   CODE     ---       SYS_EnableWakeUpTrig
*DEL*:00000000H   CODE     ---       SYS_EnableWDTReset
*DEL*:00000000H   CODE     ---       SYS_EnterIdle
      01000056H   CODE     ---       SYS_EnterStop
*DEL*:00000000H   CODE     ---       SYS_GetLVDIntFlag
*DEL*:00000000H   CODE     ---       SYS_GetPowerOnResetFlag
*DEL*:00000000H   CODE     ---       SYS_GetWDTResetFlag
      0200002BH   XDATA    BYTE      System_Mode_Before_Charge
      02000028H   XDATA    BYTE      System_Mode_Data
      02000007H   XDATA    DWORD     Systemclock
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 19


*SFR* 000000C8H.2 DATA     BIT       T2CM
*SFR* 000000C8H   DATA     BYTE      T2CON
*SFR* 000000C8H.0 DATA     BIT       T2I0
*SFR* 000000C8H.1 DATA     BIT       T2I1
*SFR* 000000CFH   DATA     BYTE      T2IE
*SFR* 000000C9H   DATA     BYTE      T2IF
*SFR* 000000C8H.7 DATA     BIT       T2PS
*SFR* 000000C8H.3 DATA     BIT       T2R0
*SFR* 000000C8H.4 DATA     BIT       T2R1
*SFR* 000000D2H   DATA     BYTE      T34MOD
*SFR* 00000096H   DATA     BYTE      TA
*SFR* 00000088H   DATA     BYTE      TCON
*SFR* 00000088H.5 DATA     BIT       TF0
*SFR* 00000088H.7 DATA     BIT       TF1
*SFR* 0000008CH   DATA     BYTE      TH0
*SFR* 0000008DH   DATA     BYTE      TH1
*SFR* 000000CDH   DATA     BYTE      TH2
*SFR* 000000DBH   DATA     BYTE      TH3
*SFR* 000000E3H   DATA     BYTE      TH4
*SFR* 00000098H.1 DATA     BIT       TI0
      01001474H   CODE     ---       Timer0_IRQHandler
      010020AAH   CODE     ---       Timer1_IRQHandler
      0100001AH   CODE     ---       Timer2_IRQHandler
      01000031H   CODE     ---       Timer3_IRQHandler
      01000032H   CODE     ---       Timer4_IRQHandler
      02000012H   XDATA    WORD      timer_1ms_count
*SFR* 0000008AH   DATA     BYTE      TL0
*SFR* 0000008BH   DATA     BYTE      TL1
*SFR* 000000CCH   DATA     BYTE      TL2
*SFR* 000000DAH   DATA     BYTE      TL3
*SFR* 000000E2H   DATA     BYTE      TL4
*SFR* 00000089H   DATA     BYTE      TMOD
      01001FC0H   CODE     ---       TMR0_Config
      01001FEAH   CODE     ---       TMR1_Config
*DEL*:00000000H   CODE     ---       TMR2_AllIntDisable
*DEL*:00000000H   CODE     ---       TMR2_AllIntEnable
*DEL*:00000000H   CODE     ---       TMR2_ClearOverflowIntFlag
*DEL*:00000000H   CODE     ---       TMR2_ClearT2EXIntFlag
*DEL*:00000000H   CODE     ---       TMR2_DisableGATE
*DEL*:00000000H   CODE     ---       TMR2_DisableOverflowInt
*DEL*:00000000H   CODE     ---       TMR2_DisableT2EXInt
*DEL*:00000000H   CODE     ---       TMR2_EnableGATE
*DEL*:00000000H   CODE     ---       TMR2_EnableOverflowInt
*DEL*:00000000H   CODE     ---       TMR2_EnableT2EXInt
*DEL*:00000000H   CODE     ---       TMR2_GetOverflowIntFlag
*DEL*:00000000H   CODE     ---       TMR2_GetT2EXIntFlag
*DEL*:00000000H   CODE     ---       TMR2_Start
*DEL*:00000000H   CODE     ---       TMR2_Stop
*SFR* 00000088H.4 DATA     BIT       TR0
*SFR* 00000088H.6 DATA     BIT       TR1
*SFR* 00000098H.2 DATA     BIT       U0RB8
*SFR* 00000098H.4 DATA     BIT       U0REN
*SFR* 00000098H.7 DATA     BIT       U0SM0
*SFR* 00000098H.6 DATA     BIT       U0SM1
*SFR* 00000098H.5 DATA     BIT       U0SM2
*SFR* 00000098H.3 DATA     BIT       U0TB8
      01001D03H   CODE     ---       UART0_IRQHandler
      01000022H   CODE     ---       UART1_IRQHandler
      01001D5EH   CODE     ---       UART_0_Config
      01001E43H   CODE     ---       UART_1_Config
      010020EEH   CODE     ---       UART_Data_Init
      0100158FH   CODE     ---       UART_Data_Process
*DEL*:00000000H   CODE     ---       UART_DisableBRT
      01002278H   CODE     ---       UART_EnableBRT
      02000057H   XDATA    ---       UART_Get_String
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 20


      00000021H.3 BIT      BIT       use_precise_timer
*SFR* 00000097H   DATA     BYTE      WDCON
*DEL*:00000000H   CODE     ---       WDT_ClearOverflowIntFlag
*DEL*:00000000H   CODE     ---       WDT_ClearWDT
*DEL*:00000000H   CODE     ---       WDT_DisableOverflowInt
*DEL*:00000000H   CODE     ---       WDT_EnableOverflowInt
*DEL*:00000000H   CODE     ---       WDT_GetOverflowIntFlag
      01000038H   CODE     ---       WDT_IRQHandler
*SFR* 000000BDH   DATA     BYTE      WUTCRH
*SFR* 000000BCH   DATA     BYTE      WUTCRL



SYMBOL TABLE OF MODULE:  .\Objects\Project (?C_STARTUP)

      VALUE       REP       CLASS    TYPE      SYMBOL NAME
      ====================================================
      ---         MODULE    ---      ---       ?C_STARTUP
      01000000H   PUBLIC    CODE     ---       ?C_STARTUP
      000000E0H   SYMBOL    DATA     ---       ACC
      000000F0H   SYMBOL    DATA     ---       B
      00000083H   SYMBOL    DATA     ---       DPH
      00000082H   SYMBOL    DATA     ---       DPL
      00000000H   SYMBOL    NUMBER   ---       IBPSTACK
      00000100H   SYMBOL    NUMBER   ---       IBPSTACKTOP
      00000100H   SYMBOL    NUMBER   ---       IDATALEN
      01001977H   SYMBOL    CODE     ---       IDATALOOP
      00000000H   SYMBOL    NUMBER   ---       PBPSTACK
      00000100H   SYMBOL    NUMBER   ---       PBPSTACKTOP
      00000000H   SYMBOL    NUMBER   ---       PDATALEN
      00000000H   SYMBOL    NUMBER   ---       PDATASTART
      00000000H   SYMBOL    NUMBER   ---       PPAGE
      00000000H   SYMBOL    NUMBER   ---       PPAGEENABLE
      000000A0H   SYMBOL    DATA     ---       PPAGE_SFR
      00000081H   SYMBOL    DATA     ---       SP
      01001974H   SYMBOL    CODE     ---       STARTUP1
      00000000H   SYMBOL    NUMBER   ---       XBPSTACK
      00000000H   SYMBOL    NUMBER   ---       XBPSTACKTOP
      00000400H   SYMBOL    NUMBER   ---       XDATALEN
      01001982H   SYMBOL    CODE     ---       XDATALOOP
      00000000H   SYMBOL    NUMBER   ---       XDATASTART
      01000000H   LINE      CODE     ---       #126
      01001974H   LINE      CODE     ---       #133
      01001976H   LINE      CODE     ---       #134
      01001977H   LINE      CODE     ---       #135
      01001978H   LINE      CODE     ---       #136
      0100197AH   LINE      CODE     ---       #140
      0100197DH   LINE      CODE     ---       #141
      0100197FH   LINE      CODE     ---       #145
      01001981H   LINE      CODE     ---       #147
      01001982H   LINE      CODE     ---       #148
      01001983H   LINE      CODE     ---       #149
      01001984H   LINE      CODE     ---       #150
      01001986H   LINE      CODE     ---       #151
      01001988H   LINE      CODE     ---       #185
      0100198BH   LINE      CODE     ---       #196

      ---         MODULE    ---      ---       ADC
      01002254H   PUBLIC    CODE     ---       _ADC_ConfigADCVref
      01002060H   PUBLIC    CODE     ---       ADC_GetADCResult
      0100218BH   PUBLIC    CODE     ---       _ADC_EnableChannel
      010021E0H   PUBLIC    CODE     ---       _ADC_ConfigRunMode
      01000006H   PUBLIC    CODE     ---       ADC_Start
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 21


      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 22


      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 23


      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01000006H   BLOCK     CODE     ---       LVL=0
      01000006H   LINE      CODE     ---       #66
      01000006H   LINE      CODE     ---       #67
      01000006H   LINE      CODE     ---       #68
      01000009H   LINE      CODE     ---       #69
      ---         BLOCKEND  ---      ---       LVL=0

      010021E0H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCClkDiv
      00000005H   SYMBOL    DATA     BYTE      ADCResultTpye
      010021E0H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      010021E0H   LINE      CODE     ---       #88
      010021E0H   LINE      CODE     ---       #89
      010021E0H   LINE      CODE     ---       #90
      010021E0H   LINE      CODE     ---       #92
      010021E2H   LINE      CODE     ---       #93
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 24


      010021E5H   LINE      CODE     ---       #94
      010021E6H   LINE      CODE     ---       #95
      010021E8H   LINE      CODE     ---       #97
      010021EAH   LINE      CODE     ---       #98
      010021EEH   LINE      CODE     ---       #99
      010021F3H   LINE      CODE     ---       #100
      010021F5H   LINE      CODE     ---       #101
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCTGSource
      00000005H   SYMBOL    DATA     BYTE      TGMode
      00000006H   SYMBOL    DATA     BYTE      Temp

      0100218BH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCChannel
      0100218BH   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      0100218BH   LINE      CODE     ---       #154
      0100218BH   LINE      CODE     ---       #155
      0100218BH   LINE      CODE     ---       #156
      0100218BH   LINE      CODE     ---       #158
      0100218DH   LINE      CODE     ---       #159
      01002191H   LINE      CODE     ---       #160
      0100219AH   LINE      CODE     ---       #161
      0100219CH   LINE      CODE     ---       #163
      0100219EH   LINE      CODE     ---       #164
      010021A2H   LINE      CODE     ---       #165
      010021A6H   LINE      CODE     ---       #166
      010021A8H   LINE      CODE     ---       #168
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      An31Channel
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000006H   SYMBOL    DATA     WORD      TrigTime
      00000005H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      ADCBrake
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000006H   SYMBOL    DATA     WORD      ADCCompareValue

      01002060H   BLOCK     CODE     ---       LVL=0
      01002060H   LINE      CODE     ---       #258
      01002060H   LINE      CODE     ---       #259
      01002060H   LINE      CODE     ---       #260
      01002067H   LINE      CODE     ---       #261
      01002067H   LINE      CODE     ---       #262
      0100207AH   LINE      CODE     ---       #263
      0100207AH   LINE      CODE     ---       #264
      01002084H   LINE      CODE     ---       #265
      ---         BLOCKEND  ---      ---       LVL=0

      01002254H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCVref
      01002254H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      01002254H   LINE      CODE     ---       #344
      01002254H   LINE      CODE     ---       #345
      01002254H   LINE      CODE     ---       #346
      01002254H   LINE      CODE     ---       #348
      01002258H   LINE      CODE     ---       #349
      0100225AH   LINE      CODE     ---       #350
      0100225BH   LINE      CODE     ---       #351
      0100225CH   LINE      CODE     ---       #353
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       EPWM
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 25


      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 26


      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 27


      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      EpwmRunModeMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000005H   SYMBOL    DATA     BYTE      ClkDiv
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000004H   SYMBOL    DATA     WORD      Period
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000004H   SYMBOL    DATA     WORD      Duty
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000004H   SYMBOL    DATA     WORD      UpCmp
      00000002H   SYMBOL    DATA     WORD      DowmCmp
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      BrakeSource
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 28


      00000005H   SYMBOL    DATA     BYTE      CountMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      BrakeSource
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000005H   SYMBOL    DATA     BYTE      BrakeLevel
      00000007H   SYMBOL    DATA     BYTE      Channel
      00000005H   SYMBOL    DATA     BYTE      DeadTime
      00000007H   SYMBOL    DATA     BYTE      Channel
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000005H   SYMBOL    DATA     BYTE      MaskLevel
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      FBBrakeLevel
      00000006H   SYMBOL    DATA     BYTE      Temp

      ---         MODULE    ---      ---       GPIO
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 29


      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 30


      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 31


      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinMSK
      00000003H   SYMBOL    DATA     BYTE      PinMode
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinMSK
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinMSK
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinNum
      00000006H   SYMBOL    DATA     BYTE      PinIntFlag
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinNum

      ---         MODULE    ---      ---       SYSTEM
      01000056H   PUBLIC    CODE     ---       SYS_EnterStop
      0100000EH   PUBLIC    CODE     ---       SYS_EnableWakeUp
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 32


      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 33


      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 34


      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      LVDValue
      00000006H   SYMBOL    DATA     BYTE      Temp

      0100000EH   BLOCK     CODE     ---       LVL=0
      0100000EH   LINE      CODE     ---       #333
      0100000EH   LINE      CODE     ---       #334
      0100000EH   LINE      CODE     ---       #335
      01000011H   LINE      CODE     ---       #336
      ---         BLOCKEND  ---      ---       LVL=0

      01000056H   BLOCK     CODE     ---       LVL=0
      01000056H   LINE      CODE     ---       #358
      01000056H   LINE      CODE     ---       #359
      01000056H   LINE      CODE     ---       #360
      01000057H   LINE      CODE     ---       #361
      01000058H   LINE      CODE     ---       #362
      0100005BH   LINE      CODE     ---       #363
      0100005CH   LINE      CODE     ---       #364
      0100005DH   LINE      CODE     ---       #365
      0100005EH   LINE      CODE     ---       #366
      0100005FH   LINE      CODE     ---       #367
      01000060H   LINE      CODE     ---       #368
      01000061H   LINE      CODE     ---       #369
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      clkdiv
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000004H   SYMBOL    DATA     WORD      time
      00000003H   SYMBOL    DATA     BYTE      Temp

      ---         MODULE    ---      ---       TIMER
      0100214DH   PUBLIC    CODE     ---       _TMR_Stop
      0100212EH   PUBLIC    CODE     ---       _TMR_Start
      010021A9H   PUBLIC    CODE     ---       _TMR_EnableOverflowInt
      01002013H   PUBLIC    CODE     ---       _TMR_ConfigTimerPeriod
      01001DF9H   PUBLIC    CODE     ---       _TMR_ConfigTimerClk
      01001DADH   PUBLIC    CODE     ---       _TMR_ConfigRunMode
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 35


      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 36


      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 37


      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01001DADH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000005H   SYMBOL    DATA     BYTE      TimerMode
      00000003H   SYMBOL    DATA     BYTE      TimerModeBranch
      01001DADH   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      01001DADH   LINE      CODE     ---       #74
      01001DADH   LINE      CODE     ---       #75
      01001DADH   LINE      CODE     ---       #76
      01001DADH   LINE      CODE     ---       #78
      01001DBCH   LINE      CODE     ---       #79
      01001DBCH   LINE      CODE     ---       #80
      01001DBCH   LINE      CODE     ---       #81
      01001DBEH   LINE      CODE     ---       #82
      01001DC2H   LINE      CODE     ---       #83
      01001DC8H   LINE      CODE     ---       #84
      01001DC8H   LINE      CODE     ---       #85
      01001DCAH   LINE      CODE     ---       #86
      01001DCAH   LINE      CODE     ---       #87
      01001DCCH   LINE      CODE     ---       #88
      01001DD0H   LINE      CODE     ---       #89
      01001DDDH   LINE      CODE     ---       #90
      01001DDFH   LINE      CODE     ---       #91
      01001DE0H   LINE      CODE     ---       #92
      01001DE0H   LINE      CODE     ---       #93
      01001DE2H   LINE      CODE     ---       #94
      01001DE5H   LINE      CODE     ---       #95
      01001DE6H   LINE      CODE     ---       #96
      01001DE8H   LINE      CODE     ---       #97
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 38


      01001DE9H   LINE      CODE     ---       #98
      01001DE9H   LINE      CODE     ---       #99
      01001DEBH   LINE      CODE     ---       #100
      01001DEFH   LINE      CODE     ---       #101
      01001DF6H   LINE      CODE     ---       #102
      01001DF8H   LINE      CODE     ---       #103
      01001DF8H   LINE      CODE     ---       #104
      01001DF8H   LINE      CODE     ---       #105
      01001DF8H   LINE      CODE     ---       #106
      01001DF8H   LINE      CODE     ---       #107
      ---         BLOCKEND  ---      ---       LVL=0

      01001DF9H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000005H   SYMBOL    DATA     BYTE      TimerClkDiv
      01001DF9H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      01001DF9H   LINE      CODE     ---       #117
      01001DF9H   LINE      CODE     ---       #118
      01001DF9H   LINE      CODE     ---       #119
      01001DF9H   LINE      CODE     ---       #121
      01001E08H   LINE      CODE     ---       #122
      01001E08H   LINE      CODE     ---       #123
      01001E08H   LINE      CODE     ---       #124
      01001E0AH   LINE      CODE     ---       #125
      01001E0EH   LINE      CODE     ---       #126
      01001E14H   LINE      CODE     ---       #127
      01001E14H   LINE      CODE     ---       #128
      01001E16H   LINE      CODE     ---       #129
      01001E16H   LINE      CODE     ---       #130
      01001E18H   LINE      CODE     ---       #131
      01001E1CH   LINE      CODE     ---       #132
      01001E21H   LINE      CODE     ---       #133
      01001E23H   LINE      CODE     ---       #134
      01001E24H   LINE      CODE     ---       #135
      01001E24H   LINE      CODE     ---       #136
      01001E26H   LINE      CODE     ---       #137
      01001E2AH   LINE      CODE     ---       #138
      01001E2FH   LINE      CODE     ---       #139
      01001E2FH   LINE      CODE     ---       #140
      01001E31H   LINE      CODE     ---       #141
      01001E31H   LINE      CODE     ---       #142
      01001E33H   LINE      CODE     ---       #143
      01001E37H   LINE      CODE     ---       #144
      01001E40H   LINE      CODE     ---       #145
      01001E42H   LINE      CODE     ---       #146
      01001E42H   LINE      CODE     ---       #147
      01001E42H   LINE      CODE     ---       #148
      01001E42H   LINE      CODE     ---       #149
      01001E42H   LINE      CODE     ---       #150
      ---         BLOCKEND  ---      ---       LVL=0

      01002013H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000005H   SYMBOL    DATA     BYTE      TimerPeriodHigh
      00000003H   SYMBOL    DATA     BYTE      TimerPeriodLow
      01002013H   LINE      CODE     ---       #160
      01002013H   LINE      CODE     ---       #161
      01002013H   LINE      CODE     ---       #162
      01002022H   LINE      CODE     ---       #163
      01002022H   LINE      CODE     ---       #164
      01002022H   LINE      CODE     ---       #165
      01002024H   LINE      CODE     ---       #166
      01002026H   LINE      CODE     ---       #167
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 39


      01002027H   LINE      CODE     ---       #168
      01002027H   LINE      CODE     ---       #169
      01002029H   LINE      CODE     ---       #170
      0100202BH   LINE      CODE     ---       #171
      0100202CH   LINE      CODE     ---       #172
      0100202CH   LINE      CODE     ---       #173
      0100202EH   LINE      CODE     ---       #174
      01002030H   LINE      CODE     ---       #175
      01002031H   LINE      CODE     ---       #176
      01002031H   LINE      CODE     ---       #177
      01002035H   LINE      CODE     ---       #178
      01002039H   LINE      CODE     ---       #179
      01002039H   LINE      CODE     ---       #180
      01002039H   LINE      CODE     ---       #181
      01002039H   LINE      CODE     ---       #182
      01002039H   LINE      CODE     ---       #183
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000007H   SYMBOL    DATA     BYTE      Timern

      010021A9H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      010021A9H   LINE      CODE     ---       #256
      010021A9H   LINE      CODE     ---       #257
      010021A9H   LINE      CODE     ---       #258
      010021B8H   LINE      CODE     ---       #259
      010021B8H   LINE      CODE     ---       #260
      010021B8H   LINE      CODE     ---       #261
      010021BAH   LINE      CODE     ---       #262
      010021BBH   LINE      CODE     ---       #263
      010021BBH   LINE      CODE     ---       #264
      010021BDH   LINE      CODE     ---       #265
      010021BEH   LINE      CODE     ---       #266
      010021BEH   LINE      CODE     ---       #267
      010021C1H   LINE      CODE     ---       #268
      010021C2H   LINE      CODE     ---       #269
      010021C2H   LINE      CODE     ---       #270
      010021C5H   LINE      CODE     ---       #271
      010021C5H   LINE      CODE     ---       #272
      010021C5H   LINE      CODE     ---       #273
      010021C5H   LINE      CODE     ---       #274
      010021C5H   LINE      CODE     ---       #275
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000006H   SYMBOL    DATA     BYTE      IntFlag
      00000007H   SYMBOL    DATA     BYTE      Timern

      0100212EH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      0100212EH   LINE      CODE     ---       #368
      0100212EH   LINE      CODE     ---       #369
      0100212EH   LINE      CODE     ---       #370
      0100213DH   LINE      CODE     ---       #371
      0100213DH   LINE      CODE     ---       #372
      0100213DH   LINE      CODE     ---       #373
      01002140H   LINE      CODE     ---       #374
      01002141H   LINE      CODE     ---       #375
      01002141H   LINE      CODE     ---       #376
      01002144H   LINE      CODE     ---       #377
      01002145H   LINE      CODE     ---       #378
      01002145H   LINE      CODE     ---       #379
      01002148H   LINE      CODE     ---       #380
      01002149H   LINE      CODE     ---       #381
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 40


      01002149H   LINE      CODE     ---       #382
      0100214CH   LINE      CODE     ---       #383
      0100214CH   LINE      CODE     ---       #384
      0100214CH   LINE      CODE     ---       #385
      0100214CH   LINE      CODE     ---       #386
      0100214CH   LINE      CODE     ---       #387
      ---         BLOCKEND  ---      ---       LVL=0

      0100214DH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      0100214DH   LINE      CODE     ---       #395
      0100214DH   LINE      CODE     ---       #396
      0100214DH   LINE      CODE     ---       #397
      0100215CH   LINE      CODE     ---       #398
      0100215CH   LINE      CODE     ---       #399
      0100215CH   LINE      CODE     ---       #400
      0100215FH   LINE      CODE     ---       #401
      01002160H   LINE      CODE     ---       #402
      01002160H   LINE      CODE     ---       #403
      01002163H   LINE      CODE     ---       #404
      01002164H   LINE      CODE     ---       #405
      01002164H   LINE      CODE     ---       #406
      01002167H   LINE      CODE     ---       #407
      01002168H   LINE      CODE     ---       #408
      01002168H   LINE      CODE     ---       #409
      0100216BH   LINE      CODE     ---       #410
      0100216BH   LINE      CODE     ---       #411
      0100216BH   LINE      CODE     ---       #412
      0100216BH   LINE      CODE     ---       #413
      0100216BH   LINE      CODE     ---       #414
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timer2Mode
      00000005H   SYMBOL    DATA     BYTE      Timer2LoadMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      TimerClkDiv
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000004H   SYMBOL    DATA     WORD      TimerPeriod
      00000007H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000005H   SYMBOL    DATA     BYTE      CompareMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000003H   SYMBOL    DATA     BYTE      Timer2CCn
      00000004H   SYMBOL    DATA     WORD      CompareValue
      00000007H   SYMBOL    DATA     BYTE      Timer2CompareIntMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000005H   SYMBOL    DATA     BYTE      Timer2CaptureMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000004H   SYMBOL    DATA     WORD      CaputerValue
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn

      ---         MODULE    ---      ---       UART
      01002266H   PUBLIC    CODE     ---       _UART_ConfigBRTPeriod
      0100225DH   PUBLIC    CODE     ---       _UART_ConfigBRTClk
      01002278H   PUBLIC    CODE     ---       UART_EnableBRT
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 41


      010021F6H   PUBLIC    CODE     ---       _UART_GetBuff
      0100216CH   PUBLIC    CODE     ---       _UART_ClearReceiveIntFlag
      01002085H   PUBLIC    CODE     ---       _UART_GetReceiveIntFlag
      01002243H   PUBLIC    CODE     ---       _UART_EnableInt
      0100221DH   PUBLIC    CODE     ---       _UART_EnableReceive
      0100220AH   PUBLIC    CODE     ---       _UART_EnableDoubleFrequency
      01001C47H   PUBLIC    CODE     ---       _UART_ConfigRunMode
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 42


      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 43


      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01001C47H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000005H   SYMBOL    DATA     BYTE      UARTMode
      00000003H   SYMBOL    DATA     BYTE      UARTBaudTimer
      01001C47H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      01001C47H   LINE      CODE     ---       #70
      01001C47H   LINE      CODE     ---       #71
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 44


      01001C47H   LINE      CODE     ---       #72
      01001C47H   LINE      CODE     ---       #74
      01001C4AH   LINE      CODE     ---       #75
      01001C4AH   LINE      CODE     ---       #76
      01001C4CH   LINE      CODE     ---       #77
      01001C50H   LINE      CODE     ---       #78
      01001C57H   LINE      CODE     ---       #79
      01001C59H   LINE      CODE     ---       #81
      01001C5CH   LINE      CODE     ---       #82
      01001C68H   LINE      CODE     ---       #83
      01001C68H   LINE      CODE     ---       #84
      01001C68H   LINE      CODE     ---       #85
      01001C68H   LINE      CODE     ---       #86
      01001C68H   LINE      CODE     ---       #87
      01001C6BH   LINE      CODE     ---       #88
      01001C6DH   LINE      CODE     ---       #89
      01001C6DH   LINE      CODE     ---       #90
      01001C70H   LINE      CODE     ---       #91
      01001C72H   LINE      CODE     ---       #92
      01001C72H   LINE      CODE     ---       #93
      01001C75H   LINE      CODE     ---       #94
      01001C75H   LINE      CODE     ---       #95
      01001C75H   LINE      CODE     ---       #96
      01001C75H   LINE      CODE     ---       #97
      01001C75H   LINE      CODE     ---       #99
      01001C75H   LINE      CODE     ---       #100
      01001C7AH   LINE      CODE     ---       #101
      01001C7AH   LINE      CODE     ---       #102
      01001C7CH   LINE      CODE     ---       #103
      01001C80H   LINE      CODE     ---       #104
      01001C89H   LINE      CODE     ---       #105
      01001C8BH   LINE      CODE     ---       #107
      01001C8EH   LINE      CODE     ---       #108
      01001C9AH   LINE      CODE     ---       #109
      01001C9AH   LINE      CODE     ---       #110
      01001C9AH   LINE      CODE     ---       #111
      01001C9AH   LINE      CODE     ---       #112
      01001C9AH   LINE      CODE     ---       #113
      01001C9DH   LINE      CODE     ---       #114
      01001C9EH   LINE      CODE     ---       #115
      01001C9EH   LINE      CODE     ---       #116
      01001CA1H   LINE      CODE     ---       #117
      01001CA2H   LINE      CODE     ---       #118
      01001CA2H   LINE      CODE     ---       #119
      01001CA5H   LINE      CODE     ---       #120
      01001CA5H   LINE      CODE     ---       #121
      01001CA5H   LINE      CODE     ---       #122
      01001CA5H   LINE      CODE     ---       #123
      01001CA5H   LINE      CODE     ---       #124
      01001CA5H   LINE      CODE     ---       #125
      ---         BLOCKEND  ---      ---       LVL=0

      0100220AH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      0100220AH   LINE      CODE     ---       #133
      0100220AH   LINE      CODE     ---       #134
      0100220AH   LINE      CODE     ---       #135
      01002210H   LINE      CODE     ---       #136
      01002210H   LINE      CODE     ---       #137
      01002213H   LINE      CODE     ---       #138
      01002213H   LINE      CODE     ---       #139
      01002219H   LINE      CODE     ---       #140
      01002219H   LINE      CODE     ---       #141
      0100221CH   LINE      CODE     ---       #142
      0100221CH   LINE      CODE     ---       #143
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 45


      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000007H   SYMBOL    DATA     BYTE      UARTn

      0100221DH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      0100221DH   LINE      CODE     ---       #280
      0100221DH   LINE      CODE     ---       #281
      0100221DH   LINE      CODE     ---       #282
      01002223H   LINE      CODE     ---       #283
      01002223H   LINE      CODE     ---       #284
      01002226H   LINE      CODE     ---       #285
      01002226H   LINE      CODE     ---       #286
      0100222CH   LINE      CODE     ---       #287
      0100222CH   LINE      CODE     ---       #288
      0100222FH   LINE      CODE     ---       #289
      0100222FH   LINE      CODE     ---       #290
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn

      01002243H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      01002243H   LINE      CODE     ---       #317
      01002243H   LINE      CODE     ---       #318
      01002243H   LINE      CODE     ---       #319
      01002249H   LINE      CODE     ---       #320
      01002249H   LINE      CODE     ---       #321
      0100224BH   LINE      CODE     ---       #322
      0100224BH   LINE      CODE     ---       #323
      01002251H   LINE      CODE     ---       #324
      01002251H   LINE      CODE     ---       #325
      01002253H   LINE      CODE     ---       #326
      01002253H   LINE      CODE     ---       #327
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn

      01002085H   BLOCK     CODE     ---       LVL=0
      00000006H   SYMBOL    DATA     BYTE      UARTn
      01002085H   LINE      CODE     ---       #353
      01002087H   LINE      CODE     ---       #354
      01002087H   LINE      CODE     ---       #355
      0100208DH   LINE      CODE     ---       #356
      0100208DH   LINE      CODE     ---       #357
      01002097H   LINE      CODE     ---       #358
      01002097H   LINE      CODE     ---       #359
      0100209DH   LINE      CODE     ---       #360
      0100209DH   LINE      CODE     ---       #361
      010020A7H   LINE      CODE     ---       #362
      010020A7H   LINE      CODE     ---       #363
      010020A9H   LINE      CODE     ---       #364
      ---         BLOCKEND  ---      ---       LVL=0

      0100216CH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      0100216CH   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      temp
      ---         BLOCKEND  ---      ---       LVL=1
      0100216CH   LINE      CODE     ---       #373
      0100216CH   LINE      CODE     ---       #374
      0100216CH   LINE      CODE     ---       #377
      01002172H   LINE      CODE     ---       #378
      01002172H   LINE      CODE     ---       #379
      01002174H   LINE      CODE     ---       #380
      01002177H   LINE      CODE     ---       #381
      0100217BH   LINE      CODE     ---       #382
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 46


      0100217BH   LINE      CODE     ---       #383
      01002181H   LINE      CODE     ---       #384
      01002181H   LINE      CODE     ---       #385
      01002183H   LINE      CODE     ---       #386
      01002186H   LINE      CODE     ---       #387
      0100218AH   LINE      CODE     ---       #388
      0100218AH   LINE      CODE     ---       #389
      ---         BLOCKEND  ---      ---       LVL=0
      00000006H   SYMBOL    DATA     BYTE      UARTn
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000006H   SYMBOL    DATA     BYTE      temp

      010021F6H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      010021F6H   LINE      CODE     ---       #443
      010021F6H   LINE      CODE     ---       #444
      010021F6H   LINE      CODE     ---       #445
      010021FCH   LINE      CODE     ---       #446
      010021FCH   LINE      CODE     ---       #447
      010021FFH   LINE      CODE     ---       #448
      010021FFH   LINE      CODE     ---       #449
      01002207H   LINE      CODE     ---       #450
      01002207H   LINE      CODE     ---       #451
      01002209H   LINE      CODE     ---       #452
      01002209H   LINE      CODE     ---       #453
      01002209H   LINE      CODE     ---       #454
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000005H   SYMBOL    DATA     BYTE      UARTSendValue
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000005H   SYMBOL    DATA     BYTE      UARTSendValue
      00000007H   SYMBOL    DATA     BYTE      UARTn

      01002278H   BLOCK     CODE     ---       LVL=0
      01002278H   LINE      CODE     ---       #534
      01002278H   LINE      CODE     ---       #535
      01002278H   LINE      CODE     ---       #536
      0100227FH   LINE      CODE     ---       #537
      ---         BLOCKEND  ---      ---       LVL=0

      0100225DH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      BRTClkDiv
      0100225DH   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      0100225DH   LINE      CODE     ---       #544
      0100225DH   LINE      CODE     ---       #545
      0100225DH   LINE      CODE     ---       #546
      0100225DH   LINE      CODE     ---       #548
      01002261H   LINE      CODE     ---       #549
      01002263H   LINE      CODE     ---       #550
      01002264H   LINE      CODE     ---       #551
      01002265H   LINE      CODE     ---       #552
      ---         BLOCKEND  ---      ---       LVL=0

      01002266H   BLOCK     CODE     ---       LVL=0
      00000006H   SYMBOL    DATA     WORD      BRTPeriod
      01002266H   LINE      CODE     ---       #560
      01002266H   LINE      CODE     ---       #561
      01002266H   LINE      CODE     ---       #562
      0100226BH   LINE      CODE     ---       #563
      0100226EH   LINE      CODE     ---       #564
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       WDT
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 47


      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 48


      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 49


      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      TsysCoefficient
      00000006H   SYMBOL    DATA     BYTE      Temp

      ---         MODULE    ---      ---       FLASH
      01001F62H   PUBLIC    CODE     ---       _FLASH_Erase
      01001F2EH   PUBLIC    CODE     ---       _FLASH_Read
      01001EF9H   PUBLIC    CODE     ---       _FLASH_Write
      0100001EH   PUBLIC    CODE     ---       FLASH_Lock
      01000016H   PUBLIC    CODE     ---       FLASH_UnLock
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 50


      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 51


      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 52


      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01000016H   BLOCK     CODE     ---       LVL=0
      01000016H   LINE      CODE     ---       #68
      01000016H   LINE      CODE     ---       #69
      01000016H   LINE      CODE     ---       #70
      01000019H   LINE      CODE     ---       #71
      ---         BLOCKEND  ---      ---       LVL=0

      0100001EH   BLOCK     CODE     ---       LVL=0
      0100001EH   LINE      CODE     ---       #79
      0100001EH   LINE      CODE     ---       #80
      0100001EH   LINE      CODE     ---       #81
      01000021H   LINE      CODE     ---       #82
      ---         BLOCKEND  ---      ---       LVL=0

      01001EF9H   BLOCK     CODE     ---       LVL=0
      00000002H   SYMBOL    DATA     BYTE      FLASHModule
      00000004H   SYMBOL    DATA     WORD      Addr
      00000003H   SYMBOL    DATA     BYTE      Data
      01001EF9H   LINE      CODE     ---       #95
      01001EFBH   LINE      CODE     ---       #96
      01001EFBH   LINE      CODE     ---       #97
      01001EFFH   LINE      CODE     ---       #98
      01001F01H   LINE      CODE     ---       #99
      01001F04H   LINE      CODE     ---       #101
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 53


      01001F07H   LINE      CODE     ---       #102
      01001F07H   LINE      CODE     ---       #103
      01001F09H   LINE      CODE     ---       #104
      01001F0AH   LINE      CODE     ---       #105
      01001F0FH   LINE      CODE     ---       #106
      01001F10H   LINE      CODE     ---       #107
      01001F11H   LINE      CODE     ---       #108
      01001F12H   LINE      CODE     ---       #109
      01001F13H   LINE      CODE     ---       #110
      01001F14H   LINE      CODE     ---       #111
      01001F15H   LINE      CODE     ---       #112
      01001F1AH   LINE      CODE     ---       #113
      01001F1CH   LINE      CODE     ---       #114
      01001F1DH   LINE      CODE     ---       #116
      01001F1DH   LINE      CODE     ---       #117
      01001F22H   LINE      CODE     ---       #118
      01001F23H   LINE      CODE     ---       #119
      01001F24H   LINE      CODE     ---       #120
      01001F25H   LINE      CODE     ---       #121
      01001F26H   LINE      CODE     ---       #122
      01001F27H   LINE      CODE     ---       #123
      01001F28H   LINE      CODE     ---       #124
      01001F2DH   LINE      CODE     ---       #125
      01001F2DH   LINE      CODE     ---       #126
      ---         BLOCKEND  ---      ---       LVL=0

      01001F2EH   BLOCK     CODE     ---       LVL=0
      00000003H   SYMBOL    DATA     BYTE      FLASHModule
      00000004H   SYMBOL    DATA     WORD      Addr
      01001F2EH   LINE      CODE     ---       #138
      01001F30H   LINE      CODE     ---       #139
      01001F30H   LINE      CODE     ---       #140
      01001F32H   LINE      CODE     ---       #141
      01001F35H   LINE      CODE     ---       #142
      01001F38H   LINE      CODE     ---       #143
      01001F38H   LINE      CODE     ---       #144
      01001F3AH   LINE      CODE     ---       #145
      01001F3BH   LINE      CODE     ---       #146
      01001F40H   LINE      CODE     ---       #147
      01001F41H   LINE      CODE     ---       #148
      01001F42H   LINE      CODE     ---       #149
      01001F43H   LINE      CODE     ---       #150
      01001F44H   LINE      CODE     ---       #151
      01001F45H   LINE      CODE     ---       #152
      01001F46H   LINE      CODE     ---       #153
      01001F4BH   LINE      CODE     ---       #154
      01001F4DH   LINE      CODE     ---       #155
      01001F4FH   LINE      CODE     ---       #157
      01001F4FH   LINE      CODE     ---       #158
      01001F54H   LINE      CODE     ---       #159
      01001F55H   LINE      CODE     ---       #160
      01001F56H   LINE      CODE     ---       #161
      01001F57H   LINE      CODE     ---       #162
      01001F58H   LINE      CODE     ---       #163
      01001F59H   LINE      CODE     ---       #164
      01001F5AH   LINE      CODE     ---       #165
      01001F5FH   LINE      CODE     ---       #166
      01001F5FH   LINE      CODE     ---       #167
      01001F61H   LINE      CODE     ---       #168
      ---         BLOCKEND  ---      ---       LVL=0

      01001F62H   BLOCK     CODE     ---       LVL=0
      00000003H   SYMBOL    DATA     BYTE      FLASHModule
      00000004H   SYMBOL    DATA     WORD      Addr
      01001F62H   LINE      CODE     ---       #179
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 54


      01001F64H   LINE      CODE     ---       #180
      01001F64H   LINE      CODE     ---       #181
      01001F66H   LINE      CODE     ---       #182
      01001F69H   LINE      CODE     ---       #183
      01001F6CH   LINE      CODE     ---       #184
      01001F6CH   LINE      CODE     ---       #185
      01001F6EH   LINE      CODE     ---       #186
      01001F6FH   LINE      CODE     ---       #187
      01001F74H   LINE      CODE     ---       #188
      01001F75H   LINE      CODE     ---       #189
      01001F76H   LINE      CODE     ---       #190
      01001F77H   LINE      CODE     ---       #191
      01001F78H   LINE      CODE     ---       #192
      01001F79H   LINE      CODE     ---       #193
      01001F7AH   LINE      CODE     ---       #194
      01001F7FH   LINE      CODE     ---       #195
      01001F81H   LINE      CODE     ---       #196
      01001F82H   LINE      CODE     ---       #198
      01001F82H   LINE      CODE     ---       #199
      01001F87H   LINE      CODE     ---       #200
      01001F88H   LINE      CODE     ---       #201
      01001F89H   LINE      CODE     ---       #202
      01001F8AH   LINE      CODE     ---       #203
      01001F8BH   LINE      CODE     ---       #204
      01001F8CH   LINE      CODE     ---       #205
      01001F8DH   LINE      CODE     ---       #206
      01001F92H   LINE      CODE     ---       #207
      01001F92H   LINE      CODE     ---       #208
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ADC_INIT
      010021C6H   PUBLIC    CODE     ---       ADC_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 55


      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 56


      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 57


      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      010021C6H   BLOCK     CODE     ---       LVL=0
      010021C6H   LINE      CODE     ---       #65
      010021C6H   LINE      CODE     ---       #66
      010021C6H   LINE      CODE     ---       #68
      010021CDH   LINE      CODE     ---       #71
      010021D2H   LINE      CODE     ---       #72
      010021D8H   LINE      CODE     ---       #75
      010021DDH   LINE      CODE     ---       #78
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       DEFINE
      0200008CH   PUBLIC    XDATA    BYTE      Motor_Direction_Data
      0200008AH   PUBLIC    XDATA    WORD      Power_Off_Wait_Time
      02000088H   PUBLIC    XDATA    WORD      Battery_ADC_Wait_Time
      00000025H.5 PUBLIC    BIT      BIT       Delay_Over
      02000087H   PUBLIC    XDATA    BYTE      Get_String_Wait_Time
      02000085H   PUBLIC    XDATA    WORD      Delay_Time
      00000025H.4 PUBLIC    BIT      BIT       Get_String_Buff
      02000083H   PUBLIC    XDATA    INT       Count_Toggle
      02000081H   PUBLIC    XDATA    WORD      Delay_Time_Count
      0200007FH   PUBLIC    XDATA    WORD      Motor_Speed_Data
      0200007DH   PUBLIC    XDATA    INT       Num
      0200007CH   PUBLIC    XDATA    BYTE      K3_cnt
      0200007BH   PUBLIC    XDATA    BYTE      K2_cnt
      0200007AH   PUBLIC    XDATA    BYTE      K1_cnt
      02000078H   PUBLIC    XDATA    WORD      longhit_cnt
      00000025H.3 PUBLIC    BIT      BIT       Center_Line_Control
      00000025H.2 PUBLIC    BIT      BIT       Power_count_clean
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 58


      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 59


      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 60


      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      ---         MODULE    ---      ---       GPIO_INIT
      01002230H   PUBLIC    CODE     ---       GPIO_Key_Interrupt_Config
      01001B71H   PUBLIC    CODE     ---       GPIO_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 61


      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 62


      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 63


      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01001B71H   BLOCK     CODE     ---       LVL=0
      01001B71H   LINE      CODE     ---       #42
      01001B71H   LINE      CODE     ---       #43
      01001B71H   LINE      CODE     ---       #44
      01001B74H   LINE      CODE     ---       #45
      01001B77H   LINE      CODE     ---       #46
      01001B7AH   LINE      CODE     ---       #47
      01001B7DH   LINE      CODE     ---       #48
      01001B80H   LINE      CODE     ---       #49
      01001B83H   LINE      CODE     ---       #50
      01001B86H   LINE      CODE     ---       #51
      01001B89H   LINE      CODE     ---       #77
      01001B8EH   LINE      CODE     ---       #78
      01001B91H   LINE      CODE     ---       #79
      01001B98H   LINE      CODE     ---       #81
      01001B9DH   LINE      CODE     ---       #82
      01001BA0H   LINE      CODE     ---       #83
      01001BA7H   LINE      CODE     ---       #85
      01001BACH   LINE      CODE     ---       #86
      01001BAFH   LINE      CODE     ---       #87
      01001BB6H   LINE      CODE     ---       #90
      01001BBBH   LINE      CODE     ---       #91
      01001BBEH   LINE      CODE     ---       #92
      01001BC5H   LINE      CODE     ---       #94
      01001BCAH   LINE      CODE     ---       #95
      01001BCDH   LINE      CODE     ---       #96
      01001BD4H   LINE      CODE     ---       #100
      01001BD9H   LINE      CODE     ---       #101
      01001BDCH   LINE      CODE     ---       #102
      01001BDEH   LINE      CODE     ---       #105
      01001BE0H   LINE      CODE     ---       #122
      ---         BLOCKEND  ---      ---       LVL=0

      01002230H   BLOCK     CODE     ---       LVL=0
      01002230H   LINE      CODE     ---       #131
      01002230H   LINE      CODE     ---       #132
      01002230H   LINE      CODE     ---       #134
      01002236H   LINE      CODE     ---       #135
      01002239H   LINE      CODE     ---       #138
      0100223DH   LINE      CODE     ---       #139
      01002240H   LINE      CODE     ---       #142
      01002242H   LINE      CODE     ---       #143
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       TIMER_INIT
      01001FEAH   PUBLIC    CODE     ---       TMR1_Config
      01001FC0H   PUBLIC    CODE     ---       TMR0_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 64


      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 65


      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 66


      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01001FC0H   BLOCK     CODE     ---       LVL=0
      01001FC0H   LINE      CODE     ---       #11
      01001FC0H   LINE      CODE     ---       #12
      01001FC0H   LINE      CODE     ---       #16
      01001FC8H   LINE      CODE     ---       #20
      01001FCEH   LINE      CODE     ---       #24
      01001FD7H   LINE      CODE     ---       #29
      01001FDCH   LINE      CODE     ---       #34
      01001FE2H   LINE      CODE     ---       #35
      01001FE5H   LINE      CODE     ---       #40
      ---         BLOCKEND  ---      ---       LVL=0

      01001FEAH   BLOCK     CODE     ---       LVL=0
      01001FEAH   LINE      CODE     ---       #50
      01001FEAH   LINE      CODE     ---       #51
      01001FEAH   LINE      CODE     ---       #55
      01001FF3H   LINE      CODE     ---       #59
      01001FFAH   LINE      CODE     ---       #63
      01002003H   LINE      CODE     ---       #68
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 67


      01002008H   LINE      CODE     ---       #73
      0100200BH   LINE      CODE     ---       #74
      0100200EH   LINE      CODE     ---       #79
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       UART_INIT
      02000052H   PUBLIC    XDATA    BYTE      ?_UART_Send_String?BYTE
      01001CA6H   PUBLIC    CODE     ---       _UART_Send_String
      01001E43H   PUBLIC    CODE     ---       UART_1_Config
      01001D5EH   PUBLIC    CODE     ---       UART_0_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 68


      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 69


      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01001D5EH   BLOCK     CODE     ---       LVL=0
      01001D5EH   BLOCK     CODE     NEAR LAB  LVL=1
      02000044H   SYMBOL    XDATA    WORD      BRTValue
      02000046H   SYMBOL    XDATA    DWORD     BaudRateVlue
      ---         BLOCKEND  ---      ---       LVL=1
      01001D5EH   LINE      CODE     ---       #42
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 70


      01001D5EH   LINE      CODE     ---       #43
      01001D5EH   LINE      CODE     ---       #44
      01001D68H   LINE      CODE     ---       #45
      01001D70H   LINE      CODE     ---       #143
      01001D79H   LINE      CODE     ---       #144
      01001D7EH   LINE      CODE     ---       #147
      01001D83H   LINE      CODE     ---       #148
      01001D88H   LINE      CODE     ---       #152
      01001D93H   LINE      CODE     ---       #153
      01001D96H   LINE      CODE     ---       #156
      01001D9CH   LINE      CODE     ---       #157
      01001DA1H   LINE      CODE     ---       #159
      01001DA6H   LINE      CODE     ---       #160
      01001DA9H   LINE      CODE     ---       #161
      01001DACH   LINE      CODE     ---       #162
      ---         BLOCKEND  ---      ---       LVL=0

      01001E43H   BLOCK     CODE     ---       LVL=0
      01001E43H   BLOCK     CODE     NEAR LAB  LVL=1
      02000044H   SYMBOL    XDATA    WORD      BRTValue
      02000046H   SYMBOL    XDATA    DWORD     BaudRateVlue
      ---         BLOCKEND  ---      ---       LVL=1
      01001E43H   LINE      CODE     ---       #223
      01001E43H   LINE      CODE     ---       #224
      01001E43H   LINE      CODE     ---       #225
      01001E4DH   LINE      CODE     ---       #226
      01001E55H   LINE      CODE     ---       #324
      01001E5EH   LINE      CODE     ---       #325
      01001E63H   LINE      CODE     ---       #328
      01001E68H   LINE      CODE     ---       #329
      01001E6DH   LINE      CODE     ---       #333
      01001E78H   LINE      CODE     ---       #334
      01001E7BH   LINE      CODE     ---       #337
      01001E81H   LINE      CODE     ---       #347
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     CHAR      ch
      00000001H   SYMBOL    DATA     ---       s

      01001CA6H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      02000053H   SYMBOL    XDATA    ---       String
      02000056H   SYMBOL    XDATA    BYTE      Length
      01001CB1H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Index
      ---         BLOCKEND  ---      ---       LVL=1
      01001CA6H   LINE      CODE     ---       #408
      01001CB1H   LINE      CODE     ---       #409
      01001CB1H   LINE      CODE     ---       #410
      01001CB3H   LINE      CODE     ---       #411
      01001CBDH   LINE      CODE     ---       #412
      01001CBDH   LINE      CODE     ---       #413
      01001CC0H   LINE      CODE     ---       #414
      01001CC0H   LINE      CODE     ---       #415
      01001CD5H   LINE      CODE     ---       #416
      01001CDAH   LINE      CODE     ---       #417
      01001CDDH   LINE      CODE     ---       #418
      01001CDDH   LINE      CODE     ---       #419
      01001CE2H   LINE      CODE     ---       #420
      01001CE2H   LINE      CODE     ---       #421
      01001CF7H   LINE      CODE     ---       #422
      01001CFCH   LINE      CODE     ---       #423
      01001CFFH   LINE      CODE     ---       #424
      01001CFFH   LINE      CODE     ---       #425
      01001D02H   LINE      CODE     ---       #426
      ---         BLOCKEND  ---      ---       LVL=0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 71



      ---         MODULE    ---      ---       ISR
      020000A0H   PUBLIC    XDATA    INT       Num_Reverse_Pulse
      0200009EH   PUBLIC    XDATA    INT       Num_Forward_Pulse
      0100003AH   PUBLIC    CODE     ---       SPI_IRQHandler
      01000039H   PUBLIC    CODE     ---       I2C_IRQHandler
      01000038H   PUBLIC    CODE     ---       WDT_IRQHandler
      01000037H   PUBLIC    CODE     ---       ADC_IRQHandler
      01000036H   PUBLIC    CODE     ---       EPWM_IRQHandler
      01000032H   PUBLIC    CODE     ---       Timer4_IRQHandler
      01000031H   PUBLIC    CODE     ---       Timer3_IRQHandler
      01000030H   PUBLIC    CODE     ---       ACMP_IRQHandler
      0100002FH   PUBLIC    CODE     ---       LSE_IRQHandler
      0100002EH   PUBLIC    CODE     ---       LVD_IRQHandler
      0100002AH   PUBLIC    CODE     ---       P3EI_IRQHandler
      01001A86H   PUBLIC    CODE     ---       P2EI_IRQHandler
      01001A0EH   PUBLIC    CODE     ---       P1EI_IRQHandler
      01000029H   PUBLIC    CODE     ---       P0EI_IRQHandler
      01000022H   PUBLIC    CODE     ---       UART1_IRQHandler
      0100001AH   PUBLIC    CODE     ---       Timer2_IRQHandler
      01001D03H   PUBLIC    CODE     ---       UART0_IRQHandler
      010020AAH   PUBLIC    CODE     ---       Timer1_IRQHandler
      01000012H   PUBLIC    CODE     ---       INT1_IRQHandler
      01001474H   PUBLIC    CODE     ---       Timer0_IRQHandler
      0100000AH   PUBLIC    CODE     ---       INT0_IRQHandler
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 72


      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 73


      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 74


      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      0100000AH   BLOCK     CODE     ---       LVL=0
      0100000AH   LINE      CODE     ---       #73
      0100000AH   LINE      CODE     ---       #76
      ---         BLOCKEND  ---      ---       LVL=0

      01001474H   BLOCK     CODE     ---       LVL=0
      01001474H   LINE      CODE     ---       #85
      01001483H   LINE      CODE     ---       #87
      01001486H   LINE      CODE     ---       #88
      01001489H   LINE      CODE     ---       #90
      0100148BH   LINE      CODE     ---       #91
      01001499H   LINE      CODE     ---       #92
      010014A7H   LINE      CODE     ---       #93
      010014BCH   LINE      CODE     ---       #95
      010014D8H   LINE      CODE     ---       #96
      010014D8H   LINE      CODE     ---       #97
      010014D8H   LINE      CODE     ---       #98
      010014D8H   LINE      CODE     ---       #99
      010014E4H   LINE      CODE     ---       #100
      010014E4H   LINE      CODE     ---       #101
      010014E4H   LINE      CODE     ---       #102
      010014E4H   LINE      CODE     ---       #103
      010014E4H   LINE      CODE     ---       #104
      010014E4H   LINE      CODE     ---       #105
      010014E6H   LINE      CODE     ---       #106
      010014E6H   LINE      CODE     ---       #107
      010014E6H   LINE      CODE     ---       #108
      010014F2H   LINE      CODE     ---       #109
      010014F2H   LINE      CODE     ---       #110
      010014F2H   LINE      CODE     ---       #111
      010014F2H   LINE      CODE     ---       #112
      010014F2H   LINE      CODE     ---       #113
      010014F2H   LINE      CODE     ---       #114
      010014F4H   LINE      CODE     ---       #115
      010014F4H   LINE      CODE     ---       #116
      010014F4H   LINE      CODE     ---       #117
      01001500H   LINE      CODE     ---       #118
      01001500H   LINE      CODE     ---       #119
      01001500H   LINE      CODE     ---       #120
      01001500H   LINE      CODE     ---       #121
      01001500H   LINE      CODE     ---       #122
      01001500H   LINE      CODE     ---       #123
      01001502H   LINE      CODE     ---       #124
      01001502H   LINE      CODE     ---       #125
      01001502H   LINE      CODE     ---       #126
      0100150EH   LINE      CODE     ---       #127
      0100150EH   LINE      CODE     ---       #128
      01001510H   LINE      CODE     ---       #129
      01001516H   LINE      CODE     ---       #130
      01001516H   LINE      CODE     ---       #131
      01001516H   LINE      CODE     ---       #132
      01001516H   LINE      CODE     ---       #133
      01001516H   LINE      CODE     ---       #134
      01001516H   LINE      CODE     ---       #135
      01001516H   LINE      CODE     ---       #137
      01001519H   LINE      CODE     ---       #138
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 75


      01001519H   LINE      CODE     ---       #139
      01001522H   LINE      CODE     ---       #140
      01001524H   LINE      CODE     ---       #142
      01001524H   LINE      CODE     ---       #143
      01001529H   LINE      CODE     ---       #144
      01001529H   LINE      CODE     ---       #146
      01001536H   LINE      CODE     ---       #147
      01001536H   LINE      CODE     ---       #148
      01001538H   LINE      CODE     ---       #149
      01001546H   LINE      CODE     ---       #150
      01001548H   LINE      CODE     ---       #152
      01001548H   LINE      CODE     ---       #153
      0100154AH   LINE      CODE     ---       #154
      01001551H   LINE      CODE     ---       #155
      01001551H   LINE      CODE     ---       #157
      01001554H   LINE      CODE     ---       #158
      01001554H   LINE      CODE     ---       #159
      01001562H   LINE      CODE     ---       #160
      01001577H   LINE      CODE     ---       #161
      01001577H   LINE      CODE     ---       #162
      01001579H   LINE      CODE     ---       #163
      01001579H   LINE      CODE     ---       #164
      01001579H   LINE      CODE     ---       #165
      0100157BH   LINE      CODE     ---       #167
      0100157BH   LINE      CODE     ---       #168
      01001582H   LINE      CODE     ---       #169
      01001582H   LINE      CODE     ---       #172
      ---         BLOCKEND  ---      ---       LVL=0

      01000012H   BLOCK     CODE     ---       LVL=0
      01000012H   LINE      CODE     ---       #181
      01000012H   LINE      CODE     ---       #184
      ---         BLOCKEND  ---      ---       LVL=0

      010020AAH   BLOCK     CODE     ---       LVL=0
      010020AAH   LINE      CODE     ---       #193
      010020B0H   LINE      CODE     ---       #196
      010020B3H   LINE      CODE     ---       #197
      010020B6H   LINE      CODE     ---       #200
      010020B9H   LINE      CODE     ---       #201
      010020B9H   LINE      CODE     ---       #202
      010020C7H   LINE      CODE     ---       #203
      010020C7H   LINE      CODE     ---       #204
      ---         BLOCKEND  ---      ---       LVL=0

      01001D03H   BLOCK     CODE     ---       LVL=0
      01001D03H   LINE      CODE     ---       #213
      01001D20H   LINE      CODE     ---       #215
      01001D28H   LINE      CODE     ---       #216
      01001D28H   LINE      CODE     ---       #217
      01001D2AH   LINE      CODE     ---       #218
      01001D2FH   LINE      CODE     ---       #219
      01001D3EH   LINE      CODE     ---       #220
      01001D43H   LINE      CODE     ---       #221
      01001D43H   LINE      CODE     ---       #222
      ---         BLOCKEND  ---      ---       LVL=0

      0100001AH   BLOCK     CODE     ---       LVL=0
      0100001AH   LINE      CODE     ---       #231
      0100001AH   LINE      CODE     ---       #234
      ---         BLOCKEND  ---      ---       LVL=0

      01000022H   BLOCK     CODE     ---       LVL=0
      01000022H   LINE      CODE     ---       #243
      01000022H   LINE      CODE     ---       #246
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 76


      ---         BLOCKEND  ---      ---       LVL=0

      01000029H   BLOCK     CODE     ---       LVL=0
      01000029H   LINE      CODE     ---       #255
      01000029H   LINE      CODE     ---       #258
      ---         BLOCKEND  ---      ---       LVL=0

      01001A0EH   BLOCK     CODE     ---       LVL=0
      01001A0EH   LINE      CODE     ---       #267
      01001A1DH   LINE      CODE     ---       #270
      01001A20H   LINE      CODE     ---       #273
      01001A23H   LINE      CODE     ---       #274
      01001A23H   LINE      CODE     ---       #276
      01001A26H   LINE      CODE     ---       #277
      01001A26H   LINE      CODE     ---       #279
      01001A35H   LINE      CODE     ---       #281
      01001A37H   LINE      CODE     ---       #283
      01001A39H   LINE      CODE     ---       #284
      01001A39H   LINE      CODE     ---       #285
      01001A3BH   LINE      CODE     ---       #287
      01001A3BH   LINE      CODE     ---       #289
      01001A3EH   LINE      CODE     ---       #290
      01001A3EH   LINE      CODE     ---       #292
      01001A59H   LINE      CODE     ---       #294
      01001A75H   LINE      CODE     ---       #295
      01001A75H   LINE      CODE     ---       #297
      01001A77H   LINE      CODE     ---       #298
      01001A77H   LINE      CODE     ---       #300
      01001A79H   LINE      CODE     ---       #301
      01001A79H   LINE      CODE     ---       #302
      01001A79H   LINE      CODE     ---       #303
      ---         BLOCKEND  ---      ---       LVL=0

      01001A86H   BLOCK     CODE     ---       LVL=0
      01001A86H   LINE      CODE     ---       #312
      01001A95H   LINE      CODE     ---       #314
      01001A98H   LINE      CODE     ---       #316
      01001A9BH   LINE      CODE     ---       #317
      01001A9BH   LINE      CODE     ---       #319
      01001A9EH   LINE      CODE     ---       #320
      01001A9EH   LINE      CODE     ---       #321
      01001AADH   LINE      CODE     ---       #322
      01001AAFH   LINE      CODE     ---       #323
      01001AB1H   LINE      CODE     ---       #324
      01001AB1H   LINE      CODE     ---       #325
      01001AB3H   LINE      CODE     ---       #327
      01001AB3H   LINE      CODE     ---       #328
      01001AB6H   LINE      CODE     ---       #329
      01001AB6H   LINE      CODE     ---       #330
      01001AD1H   LINE      CODE     ---       #331
      01001AEDH   LINE      CODE     ---       #332
      01001AEDH   LINE      CODE     ---       #333
      01001AEFH   LINE      CODE     ---       #334
      01001AEFH   LINE      CODE     ---       #335
      01001AF1H   LINE      CODE     ---       #336
      01001AF1H   LINE      CODE     ---       #337
      01001AF1H   LINE      CODE     ---       #338
      ---         BLOCKEND  ---      ---       LVL=0

      0100002AH   BLOCK     CODE     ---       LVL=0
      0100002AH   LINE      CODE     ---       #347
      0100002AH   LINE      CODE     ---       #350
      ---         BLOCKEND  ---      ---       LVL=0

      0100002EH   BLOCK     CODE     ---       LVL=0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 77


      0100002EH   LINE      CODE     ---       #359
      0100002EH   LINE      CODE     ---       #362
      ---         BLOCKEND  ---      ---       LVL=0

      0100002FH   BLOCK     CODE     ---       LVL=0
      0100002FH   LINE      CODE     ---       #371
      0100002FH   LINE      CODE     ---       #374
      ---         BLOCKEND  ---      ---       LVL=0

      01000030H   BLOCK     CODE     ---       LVL=0
      01000030H   LINE      CODE     ---       #383
      01000030H   LINE      CODE     ---       #386
      ---         BLOCKEND  ---      ---       LVL=0

      01000031H   BLOCK     CODE     ---       LVL=0
      01000031H   LINE      CODE     ---       #395
      01000031H   LINE      CODE     ---       #398
      ---         BLOCKEND  ---      ---       LVL=0

      01000032H   BLOCK     CODE     ---       LVL=0
      01000032H   LINE      CODE     ---       #407
      01000032H   LINE      CODE     ---       #410
      ---         BLOCKEND  ---      ---       LVL=0

      01000036H   BLOCK     CODE     ---       LVL=0
      01000036H   LINE      CODE     ---       #419
      01000036H   LINE      CODE     ---       #422
      ---         BLOCKEND  ---      ---       LVL=0

      01000037H   BLOCK     CODE     ---       LVL=0
      01000037H   LINE      CODE     ---       #431
      01000037H   LINE      CODE     ---       #434
      ---         BLOCKEND  ---      ---       LVL=0

      01000038H   BLOCK     CODE     ---       LVL=0
      01000038H   LINE      CODE     ---       #443
      01000038H   LINE      CODE     ---       #446
      ---         BLOCKEND  ---      ---       LVL=0

      01000039H   BLOCK     CODE     ---       LVL=0
      01000039H   LINE      CODE     ---       #455
      01000039H   LINE      CODE     ---       #458
      ---         BLOCKEND  ---      ---       LVL=0

      0100003AH   BLOCK     CODE     ---       LVL=0
      0100003AH   LINE      CODE     ---       #467
      0100003AH   LINE      CODE     ---       #470
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       UART_FUNCTION
      02000077H   PUBLIC    XDATA    BYTE      Data_Length
      02000057H   PUBLIC    XDATA    ---       UART_Get_String
      0100158FH   PUBLIC    CODE     ---       UART_Data_Process
      01001AFEH   PUBLIC    CODE     ---       _Function_UART_Send_CMD
      010020EEH   PUBLIC    CODE     ---       UART_Data_Init
      01002286H   PUBLIC    CODE     ---       Clean_UART_Data_Length
      01002280H   PUBLIC    CODE     ---       Return_UART_Data_Length
      010020CEH   PUBLIC    CODE     ---       _UART_Data_Copy
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 78


      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 79


      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 80


      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      0100228CH   SYMBOL    CONST    ---       _?ix1000
      0100228FH   SYMBOL    CONST    ---       _?ix1001
      01002292H   SYMBOL    CONST    ---       _?ix1002

      010020CEH   BLOCK     CODE     ---       LVL=0
      00000001H   SYMBOL    DATA     ---       Data_Point
      00000005H   SYMBOL    DATA     BYTE      Source_Data
      010020CEH   LINE      CODE     ---       #12
      010020CEH   LINE      CODE     ---       #13
      010020CEH   LINE      CODE     ---       #14
      010020DBH   LINE      CODE     ---       #15
      010020E1H   LINE      CODE     ---       #16
      010020EBH   LINE      CODE     ---       #17
      010020EBH   LINE      CODE     ---       #18
      010020EDH   LINE      CODE     ---       #19
      010020EDH   LINE      CODE     ---       #20
      ---         BLOCKEND  ---      ---       LVL=0

      01002280H   BLOCK     CODE     ---       LVL=0
      01002280H   LINE      CODE     ---       #24
      01002280H   LINE      CODE     ---       #25
      01002280H   LINE      CODE     ---       #26
      01002285H   LINE      CODE     ---       #27
      ---         BLOCKEND  ---      ---       LVL=0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 81



      01002286H   BLOCK     CODE     ---       LVL=0
      01002286H   LINE      CODE     ---       #30
      01002286H   LINE      CODE     ---       #31
      01002286H   LINE      CODE     ---       #32
      0100228BH   LINE      CODE     ---       #33
      ---         BLOCKEND  ---      ---       LVL=0

      010020EEH   BLOCK     CODE     ---       LVL=0
      010020EEH   BLOCK     CODE     NEAR LAB  LVL=1
      00000007H   SYMBOL    DATA     BYTE      i
      ---         BLOCKEND  ---      ---       LVL=1
      010020EEH   LINE      CODE     ---       #36
      010020EEH   LINE      CODE     ---       #37
      010020EEH   LINE      CODE     ---       #39
      010020F3H   LINE      CODE     ---       #40
      010020FEH   LINE      CODE     ---       #41
      010020FEH   LINE      CODE     ---       #42
      0100210AH   LINE      CODE     ---       #43
      0100210DH   LINE      CODE     ---       #44
      ---         BLOCKEND  ---      ---       LVL=0

      01001AFEH   BLOCK     CODE     ---       LVL=0
      02000044H   SYMBOL    XDATA    BYTE      CMD_No
      01001B03H   BLOCK     CODE     NEAR LAB  LVL=1
      02000045H   SYMBOL    XDATA    ---       UART_PASS_Data
      02000048H   SYMBOL    XDATA    ---       UART_Error_Data
      0200004BH   SYMBOL    XDATA    ---       UART_Clean_Pair
      ---         BLOCKEND  ---      ---       LVL=1
      01001AFEH   LINE      CODE     ---       #61
      01001B03H   LINE      CODE     ---       #62
      01001B03H   LINE      CODE     ---       #63
      01001B16H   LINE      CODE     ---       #64
      01001B29H   LINE      CODE     ---       #65
      01001B3CH   LINE      CODE     ---       #66
      01001B4AH   LINE      CODE     ---       #67
      01001B4AH   LINE      CODE     ---       #69
      01001B4AH   LINE      CODE     ---       #70
      01001B4AH   LINE      CODE     ---       #71
      01001B50H   LINE      CODE     ---       #72
      01001B50H   LINE      CODE     ---       #73
      01001B52H   LINE      CODE     ---       #75
      01001B52H   LINE      CODE     ---       #76
      01001B52H   LINE      CODE     ---       #77
      01001B5DH   LINE      CODE     ---       #78
      01001B5DH   LINE      CODE     ---       #79
      01001B5FH   LINE      CODE     ---       #81
      01001B5FH   LINE      CODE     ---       #82
      01001B5FH   LINE      CODE     ---       #83
      01001B70H   LINE      CODE     ---       #86
      01001B70H   LINE      CODE     ---       #87
      01001B70H   LINE      CODE     ---       #88
      01001B70H   LINE      CODE     ---       #89
      ---         BLOCKEND  ---      ---       LVL=0

      0100158FH   BLOCK     CODE     ---       LVL=0
      0100158FH   BLOCK     CODE     NEAR LAB  LVL=1
      00000007H   SYMBOL    DATA     BYTE      i
      00000006H   SYMBOL    DATA     BYTE      Return_Data
      ---         BLOCKEND  ---      ---       LVL=1
      0100158FH   LINE      CODE     ---       #92
      0100158FH   LINE      CODE     ---       #93
      0100158FH   LINE      CODE     ---       #96
      01001592H   LINE      CODE     ---       #98
      01001597H   LINE      CODE     ---       #99
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 82


      01001597H   LINE      CODE     ---       #100
      0100159AH   LINE      CODE     ---       #101
      0100159DH   LINE      CODE     ---       #102
      0100159DH   LINE      CODE     ---       #104
      010015A8H   LINE      CODE     ---       #105
      010015A8H   LINE      CODE     ---       #106
      010015B7H   LINE      CODE     ---       #107
      010015B7H   LINE      CODE     ---       #109
      010015B9H   LINE      CODE     ---       #110
      010015BCH   LINE      CODE     ---       #111
      010015CAH   LINE      CODE     ---       #112
      010015CAH   LINE      CODE     ---       #114
      010015CCH   LINE      CODE     ---       #115
      010015CFH   LINE      CODE     ---       #116
      010015DDH   LINE      CODE     ---       #117
      010015DDH   LINE      CODE     ---       #119
      010015DFH   LINE      CODE     ---       #120
      010015E2H   LINE      CODE     ---       #121
      010015F3H   LINE      CODE     ---       #122
      010015F3H   LINE      CODE     ---       #124
      010015F5H   LINE      CODE     ---       #125
      010015F8H   LINE      CODE     ---       #126
      01001606H   LINE      CODE     ---       #127
      01001606H   LINE      CODE     ---       #129
      01001608H   LINE      CODE     ---       #130
      0100160BH   LINE      CODE     ---       #131
      01001619H   LINE      CODE     ---       #132
      01001619H   LINE      CODE     ---       #134
      0100161BH   LINE      CODE     ---       #135
      0100161EH   LINE      CODE     ---       #136
      0100162FH   LINE      CODE     ---       #137
      0100162FH   LINE      CODE     ---       #139
      01001631H   LINE      CODE     ---       #140
      01001633H   LINE      CODE     ---       #141
      01001641H   LINE      CODE     ---       #142
      01001641H   LINE      CODE     ---       #144
      01001643H   LINE      CODE     ---       #145
      01001645H   LINE      CODE     ---       #146
      01001653H   LINE      CODE     ---       #147
      01001653H   LINE      CODE     ---       #149
      01001655H   LINE      CODE     ---       #150
      01001657H   LINE      CODE     ---       #151
      01001668H   LINE      CODE     ---       #152
      01001668H   LINE      CODE     ---       #154
      0100166AH   LINE      CODE     ---       #155
      0100166CH   LINE      CODE     ---       #156
      0100167AH   LINE      CODE     ---       #157
      0100167AH   LINE      CODE     ---       #159
      0100167CH   LINE      CODE     ---       #160
      0100167EH   LINE      CODE     ---       #161
      0100168CH   LINE      CODE     ---       #162
      0100168CH   LINE      CODE     ---       #164
      0100168EH   LINE      CODE     ---       #165
      01001690H   LINE      CODE     ---       #166
      0100169CH   LINE      CODE     ---       #167
      0100169CH   LINE      CODE     ---       #169
      0100169EH   LINE      CODE     ---       #170
      010016A0H   LINE      CODE     ---       #172
      010016A0H   LINE      CODE     ---       #174
      010016A2H   LINE      CODE     ---       #175
      010016A2H   LINE      CODE     ---       #176
      010016A2H   LINE      CODE     ---       #178
      010016A5H   LINE      CODE     ---       #180
      010016A7H   LINE      CODE     ---       #181
      ---         BLOCKEND  ---      ---       LVL=0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 83



      ---         MODULE    ---      ---       BATTERY_FUNCTION

      ---         MODULE    ---      ---       KEY
      00000025H.1 PUBLIC    BIT      BIT       K5_Press
      00000025H.0 PUBLIC    BIT      BIT       K4_Press
      00000024H.7 PUBLIC    BIT      BIT       K3_Press
      00000024H.6 PUBLIC    BIT      BIT       K2_Press
      00000024H.5 PUBLIC    BIT      BIT       K1_Press
      0200009DH   PUBLIC    XDATA    BYTE      K5_Count
      0200009CH   PUBLIC    XDATA    BYTE      K4_Count
      0200009BH   PUBLIC    XDATA    BYTE      K3_Count
      0200009AH   PUBLIC    XDATA    BYTE      K2_Count
      02000099H   PUBLIC    XDATA    BYTE      K1_Count
      02000098H   PUBLIC    XDATA    BYTE      Key_Buff
      01001EBFH   PUBLIC    CODE     ---       Key_Buff_Return
      0100134AH   PUBLIC    CODE     ---       Key_Scan
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 84


      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 85


      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 86



      0100134AH   BLOCK     CODE     ---       LVL=0
      0100134AH   LINE      CODE     ---       #20
      0100134AH   LINE      CODE     ---       #21
      0100134AH   LINE      CODE     ---       #23
      0100134DH   LINE      CODE     ---       #24
      0100134DH   LINE      CODE     ---       #25
      01001350H   LINE      CODE     ---       #26
      01001350H   LINE      CODE     ---       #27
      0100135DH   LINE      CODE     ---       #28
      0100135DH   LINE      CODE     ---       #29
      0100135FH   LINE      CODE     ---       #30
      01001361H   LINE      CODE     ---       #31
      01001363H   LINE      CODE     ---       #43
      01001363H   LINE      CODE     ---       #44
      01001366H   LINE      CODE     ---       #45
      01001366H   LINE      CODE     ---       #46
      01001373H   LINE      CODE     ---       #47
      01001373H   LINE      CODE     ---       #48
      01001375H   LINE      CODE     ---       #49
      01001377H   LINE      CODE     ---       #50
      01001379H   LINE      CODE     ---       #52
      01001379H   LINE      CODE     ---       #53
      0100137FH   LINE      CODE     ---       #54
      0100137FH   LINE      CODE     ---       #55
      01001381H   LINE      CODE     ---       #57
      01001381H   LINE      CODE     ---       #58
      01001386H   LINE      CODE     ---       #59
      01001386H   LINE      CODE     ---       #60
      01001386H   LINE      CODE     ---       #63
      01001389H   LINE      CODE     ---       #64
      01001389H   LINE      CODE     ---       #65
      0100138CH   LINE      CODE     ---       #66
      0100138CH   LINE      CODE     ---       #67
      01001399H   LINE      CODE     ---       #68
      01001399H   LINE      CODE     ---       #69
      0100139BH   LINE      CODE     ---       #70
      0100139DH   LINE      CODE     ---       #71
      0100139FH   LINE      CODE     ---       #83
      0100139FH   LINE      CODE     ---       #84
      010013A2H   LINE      CODE     ---       #85
      010013A2H   LINE      CODE     ---       #86
      010013AFH   LINE      CODE     ---       #87
      010013AFH   LINE      CODE     ---       #88
      010013B1H   LINE      CODE     ---       #89
      010013B3H   LINE      CODE     ---       #90
      010013B5H   LINE      CODE     ---       #92
      010013B5H   LINE      CODE     ---       #93
      010013BBH   LINE      CODE     ---       #94
      010013BBH   LINE      CODE     ---       #95
      010013BDH   LINE      CODE     ---       #97
      010013BDH   LINE      CODE     ---       #98
      010013C2H   LINE      CODE     ---       #99
      010013C2H   LINE      CODE     ---       #100
      010013C2H   LINE      CODE     ---       #103
      010013C5H   LINE      CODE     ---       #104
      010013C5H   LINE      CODE     ---       #105
      010013C8H   LINE      CODE     ---       #106
      010013C8H   LINE      CODE     ---       #107
      010013D5H   LINE      CODE     ---       #108
      010013D5H   LINE      CODE     ---       #109
      010013D7H   LINE      CODE     ---       #110
      010013D9H   LINE      CODE     ---       #111
      010013DBH   LINE      CODE     ---       #123
      010013DBH   LINE      CODE     ---       #124
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 87


      010013DEH   LINE      CODE     ---       #125
      010013DEH   LINE      CODE     ---       #126
      010013EBH   LINE      CODE     ---       #127
      010013EBH   LINE      CODE     ---       #128
      010013EDH   LINE      CODE     ---       #129
      010013EFH   LINE      CODE     ---       #130
      010013F1H   LINE      CODE     ---       #132
      010013F1H   LINE      CODE     ---       #133
      010013F7H   LINE      CODE     ---       #134
      010013F7H   LINE      CODE     ---       #135
      010013F9H   LINE      CODE     ---       #137
      010013F9H   LINE      CODE     ---       #138
      010013FEH   LINE      CODE     ---       #139
      010013FEH   LINE      CODE     ---       #140
      010013FEH   LINE      CODE     ---       #143
      01001401H   LINE      CODE     ---       #144
      01001401H   LINE      CODE     ---       #145
      01001404H   LINE      CODE     ---       #146
      01001404H   LINE      CODE     ---       #147
      01001411H   LINE      CODE     ---       #148
      01001411H   LINE      CODE     ---       #149
      01001413H   LINE      CODE     ---       #150
      01001415H   LINE      CODE     ---       #151
      01001417H   LINE      CODE     ---       #163
      01001417H   LINE      CODE     ---       #164
      0100141AH   LINE      CODE     ---       #165
      0100141AH   LINE      CODE     ---       #166
      01001427H   LINE      CODE     ---       #167
      01001427H   LINE      CODE     ---       #168
      01001429H   LINE      CODE     ---       #169
      0100142BH   LINE      CODE     ---       #170
      0100142DH   LINE      CODE     ---       #172
      0100142DH   LINE      CODE     ---       #173
      01001433H   LINE      CODE     ---       #174
      01001433H   LINE      CODE     ---       #175
      01001435H   LINE      CODE     ---       #177
      01001435H   LINE      CODE     ---       #178
      0100143AH   LINE      CODE     ---       #179
      0100143AH   LINE      CODE     ---       #180
      0100143AH   LINE      CODE     ---       #183
      0100143DH   LINE      CODE     ---       #184
      0100143DH   LINE      CODE     ---       #185
      01001440H   LINE      CODE     ---       #186
      01001440H   LINE      CODE     ---       #187
      0100144DH   LINE      CODE     ---       #188
      0100144DH   LINE      CODE     ---       #189
      0100144FH   LINE      CODE     ---       #190
      01001451H   LINE      CODE     ---       #191
      01001452H   LINE      CODE     ---       #203
      01001452H   LINE      CODE     ---       #204
      01001455H   LINE      CODE     ---       #205
      01001455H   LINE      CODE     ---       #206
      01001462H   LINE      CODE     ---       #207
      01001462H   LINE      CODE     ---       #208
      01001464H   LINE      CODE     ---       #209
      01001466H   LINE      CODE     ---       #210
      01001467H   LINE      CODE     ---       #212
      01001467H   LINE      CODE     ---       #213
      0100146DH   LINE      CODE     ---       #214
      0100146DH   LINE      CODE     ---       #215
      0100146EH   LINE      CODE     ---       #217
      0100146EH   LINE      CODE     ---       #218
      01001473H   LINE      CODE     ---       #219
      01001473H   LINE      CODE     ---       #220
      01001473H   LINE      CODE     ---       #221
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 88


      ---         BLOCKEND  ---      ---       LVL=0

      01001EBFH   BLOCK     CODE     ---       LVL=0
      01001EBFH   LINE      CODE     ---       #224
      01001EBFH   LINE      CODE     ---       #225
      01001EBFH   LINE      CODE     ---       #226
      01001EC4H   LINE      CODE     ---       #228
      01001ECBH   LINE      CODE     ---       #229
      01001ED5H   LINE      CODE     ---       #230
      01001EDFH   LINE      CODE     ---       #231
      01001EE9H   LINE      CODE     ---       #232
      01001EF3H   LINE      CODE     ---       #234
      01001EF8H   LINE      CODE     ---       #235
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ADC_USED
      01000026H   PUBLIC    CODE     ---       ADC_GetResult
      0100003EH   PUBLIC    CODE     ---       ADC_ClearConvertIntFlag
      01000086H   PUBLIC    CODE     ---       ADC_GetConvertIntFlag
      0100226FH   PUBLIC    CODE     ---       _ADC_StartConvert
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 89


      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 90


      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 91


      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      ADC_Channel
      0200008DH   SYMBOL    XDATA    ---       filter_buffer
      02000097H   SYMBOL    XDATA    BYTE      filter_index

      0100226FH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADC_Channel
      0100226FH   LINE      CODE     ---       #43
      0100226FH   LINE      CODE     ---       #44
      0100226FH   LINE      CODE     ---       #45
      01002271H   LINE      CODE     ---       #46
      01002274H   LINE      CODE     ---       #47
      01002277H   LINE      CODE     ---       #48
      ---         BLOCKEND  ---      ---       LVL=0

      01000086H   BLOCK     CODE     ---       LVL=0
      01000086H   LINE      CODE     ---       #50
      01000086H   LINE      CODE     ---       #51
      01000086H   LINE      CODE     ---       #52
      0100008FH   LINE      CODE     ---       #53
      ---         BLOCKEND  ---      ---       LVL=0

      0100003EH   BLOCK     CODE     ---       LVL=0
      0100003EH   LINE      CODE     ---       #55
      0100003EH   LINE      CODE     ---       #56
      0100003EH   LINE      CODE     ---       #58
      ---         BLOCKEND  ---      ---       LVL=0

      01000026H   BLOCK     CODE     ---       LVL=0
      01000026H   LINE      CODE     ---       #60
      01000026H   LINE      CODE     ---       #61
      01000026H   LINE      CODE     ---       #62
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       MAIN
      00000024H.4 PUBLIC    BIT      BIT       speedup
      00000024H.3 PUBLIC    BIT      BIT       longhit
      02000032H   PUBLIC    XDATA    BYTE      ledonoff_cnt
      00000024H.2 PUBLIC    BIT      BIT       key_short_press_mode
      00000024H.1 PUBLIC    BIT      BIT       direction_changed
      00000024H.0 PUBLIC    BIT      BIT       Bit_N_ms_Buff
      02000030H   PUBLIC    XDATA    WORD      precise_k2_timer
      0200002EH   PUBLIC    XDATA    WORD      BatV
      00000023H.7 PUBLIC    BIT      BIT       key3_long_started
      00000023H.6 PUBLIC    BIT      BIT       key1_long_started
      00000023H.5 PUBLIC    BIT      BIT       k2_long_press_detected
      00000023H.4 PUBLIC    BIT      BIT       Bit_Toggle
      00000023H.3 PUBLIC    BIT      BIT       Delay_Open
      0200002DH   PUBLIC    XDATA    BYTE      batlow1_cnt
      00000023H.2 PUBLIC    BIT      BIT       charge_flash
      0200002CH   PUBLIC    XDATA    BYTE      last_direction
      00000023H.1 PUBLIC    BIT      BIT       key3_pressed
      00000023H.0 PUBLIC    BIT      BIT       Bit_1_ms_Buff
      00000022H.7 PUBLIC    BIT      BIT       key1_pressed
      0200002BH   PUBLIC    XDATA    BYTE      System_Mode_Before_Charge
      00000022H.6 PUBLIC    BIT      BIT       Charge_Was_Connected
      00000022H.5 PUBLIC    BIT      BIT       ledonoff
      02000029H   PUBLIC    XDATA    WORD      original_speed
      00000022H.4 PUBLIC    BIT      BIT       key3_handle
      02000028H   PUBLIC    XDATA    BYTE      System_Mode_Data
      02000026H   PUBLIC    XDATA    INT       Self_Check
      00000022H.3 PUBLIC    BIT      BIT       key1_handle
      02000024H   PUBLIC    XDATA    WORD      key3_duration
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 92


      02000022H   PUBLIC    XDATA    WORD      dly
      00000022H.2 PUBLIC    BIT      BIT       k3_released
      02000020H   PUBLIC    XDATA    WORD      key1_duration
      00000022H.1 PUBLIC    BIT      BIT       k2_released
      0200001EH   PUBLIC    XDATA    WORD      k2_long_press_timer
      00000022H.0 PUBLIC    BIT      BIT       batlow1
      0200001CH   PUBLIC    XDATA    INT       Count_1_Degree_Pulse
      00000021H.7 PUBLIC    BIT      BIT       auto_rotate_mode
      0200001BH   PUBLIC    XDATA    BYTE      batlow_cnt
      00000021H.6 PUBLIC    BIT      BIT       Charg_State_Buff
      0200001AH   PUBLIC    XDATA    BYTE      battery_check_divider
      00000021H.5 PUBLIC    BIT      BIT       led_flash_state
      02000018H   PUBLIC    XDATA    WORD      led_flash_timer
      02000017H   PUBLIC    XDATA    BYTE      ledonoff1_cnt
      02000016H   PUBLIC    XDATA    BYTE      main_loop_counter
      00000021H.4 PUBLIC    BIT      BIT       need_led_flash
      02000014H   PUBLIC    XDATA    WORD      auto_rotate_flash_timer
      02000012H   PUBLIC    XDATA    WORD      timer_1ms_count
      00000021H.3 PUBLIC    BIT      BIT       use_precise_timer
      02000010H   PUBLIC    XDATA    WORD      speedup_cnt
      0200000EH   PUBLIC    XDATA    WORD      key3_press_time
      00000021H.2 PUBLIC    BIT      BIT       auto_rotate_flash
      0200000CH   PUBLIC    XDATA    WORD      key1_press_time
      0200000BH   PUBLIC    XDATA    BYTE      key_scan_divider
      00000021H.1 PUBLIC    BIT      BIT       K3_cnt_EN
      02000007H   PUBLIC    XDATA    DWORD     Systemclock
      00000021H.0 PUBLIC    BIT      BIT       K2_cnt_EN
      00000020H.7 PUBLIC    BIT      BIT       K1_cnt_EN
      00000020H.6 PUBLIC    BIT      BIT       auto_rotate_running
      00000020H.5 PUBLIC    BIT      BIT       MOTOR_RUNNING_FLAG
      00000020H.4 PUBLIC    BIT      BIT       key_control_active
      00000020H.3 PUBLIC    BIT      BIT       Key_Long_Press
      00000020H.2 PUBLIC    BIT      BIT       batlow
      02000005H   PUBLIC    XDATA    WORD      charge_flash_cnt
      00000020H.1 PUBLIC    BIT      BIT       auto_rotate_entry_complete
      00000020H.0 PUBLIC    BIT      BIT       ledonoff1
      010011DFH   PUBLIC    CODE     ---       Key_Interrupt_Process
      010016A8H   PUBLIC    CODE     ---       LED_Control
      0100203AH   PUBLIC    CODE     ---       Restore_dly
      01001F93H   PUBLIC    CODE     ---       _Store_dly
      010018A9H   PUBLIC    CODE     ---       Battery_Check
      01001E82H   PUBLIC    CODE     ---       _Key_Function_Switch_System
      01001BE1H   PUBLIC    CODE     ---       _Motor_Step_Control
      0100210EH   PUBLIC    CODE     ---       _Delay1ms
      010000B6H   PUBLIC    CODE     ---       main
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 93


      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 94


      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 95


      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      010000B6H   BLOCK     CODE     ---       LVL=0
      010000B6H   BLOCK     CODE     NEAR LAB  LVL=1
      00000025H.6 SYMBOL    BIT      BIT       Delay_Open_Buff
      02000033H   SYMBOL    XDATA    INT       Key_Input
      02000035H   SYMBOL    XDATA    INT       Charge_Input
      02000037H   SYMBOL    XDATA    INT       Key_State
      02000039H   SYMBOL    XDATA    INT       Key_State_Save
      0200003BH   SYMBOL    XDATA    INT       Charge_State_Save
      0200003DH   SYMBOL    XDATA    INT       Key_Keep_Time_For_System_Open
      00000025H.7 SYMBOL    BIT      BIT       Long_Press_To_Open
      00000026H.0 SYMBOL    BIT      BIT       Blue_Teeth_Long_Press
      0200003FH   SYMBOL    XDATA    INT       Charge_Keep_Time_For_System_Open
      02000041H   SYMBOL    XDATA    BYTE      UART_Get_CMD
      00000026H.1 SYMBOL    BIT      BIT       Voltage_Low
      02000042H   SYMBOL    XDATA    WORD      k2k3_press_time
      ---         BLOCKEND  ---      ---       LVL=1
      010000B6H   LINE      CODE     ---       #143
      010000B6H   LINE      CODE     ---       #144
      010000B6H   LINE      CODE     ---       #150
      010000B8H   LINE      CODE     ---       #153
      010000BAH   LINE      CODE     ---       #154
      010000C1H   LINE      CODE     ---       #157
      010000C4H   LINE      CODE     ---       #158
      010000CBH   LINE      CODE     ---       #160
      010000CEH   LINE      CODE     ---       #162
      010000D1H   LINE      CODE     ---       #163
      010000D4H   LINE      CODE     ---       #165
      010000D7H   LINE      CODE     ---       #166
      010000DAH   LINE      CODE     ---       #170
      010000DCH   LINE      CODE     ---       #171
      010000E3H   LINE      CODE     ---       #172
      010000E7H   LINE      CODE     ---       #173
      010000E9H   LINE      CODE     ---       #174
      010000EFH   LINE      CODE     ---       #175
      010000F5H   LINE      CODE     ---       #176
      010000FBH   LINE      CODE     ---       #177
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 96


      010000FDH   LINE      CODE     ---       #178
      01000108H   LINE      CODE     ---       #179
      0100011CH   LINE      CODE     ---       #180
      0100011CH   LINE      CODE     ---       #181
      01000125H   LINE      CODE     ---       #182
      0100012BH   LINE      CODE     ---       #183
      0100012BH   LINE      CODE     ---       #184
      0100012DH   LINE      CODE     ---       #187
      01000130H   LINE      CODE     ---       #189
      01000136H   LINE      CODE     ---       #190
      01000136H   LINE      CODE     ---       #191
      01000139H   LINE      CODE     ---       #192
      0100013BH   LINE      CODE     ---       #194
      0100013EH   LINE      CODE     ---       #195
      01000149H   LINE      CODE     ---       #198
      0100014FH   LINE      CODE     ---       #199
      0100014FH   LINE      CODE     ---       #200
      0100015DH   LINE      CODE     ---       #201
      0100016FH   LINE      CODE     ---       #202
      0100016FH   LINE      CODE     ---       #203
      01000173H   LINE      CODE     ---       #204
      01000175H   LINE      CODE     ---       #205
      0100017BH   LINE      CODE     ---       #206
      0100017DH   LINE      CODE     ---       #207
      0100017FH   LINE      CODE     ---       #208
      01000181H   LINE      CODE     ---       #209
      01000188H   LINE      CODE     ---       #210
      0100018EH   LINE      CODE     ---       #211
      01000190H   LINE      CODE     ---       #212
      01000192H   LINE      CODE     ---       #213
      0100019AH   LINE      CODE     ---       #214
      0100019CH   LINE      CODE     ---       #215
      0100019EH   LINE      CODE     ---       #216
      010001A0H   LINE      CODE     ---       #217
      010001A0H   LINE      CODE     ---       #218
      010001A3H   LINE      CODE     ---       #220
      010001A3H   LINE      CODE     ---       #221
      010001AAH   LINE      CODE     ---       #223
      010001C6H   LINE      CODE     ---       #224
      010001C6H   LINE      CODE     ---       #225
      010001CCH   LINE      CODE     ---       #226
      010001CCH   LINE      CODE     ---       #227
      010001DAH   LINE      CODE     ---       #228
      010001EBH   LINE      CODE     ---       #229
      010001EBH   LINE      CODE     ---       #230
      010001EFH   LINE      CODE     ---       #231
      010001F1H   LINE      CODE     ---       #232
      010001F3H   LINE      CODE     ---       #233
      010001F8H   LINE      CODE     ---       #234
      010001F8H   LINE      CODE     ---       #235
      010001FAH   LINE      CODE     ---       #236
      01000205H   LINE      CODE     ---       #237
      01000205H   LINE      CODE     ---       #238
      01000213H   LINE      CODE     ---       #239
      01000224H   LINE      CODE     ---       #240
      01000224H   LINE      CODE     ---       #241
      01000228H   LINE      CODE     ---       #242
      0100022AH   LINE      CODE     ---       #243
      0100022EH   LINE      CODE     ---       #244
      01000230H   LINE      CODE     ---       #245
      01000236H   LINE      CODE     ---       #246
      01000236H   LINE      CODE     ---       #247
      01000238H   LINE      CODE     ---       #248
      01000242H   LINE      CODE     ---       #249
      01000242H   LINE      CODE     ---       #250
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 97


      01000248H   LINE      CODE     ---       #251
      0100024AH   LINE      CODE     ---       #252
      0100024EH   LINE      CODE     ---       #253
      01000254H   LINE      CODE     ---       #254
      01000256H   LINE      CODE     ---       #255
      01000256H   LINE      CODE     ---       #256
      01000258H   LINE      CODE     ---       #258
      01000262H   LINE      CODE     ---       #259
      01000262H   LINE      CODE     ---       #260
      01000267H   LINE      CODE     ---       #261
      01000267H   LINE      CODE     ---       #262
      01000267H   LINE      CODE     ---       #264
      01000276H   LINE      CODE     ---       #265
      0100028EH   LINE      CODE     ---       #266
      01000291H   LINE      CODE     ---       #268
      01000293H   LINE      CODE     ---       #269
      01000299H   LINE      CODE     ---       #270
      010002A1H   LINE      CODE     ---       #271
      010002A4H   LINE      CODE     ---       #274
      010002ADH   LINE      CODE     ---       #275
      010002ADH   LINE      CODE     ---       #277
      010002B3H   LINE      CODE     ---       #278
      010002B3H   LINE      CODE     ---       #280
      010002B3H   LINE      CODE     ---       #282
      010002B3H   LINE      CODE     ---       #284
      010002B3H   LINE      CODE     ---       #285
      010002B3H   LINE      CODE     ---       #286
      010002B6H   LINE      CODE     ---       #287
      010002B8H   LINE      CODE     ---       #289
      010002BEH   LINE      CODE     ---       #292
      010002CDH   LINE      CODE     ---       #293
      010002CDH   LINE      CODE     ---       #294
      010002CFH   LINE      CODE     ---       #295
      010002D2H   LINE      CODE     ---       #296
      010002D2H   LINE      CODE     ---       #297
      010002DDH   LINE      CODE     ---       #299
      010002EEH   LINE      CODE     ---       #300
      01000306H   LINE      CODE     ---       #303
      01000315H   LINE      CODE     ---       #304
      01000315H   LINE      CODE     ---       #305
      01000317H   LINE      CODE     ---       #306
      0100031AH   LINE      CODE     ---       #307
      0100031AH   LINE      CODE     ---       #310
      0100031DH   LINE      CODE     ---       #313
      0100032AH   LINE      CODE     ---       #314
      0100032AH   LINE      CODE     ---       #316
      0100032DH   LINE      CODE     ---       #317
      0100032DH   LINE      CODE     ---       #319
      01000330H   LINE      CODE     ---       #320
      01000330H   LINE      CODE     ---       #321
      01000338H   LINE      CODE     ---       #322
      0100033AH   LINE      CODE     ---       #323
      0100033AH   LINE      CODE     ---       #324
      0100033AH   LINE      CODE     ---       #325
      0100033CH   LINE      CODE     ---       #326
      0100033EH   LINE      CODE     ---       #327
      01000344H   LINE      CODE     ---       #328
      01000344H   LINE      CODE     ---       #330
      01000347H   LINE      CODE     ---       #331
      01000347H   LINE      CODE     ---       #333
      0100034AH   LINE      CODE     ---       #334
      0100034AH   LINE      CODE     ---       #335
      01000352H   LINE      CODE     ---       #336
      01000354H   LINE      CODE     ---       #337
      01000354H   LINE      CODE     ---       #338
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 98


      01000354H   LINE      CODE     ---       #339
      01000356H   LINE      CODE     ---       #340
      01000358H   LINE      CODE     ---       #342
      01000358H   LINE      CODE     ---       #344
      0100035EH   LINE      CODE     ---       #345
      0100035EH   LINE      CODE     ---       #347
      01000366H   LINE      CODE     ---       #348
      01000368H   LINE      CODE     ---       #352
      01000368H   LINE      CODE     ---       #353
      0100036AH   LINE      CODE     ---       #354
      0100036AH   LINE      CODE     ---       #357
      01000375H   LINE      CODE     ---       #358
      01000375H   LINE      CODE     ---       #360
      01000378H   LINE      CODE     ---       #361
      01000378H   LINE      CODE     ---       #363
      0100037BH   LINE      CODE     ---       #364
      0100037BH   LINE      CODE     ---       #365
      0100037DH   LINE      CODE     ---       #366
      0100037DH   LINE      CODE     ---       #368
      01000380H   LINE      CODE     ---       #369
      01000380H   LINE      CODE     ---       #370
      01000382H   LINE      CODE     ---       #371
      01000382H   LINE      CODE     ---       #374
      0100038BH   LINE      CODE     ---       #375
      0100038BH   LINE      CODE     ---       #376
      0100038DH   LINE      CODE     ---       #377
      0100038FH   LINE      CODE     ---       #378
      01000391H   LINE      CODE     ---       #379
      01000399H   LINE      CODE     ---       #380
      0100039BH   LINE      CODE     ---       #381
      0100039BH   LINE      CODE     ---       #382
      0100039DH   LINE      CODE     ---       #384
      0100039DH   LINE      CODE     ---       #387
      010003A9H   LINE      CODE     ---       #388
      010003A9H   LINE      CODE     ---       #389
      010003ACH   LINE      CODE     ---       #390
      010003ACH   LINE      CODE     ---       #392
      010003AEH   LINE      CODE     ---       #393
      010003B4H   LINE      CODE     ---       #395
      010003B9H   LINE      CODE     ---       #396
      010003C2H   LINE      CODE     ---       #397
      010003C7H   LINE      CODE     ---       #398
      010003C7H   LINE      CODE     ---       #401
      010003D6H   LINE      CODE     ---       #402
      010003D6H   LINE      CODE     ---       #403
      010003D8H   LINE      CODE     ---       #404
      010003DDH   LINE      CODE     ---       #405
      010003DFH   LINE      CODE     ---       #406
      010003E1H   LINE      CODE     ---       #407
      010003E3H   LINE      CODE     ---       #408
      010003EBH   LINE      CODE     ---       #409
      010003EDH   LINE      CODE     ---       #410
      010003EFH   LINE      CODE     ---       #411
      010003F4H   LINE      CODE     ---       #412
      010003F4H   LINE      CODE     ---       #413
      010003F6H   LINE      CODE     ---       #415
      010003F6H   LINE      CODE     ---       #417
      010003F9H   LINE      CODE     ---       #418
      010003F9H   LINE      CODE     ---       #419
      010003FBH   LINE      CODE     ---       #420
      01000400H   LINE      CODE     ---       #421
      01000407H   LINE      CODE     ---       #422
      01000407H   LINE      CODE     ---       #423
      01000409H   LINE      CODE     ---       #424
      01000409H   LINE      CODE     ---       #425
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 99


      01000409H   LINE      CODE     ---       #428
      0100040FH   LINE      CODE     ---       #429
      0100040FH   LINE      CODE     ---       #430
      0100042CH   LINE      CODE     ---       #431
      0100042CH   LINE      CODE     ---       #432
      0100042FH   LINE      CODE     ---       #433
      0100042FH   LINE      CODE     ---       #435
      0100042FH   LINE      CODE     ---       #436
      0100042FH   LINE      CODE     ---       #437
      0100042FH   LINE      CODE     ---       #438
      0100042FH   LINE      CODE     ---       #439
      0100042FH   LINE      CODE     ---       #440
      0100042FH   LINE      CODE     ---       #441
      01000431H   LINE      CODE     ---       #442
      0100043CH   LINE      CODE     ---       #443
      0100043CH   LINE      CODE     ---       #445
      0100043CH   LINE      CODE     ---       #446
      0100043CH   LINE      CODE     ---       #447
      0100043CH   LINE      CODE     ---       #448
      0100043CH   LINE      CODE     ---       #450
      0100043EH   LINE      CODE     ---       #451
      01000444H   LINE      CODE     ---       #452
      01000444H   LINE      CODE     ---       #454
      01000449H   LINE      CODE     ---       #455
      01000449H   LINE      CODE     ---       #456
      01000449H   LINE      CODE     ---       #457
      01000449H   LINE      CODE     ---       #458
      01000449H   LINE      CODE     ---       #459
      01000449H   LINE      CODE     ---       #460
      01000449H   LINE      CODE     ---       #461
      0100044BH   LINE      CODE     ---       #462
      01000468H   LINE      CODE     ---       #463
      01000468H   LINE      CODE     ---       #464
      0100046BH   LINE      CODE     ---       #465
      0100046BH   LINE      CODE     ---       #467
      01000470H   LINE      CODE     ---       #468
      01000470H   LINE      CODE     ---       #469
      01000470H   LINE      CODE     ---       #470
      01000470H   LINE      CODE     ---       #471
      01000470H   LINE      CODE     ---       #472
      01000470H   LINE      CODE     ---       #473
      01000472H   LINE      CODE     ---       #474
      0100047DH   LINE      CODE     ---       #475
      0100047DH   LINE      CODE     ---       #477
      0100047FH   LINE      CODE     ---       #478
      01000481H   LINE      CODE     ---       #479
      01000489H   LINE      CODE     ---       #480
      0100048BH   LINE      CODE     ---       #481
      0100048DH   LINE      CODE     ---       #482
      01000493H   LINE      CODE     ---       #483
      01000493H   LINE      CODE     ---       #485
      01000499H   LINE      CODE     ---       #486
      010004A2H   LINE      CODE     ---       #487
      010004A4H   LINE      CODE     ---       #488
      010004A6H   LINE      CODE     ---       #489
      010004A8H   LINE      CODE     ---       #490
      010004AFH   LINE      CODE     ---       #491
      010004AFH   LINE      CODE     ---       #492
      010004AFH   LINE      CODE     ---       #493
      010004AFH   LINE      CODE     ---       #496
      010004B5H   LINE      CODE     ---       #497
      010004B5H   LINE      CODE     ---       #498
      010004B7H   LINE      CODE     ---       #499
      010004C5H   LINE      CODE     ---       #501
      010004D6H   LINE      CODE     ---       #502
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 100


      010004D6H   LINE      CODE     ---       #503
      010004DEH   LINE      CODE     ---       #504
      010004E0H   LINE      CODE     ---       #506
      010004E0H   LINE      CODE     ---       #507
      010004E7H   LINE      CODE     ---       #508
      010004E7H   LINE      CODE     ---       #509
      010004E7H   LINE      CODE     ---       #512
      010004EAH   LINE      CODE     ---       #513
      010004EAH   LINE      CODE     ---       #515
      010004F8H   LINE      CODE     ---       #516
      0100050AH   LINE      CODE     ---       #517
      0100050AH   LINE      CODE     ---       #518
      0100050EH   LINE      CODE     ---       #519
      01000510H   LINE      CODE     ---       #520
      01000510H   LINE      CODE     ---       #521
      01000513H   LINE      CODE     ---       #523
      01000513H   LINE      CODE     ---       #525
      01000515H   LINE      CODE     ---       #526
      01000519H   LINE      CODE     ---       #527
      01000519H   LINE      CODE     ---       #528
      0100051CH   LINE      CODE     ---       #531
      0100051CH   LINE      CODE     ---       #532
      0100051FH   LINE      CODE     ---       #534
      0100053FH   LINE      CODE     ---       #535
      0100053FH   LINE      CODE     ---       #536
      01000541H   LINE      CODE     ---       #537
      01000544H   LINE      CODE     ---       #538
      01000561H   LINE      CODE     ---       #539
      01000561H   LINE      CODE     ---       #540
      01000561H   LINE      CODE     ---       #541
      01000561H   LINE      CODE     ---       #542
      01000563H   LINE      CODE     ---       #543
      01000580H   LINE      CODE     ---       #544
      01000580H   LINE      CODE     ---       #545
      01000588H   LINE      CODE     ---       #546
      0100058EH   LINE      CODE     ---       #547
      01000590H   LINE      CODE     ---       #548
      0100059FH   LINE      CODE     ---       #549
      0100059FH   LINE      CODE     ---       #551
      010005A2H   LINE      CODE     ---       #552
      010005A2H   LINE      CODE     ---       #553
      010005A4H   LINE      CODE     ---       #554
      010005AAH   LINE      CODE     ---       #555
      010005AFH   LINE      CODE     ---       #556
      010005B8H   LINE      CODE     ---       #557
      010005BDH   LINE      CODE     ---       #558
      010005BDH   LINE      CODE     ---       #560
      010005CCH   LINE      CODE     ---       #561
      010005CCH   LINE      CODE     ---       #562
      010005CEH   LINE      CODE     ---       #563
      010005D3H   LINE      CODE     ---       #564
      010005DCH   LINE      CODE     ---       #565
      010005DEH   LINE      CODE     ---       #566
      010005E0H   LINE      CODE     ---       #567
      010005E5H   LINE      CODE     ---       #568
      010005E5H   LINE      CODE     ---       #569
      010005E5H   LINE      CODE     ---       #570
      010005F4H   LINE      CODE     ---       #571
      010005F4H   LINE      CODE     ---       #573
      010005F6H   LINE      CODE     ---       #574
      010005FBH   LINE      CODE     ---       #575
      01000602H   LINE      CODE     ---       #576
      01000602H   LINE      CODE     ---       #577
      01000602H   LINE      CODE     ---       #579
      01000611H   LINE      CODE     ---       #582
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 101


      0100061CH   LINE      CODE     ---       #583
      0100061CH   LINE      CODE     ---       #584
      01000622H   LINE      CODE     ---       #585
      01000622H   LINE      CODE     ---       #586
      0100062FH   LINE      CODE     ---       #587
      0100062FH   LINE      CODE     ---       #588
      01000631H   LINE      CODE     ---       #589
      01000633H   LINE      CODE     ---       #590
      0100063BH   LINE      CODE     ---       #591
      0100063DH   LINE      CODE     ---       #593
      0100063DH   LINE      CODE     ---       #594
      01000643H   LINE      CODE     ---       #595
      01000643H   LINE      CODE     ---       #597
      0100064BH   LINE      CODE     ---       #598
      0100064BH   LINE      CODE     ---       #599
      0100064CH   LINE      CODE     ---       #600
      01000650H   LINE      CODE     ---       #601
      01000653H   LINE      CODE     ---       #602
      0100065CH   LINE      CODE     ---       #603
      0100065CH   LINE      CODE     ---       #604
      01000661H   LINE      CODE     ---       #606
      010006AAH   LINE      CODE     ---       #607
      010006AAH   LINE      CODE     ---       #608
      010006AAH   LINE      CODE     ---       #609
      010006AAH   LINE      CODE     ---       #610
      010006B8H   LINE      CODE     ---       #611
      010006C5H   LINE      CODE     ---       #612
      010006C5H   LINE      CODE     ---       #613
      010006CCH   LINE      CODE     ---       #614
      010006CEH   LINE      CODE     ---       #616
      010006CEH   LINE      CODE     ---       #617
      010006D0H   LINE      CODE     ---       #618
      010006D7H   LINE      CODE     ---       #619
      010006D7H   LINE      CODE     ---       #620
      010006D7H   LINE      CODE     ---       #621
      010006D7H   LINE      CODE     ---       #622
      010006D7H   LINE      CODE     ---       #623
      010006D9H   LINE      CODE     ---       #624
      010006D9H   LINE      CODE     ---       #625
      010006E2H   LINE      CODE     ---       #626
      010006EDH   LINE      CODE     ---       #627
      010006EDH   LINE      CODE     ---       #628
      010006EFH   LINE      CODE     ---       #629
      010006EFH   LINE      CODE     ---       #630
      010006EFH   LINE      CODE     ---       #631
      010006EFH   LINE      CODE     ---       #632
      010006EFH   LINE      CODE     ---       #633
      010006EFH   LINE      CODE     ---       #634
      010006F2H   LINE      CODE     ---       #635
      010006F2H   LINE      CODE     ---       #636
      010006F2H   LINE      CODE     ---       #637
      01000700H   LINE      CODE     ---       #638
      0100070DH   LINE      CODE     ---       #639
      0100070DH   LINE      CODE     ---       #640
      01000713H   LINE      CODE     ---       #641
      01000715H   LINE      CODE     ---       #643
      01000715H   LINE      CODE     ---       #644
      01000717H   LINE      CODE     ---       #645
      0100071EH   LINE      CODE     ---       #646
      0100071EH   LINE      CODE     ---       #647
      01000729H   LINE      CODE     ---       #648
      0100072EH   LINE      CODE     ---       #649
      0100072EH   LINE      CODE     ---       #650
      01000731H   LINE      CODE     ---       #651
      01000731H   LINE      CODE     ---       #652
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 102


      0100073AH   LINE      CODE     ---       #653
      01000742H   LINE      CODE     ---       #654
      01000742H   LINE      CODE     ---       #655
      01000744H   LINE      CODE     ---       #656
      01000744H   LINE      CODE     ---       #657
      01000744H   LINE      CODE     ---       #658
      01000744H   LINE      CODE     ---       #659
      01000744H   LINE      CODE     ---       #660
      01000744H   LINE      CODE     ---       #661
      01000746H   LINE      CODE     ---       #662
      01000746H   LINE      CODE     ---       #663
      0100074FH   LINE      CODE     ---       #664
      01000757H   LINE      CODE     ---       #665
      01000757H   LINE      CODE     ---       #666
      01000759H   LINE      CODE     ---       #667
      01000759H   LINE      CODE     ---       #668
      0100075EH   LINE      CODE     ---       #669
      0100075EH   LINE      CODE     ---       #670
      0100075EH   LINE      CODE     ---       #671
      0100075EH   LINE      CODE     ---       #672
      01000760H   LINE      CODE     ---       #673
      01000760H   LINE      CODE     ---       #674
      01000769H   LINE      CODE     ---       #675
      01000771H   LINE      CODE     ---       #676
      01000771H   LINE      CODE     ---       #677
      01000773H   LINE      CODE     ---       #678
      01000773H   LINE      CODE     ---       #679
      01000779H   LINE      CODE     ---       #680
      0100077BH   LINE      CODE     ---       #681
      0100077BH   LINE      CODE     ---       #682
      0100077BH   LINE      CODE     ---       #683
      0100077EH   LINE      CODE     ---       #684
      0100077EH   LINE      CODE     ---       #685
      01000784H   LINE      CODE     ---       #686
      01000786H   LINE      CODE     ---       #687
      0100078EH   LINE      CODE     ---       #688
      01000790H   LINE      CODE     ---       #689
      01000793H   LINE      CODE     ---       #690
      01000793H   LINE      CODE     ---       #691
      01000793H   LINE      CODE     ---       #692
      0100079CH   LINE      CODE     ---       #693
      010007A4H   LINE      CODE     ---       #694
      010007A4H   LINE      CODE     ---       #695
      010007A6H   LINE      CODE     ---       #696
      010007A6H   LINE      CODE     ---       #697
      010007ACH   LINE      CODE     ---       #698
      010007AEH   LINE      CODE     ---       #699
      010007AEH   LINE      CODE     ---       #700
      010007AEH   LINE      CODE     ---       #701
      010007AEH   LINE      CODE     ---       #702
      010007B0H   LINE      CODE     ---       #703
      010007B0H   LINE      CODE     ---       #704
      010007B0H   LINE      CODE     ---       #705
      010007B9H   LINE      CODE     ---       #706
      010007C1H   LINE      CODE     ---       #707
      010007C1H   LINE      CODE     ---       #708
      010007C3H   LINE      CODE     ---       #709
      010007C3H   LINE      CODE     ---       #710
      010007C9H   LINE      CODE     ---       #711
      010007CBH   LINE      CODE     ---       #712
      010007CBH   LINE      CODE     ---       #713
      010007CBH   LINE      CODE     ---       #714
      010007CBH   LINE      CODE     ---       #715
      010007CDH   LINE      CODE     ---       #716
      010007CDH   LINE      CODE     ---       #717
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 103


      010007CDH   LINE      CODE     ---       #718
      010007D6H   LINE      CODE     ---       #719
      010007DEH   LINE      CODE     ---       #720
      010007DEH   LINE      CODE     ---       #721
      010007E0H   LINE      CODE     ---       #722
      010007E0H   LINE      CODE     ---       #723
      010007E6H   LINE      CODE     ---       #724
      010007E8H   LINE      CODE     ---       #725
      010007E8H   LINE      CODE     ---       #726
      010007E8H   LINE      CODE     ---       #727
      010007E8H   LINE      CODE     ---       #728
      010007EAH   LINE      CODE     ---       #729
      010007EAH   LINE      CODE     ---       #730
      010007EAH   LINE      CODE     ---       #731
      010007F3H   LINE      CODE     ---       #732
      010007FBH   LINE      CODE     ---       #733
      010007FBH   LINE      CODE     ---       #734
      010007FDH   LINE      CODE     ---       #735
      010007FDH   LINE      CODE     ---       #736
      01000803H   LINE      CODE     ---       #737
      01000805H   LINE      CODE     ---       #738
      01000807H   LINE      CODE     ---       #739
      0100080DH   LINE      CODE     ---       #740
      0100080DH   LINE      CODE     ---       #741
      0100080FH   LINE      CODE     ---       #742
      0100080FH   LINE      CODE     ---       #743
      0100080FH   LINE      CODE     ---       #744
      01000815H   LINE      CODE     ---       #745
      01000817H   LINE      CODE     ---       #746
      01000817H   LINE      CODE     ---       #747
      01000817H   LINE      CODE     ---       #748
      01000817H   LINE      CODE     ---       #749
      01000817H   LINE      CODE     ---       #750
      01000817H   LINE      CODE     ---       #751
      01000817H   LINE      CODE     ---       #752
      01000817H   LINE      CODE     ---       #753
      01000817H   LINE      CODE     ---       #756
      01000828H   LINE      CODE     ---       #757
      01000828H   LINE      CODE     ---       #758
      0100082AH   LINE      CODE     ---       #759
      0100082CH   LINE      CODE     ---       #760
      0100083AH   LINE      CODE     ---       #762
      01000846H   LINE      CODE     ---       #763
      01000846H   LINE      CODE     ---       #764
      01000848H   LINE      CODE     ---       #765
      0100084AH   LINE      CODE     ---       #766
      0100084CH   LINE      CODE     ---       #767
      0100084CH   LINE      CODE     ---       #768
      01000852H   LINE      CODE     ---       #769
      01000852H   LINE      CODE     ---       #770
      01000855H   LINE      CODE     ---       #771
      0100085CH   LINE      CODE     ---       #772
      0100085EH   LINE      CODE     ---       #773
      0100085EH   LINE      CODE     ---       #774
      01000861H   LINE      CODE     ---       #775
      01000861H   LINE      CODE     ---       #776
      01000869H   LINE      CODE     ---       #777
      01000869H   LINE      CODE     ---       #778
      0100086CH   LINE      CODE     ---       #779
      0100086CH   LINE      CODE     ---       #781
      0100086FH   LINE      CODE     ---       #782
      0100086FH   LINE      CODE     ---       #783
      01000878H   LINE      CODE     ---       #784
      0100087AH   LINE      CODE     ---       #786
      0100087AH   LINE      CODE     ---       #788
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 104


      01000887H   LINE      CODE     ---       #789
      01000887H   LINE      CODE     ---       #790
      0100088FH   LINE      CODE     ---       #791
      01000891H   LINE      CODE     ---       #792
      01000897H   LINE      CODE     ---       #793
      01000897H   LINE      CODE     ---       #794
      0100089FH   LINE      CODE     ---       #795
      010008A1H   LINE      CODE     ---       #796
      010008A7H   LINE      CODE     ---       #797
      010008A7H   LINE      CODE     ---       #798
      010008AFH   LINE      CODE     ---       #799
      010008AFH   LINE      CODE     ---       #800
      010008AFH   LINE      CODE     ---       #801
      010008AFH   LINE      CODE     ---       #802
      010008B2H   LINE      CODE     ---       #803
      010008B2H   LINE      CODE     ---       #804
      010008B8H   LINE      CODE     ---       #805
      010008B8H   LINE      CODE     ---       #806
      010008C9H   LINE      CODE     ---       #807
      010008C9H   LINE      CODE     ---       #808
      010008C9H   LINE      CODE     ---       #809
      010008CBH   LINE      CODE     ---       #811
      010008CBH   LINE      CODE     ---       #812
      010008D3H   LINE      CODE     ---       #813
      010008DAH   LINE      CODE     ---       #814
      010008E0H   LINE      CODE     ---       #815
      010008E2H   LINE      CODE     ---       #816
      010008E4H   LINE      CODE     ---       #817
      010008E6H   LINE      CODE     ---       #818
      010008E8H   LINE      CODE     ---       #819
      010008EAH   LINE      CODE     ---       #820
      010008EAH   LINE      CODE     ---       #821
      010008EDH   LINE      CODE     ---       #823
      010008EDH   LINE      CODE     ---       #824
      010008F5H   LINE      CODE     ---       #825
      010008FCH   LINE      CODE     ---       #826
      01000902H   LINE      CODE     ---       #827
      01000904H   LINE      CODE     ---       #828
      01000906H   LINE      CODE     ---       #829
      01000908H   LINE      CODE     ---       #830
      0100090AH   LINE      CODE     ---       #831
      0100090AH   LINE      CODE     ---       #832
      0100090DH   LINE      CODE     ---       #833
      0100091EH   LINE      CODE     ---       #834
      0100091EH   LINE      CODE     ---       #835
      01000926H   LINE      CODE     ---       #836
      01000929H   LINE      CODE     ---       #838
      01000929H   LINE      CODE     ---       #839
      0100092FH   LINE      CODE     ---       #840
      0100092FH   LINE      CODE     ---       #841
      0100092FH   LINE      CODE     ---       #842
      0100092FH   LINE      CODE     ---       #843
      0100092FH   LINE      CODE     ---       #844
      0100092FH   LINE      CODE     ---       #845
      0100092FH   LINE      CODE     ---       #846
      0100092FH   LINE      CODE     ---       #847
      01000931H   LINE      CODE     ---       #849
      01000931H   LINE      CODE     ---       #850
      01000937H   LINE      CODE     ---       #851
      01000937H   LINE      CODE     ---       #852
      0100093EH   LINE      CODE     ---       #853
      01000941H   LINE      CODE     ---       #855
      01000941H   LINE      CODE     ---       #856
      01000949H   LINE      CODE     ---       #857
      01000950H   LINE      CODE     ---       #858
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 105


      01000956H   LINE      CODE     ---       #859
      01000958H   LINE      CODE     ---       #860
      0100095AH   LINE      CODE     ---       #861
      0100095CH   LINE      CODE     ---       #862
      0100095CH   LINE      CODE     ---       #863
      0100095CH   LINE      CODE     ---       #864
      0100095CH   LINE      CODE     ---       #865
      0100095FH   LINE      CODE     ---       #866
      0100096AH   LINE      CODE     ---       #867
      0100096AH   LINE      CODE     ---       #868
      0100096CH   LINE      CODE     ---       #869
      01000974H   LINE      CODE     ---       #870
      01000977H   LINE      CODE     ---       #871
      0100097EH   LINE      CODE     ---       #872
      0100097EH   LINE      CODE     ---       #873
      01000980H   LINE      CODE     ---       #874
      01000982H   LINE      CODE     ---       #875
      01000984H   LINE      CODE     ---       #876
      0100098CH   LINE      CODE     ---       #879
      0100098EH   LINE      CODE     ---       #880
      01000990H   LINE      CODE     ---       #881
      01000992H   LINE      CODE     ---       #882
      01000998H   LINE      CODE     ---       #883
      0100099EH   LINE      CODE     ---       #884
      010009A0H   LINE      CODE     ---       #885
      010009A2H   LINE      CODE     ---       #886
      010009A4H   LINE      CODE     ---       #887
      010009A6H   LINE      CODE     ---       #889
      010009A9H   LINE      CODE     ---       #891
      010009ACH   LINE      CODE     ---       #893
      010009AFH   LINE      CODE     ---       #895
      010009B2H   LINE      CODE     ---       #896
      010009B5H   LINE      CODE     ---       #897
      010009B8H   LINE      CODE     ---       #898
      010009BBH   LINE      CODE     ---       #899
      010009BEH   LINE      CODE     ---       #900
      010009C1H   LINE      CODE     ---       #901
      010009C4H   LINE      CODE     ---       #903
      010009CAH   LINE      CODE     ---       #904
      010009CAH   LINE      CODE     ---       #905
      ---         BLOCKEND  ---      ---       LVL=0

      0100210EH   BLOCK     CODE     ---       LVL=0
      00000006H   SYMBOL    DATA     WORD      z
      0100210EH   BLOCK     CODE     NEAR LAB  LVL=1
      00000002H   SYMBOL    DATA     WORD      x
      00000004H   SYMBOL    DATA     WORD      y
      ---         BLOCKEND  ---      ---       LVL=1
      0100210EH   LINE      CODE     ---       #909
      0100210EH   LINE      CODE     ---       #910
      0100210EH   LINE      CODE     ---       #912
      01002118H   LINE      CODE     ---       #913
      0100212DH   LINE      CODE     ---       #914
      ---         BLOCKEND  ---      ---       LVL=0

      01001BE1H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Direction
      01001BE1H   BLOCK     CODE     NEAR LAB  LVL=1
      02000000H   SYMBOL    XDATA    BYTE      Step_No
      ---         BLOCKEND  ---      ---       LVL=1
      01001BE1H   LINE      CODE     ---       #916
      01001BE1H   LINE      CODE     ---       #917
      01001BE1H   LINE      CODE     ---       #920
      01001BE6H   LINE      CODE     ---       #921
      01001BE6H   LINE      CODE     ---       #923
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 106


      01001BECH   LINE      CODE     ---       #924
      01001BF6H   LINE      CODE     ---       #925
      01001BF6H   LINE      CODE     ---       #926
      01001BF9H   LINE      CODE     ---       #927
      01001BF9H   LINE      CODE     ---       #928
      01001BFBH   LINE      CODE     ---       #930
      01001BFBH   LINE      CODE     ---       #932
      01001C01H   LINE      CODE     ---       #933
      01001C0BH   LINE      CODE     ---       #934
      01001C0BH   LINE      CODE     ---       #935
      01001C0EH   LINE      CODE     ---       #936
      01001C0EH   LINE      CODE     ---       #937
      01001C0EH   LINE      CODE     ---       #940
      01001C20H   LINE      CODE     ---       #941
      01001C20H   LINE      CODE     ---       #942
      01001C26H   LINE      CODE     ---       #943
      01001C2CH   LINE      CODE     ---       #944
      01001C35H   LINE      CODE     ---       #945
      01001C3EH   LINE      CODE     ---       #946
      01001C46H   LINE      CODE     ---       #947
      01001C46H   LINE      CODE     ---       #948
      ---         BLOCKEND  ---      ---       LVL=0

      01001E82H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Key_Input
      01001E82H   LINE      CODE     ---       #950
      01001E82H   LINE      CODE     ---       #951
      01001E82H   LINE      CODE     ---       #952
      01001E8FH   LINE      CODE     ---       #953
      01001E8FH   LINE      CODE     ---       #954
      01001E8FH   LINE      CODE     ---       #955
      01001E98H   LINE      CODE     ---       #956
      01001E9EH   LINE      CODE     ---       #957
      01001E9FH   LINE      CODE     ---       #958
      01001E9FH   LINE      CODE     ---       #959
      01001EA2H   LINE      CODE     ---       #960
      01001EA2H   LINE      CODE     ---       #961
      01001EA7H   LINE      CODE     ---       #962
      01001EA8H   LINE      CODE     ---       #964
      01001EA8H   LINE      CODE     ---       #965
      01001EAEH   LINE      CODE     ---       #966
      01001EAEH   LINE      CODE     ---       #967
      01001EAFH   LINE      CODE     ---       #968
      01001EAFH   LINE      CODE     ---       #969
      01001EB8H   LINE      CODE     ---       #970
      01001EBEH   LINE      CODE     ---       #971
      01001EBEH   LINE      CODE     ---       #972
      01001EBEH   LINE      CODE     ---       #973
      01001EBEH   LINE      CODE     ---       #974
      01001EBEH   LINE      CODE     ---       #975
      ---         BLOCKEND  ---      ---       LVL=0

      010018A9H   BLOCK     CODE     ---       LVL=0
      010018A9H   BLOCK     CODE     NEAR LAB  LVL=1
      02000001H   SYMBOL    XDATA    BYTE      adc_state
      02000002H   SYMBOL    XDATA    BYTE      sample_count
      02000003H   SYMBOL    XDATA    WORD      adc_sum
      ---         BLOCKEND  ---      ---       LVL=1
      010018A9H   LINE      CODE     ---       #978
      010018A9H   LINE      CODE     ---       #979
      010018A9H   LINE      CODE     ---       #985
      010018BAH   LINE      CODE     ---       #986
      010018BAH   LINE      CODE     ---       #987
      010018BAH   LINE      CODE     ---       #988
      010018BFH   LINE      CODE     ---       #989
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 107


      010018C5H   LINE      CODE     ---       #990
      010018C6H   LINE      CODE     ---       #992
      010018C6H   LINE      CODE     ---       #993
      010018CFH   LINE      CODE     ---       #994
      010018CFH   LINE      CODE     ---       #995
      010018DEH   LINE      CODE     ---       #996
      010018E4H   LINE      CODE     ---       #997
      010018E7H   LINE      CODE     ---       #999
      010018F4H   LINE      CODE     ---       #1000
      010018F4H   LINE      CODE     ---       #1001
      0100190BH   LINE      CODE     ---       #1002
      01001912H   LINE      CODE     ---       #1003
      01001916H   LINE      CODE     ---       #1004
      0100191CH   LINE      CODE     ---       #1005
      0100191DH   LINE      CODE     ---       #1007
      0100191DH   LINE      CODE     ---       #1008
      0100191DH   LINE      CODE     ---       #1009
      0100191DH   LINE      CODE     ---       #1010
      0100191DH   LINE      CODE     ---       #1011
      0100191FH   LINE      CODE     ---       #1013
      0100191FH   LINE      CODE     ---       #1014
      0100192FH   LINE      CODE     ---       #1015
      0100192FH   LINE      CODE     ---       #1016
      01001935H   LINE      CODE     ---       #1017
      01001940H   LINE      CODE     ---       #1018
      01001942H   LINE      CODE     ---       #1020
      01001942H   LINE      CODE     ---       #1021
      01001944H   LINE      CODE     ---       #1022
      01001949H   LINE      CODE     ---       #1023
      01001949H   LINE      CODE     ---       #1025
      01001952H   LINE      CODE     ---       #1026
      01001952H   LINE      CODE     ---       #1027
      01001958H   LINE      CODE     ---       #1028
      01001963H   LINE      CODE     ---       #1029
      01001965H   LINE      CODE     ---       #1031
      01001965H   LINE      CODE     ---       #1032
      0100196AH   LINE      CODE     ---       #1033
      0100196CH   LINE      CODE     ---       #1034
      0100196CH   LINE      CODE     ---       #1035
      0100196CH   LINE      CODE     ---       #1036
      0100196EH   LINE      CODE     ---       #1038
      0100196EH   LINE      CODE     ---       #1039
      01001973H   LINE      CODE     ---       #1040
      01001973H   LINE      CODE     ---       #1041
      01001973H   LINE      CODE     ---       #1042
      ---         BLOCKEND  ---      ---       LVL=0

      01001F93H   BLOCK     CODE     ---       LVL=0
      02000044H   SYMBOL    XDATA    WORD      dly1
      01001F93H   LINE      CODE     ---       #1044
      01001F9BH   LINE      CODE     ---       #1045
      01001F9BH   LINE      CODE     ---       #1046
      01001F9EH   LINE      CODE     ---       #1047
      01001FA1H   LINE      CODE     ---       #1048
      01001FAAH   LINE      CODE     ---       #1049
      01001FB9H   LINE      CODE     ---       #1050
      01001FBCH   LINE      CODE     ---       #1051
      01001FBFH   LINE      CODE     ---       #1052
      ---         BLOCKEND  ---      ---       LVL=0

      0100203AH   BLOCK     CODE     ---       LVL=0
      0100203AH   BLOCK     CODE     NEAR LAB  LVL=1
      02000044H   SYMBOL    XDATA    WORD      temp
      ---         BLOCKEND  ---      ---       LVL=1
      0100203AH   LINE      CODE     ---       #1054
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 108


      0100203AH   LINE      CODE     ---       #1055
      0100203AH   LINE      CODE     ---       #1058
      0100203DH   LINE      CODE     ---       #1059
      01002040H   LINE      CODE     ---       #1060
      01002051H   LINE      CODE     ---       #1061
      01002054H   LINE      CODE     ---       #1062
      01002057H   LINE      CODE     ---       #1064
      0100205FH   LINE      CODE     ---       #1065
      ---         BLOCKEND  ---      ---       LVL=0

      010016A8H   BLOCK     CODE     ---       LVL=0
      010016A8H   LINE      CODE     ---       #1067
      010016A8H   LINE      CODE     ---       #1068
      010016A8H   LINE      CODE     ---       #1070
      010016B3H   LINE      CODE     ---       #1071
      010016B3H   LINE      CODE     ---       #1073
      010016B6H   LINE      CODE     ---       #1074
      010016B6H   LINE      CODE     ---       #1076
      010016B8H   LINE      CODE     ---       #1077
      010016BBH   LINE      CODE     ---       #1078
      010016BBH   LINE      CODE     ---       #1080
      010016C2H   LINE      CODE     ---       #1081
      010016C4H   LINE      CODE     ---       #1082
      010016C6H   LINE      CODE     ---       #1084
      010016C6H   LINE      CODE     ---       #1085
      010016C8H   LINE      CODE     ---       #1086
      010016C8H   LINE      CODE     ---       #1087
      010016CAH   LINE      CODE     ---       #1089
      010016CAH   LINE      CODE     ---       #1091
      010016CCH   LINE      CODE     ---       #1092
      010016CFH   LINE      CODE     ---       #1093
      010016CFH   LINE      CODE     ---       #1095
      010016D2H   LINE      CODE     ---       #1096
      010016D2H   LINE      CODE     ---       #1097
      010016D4H   LINE      CODE     ---       #1098
      010016D6H   LINE      CODE     ---       #1100
      010016D6H   LINE      CODE     ---       #1101
      010016D8H   LINE      CODE     ---       #1102
      010016D8H   LINE      CODE     ---       #1103
      010016DAH   LINE      CODE     ---       #1105
      010016DAH   LINE      CODE     ---       #1107
      010016DCH   LINE      CODE     ---       #1108
      010016DCH   LINE      CODE     ---       #1109
      010016DCH   LINE      CODE     ---       #1110
      010016DEH   LINE      CODE     ---       #1112
      010016DEH   LINE      CODE     ---       #1114
      010016E1H   LINE      CODE     ---       #1115
      010016E1H   LINE      CODE     ---       #1116
      010016E3H   LINE      CODE     ---       #1117
      010016E6H   LINE      CODE     ---       #1118
      010016E6H   LINE      CODE     ---       #1120
      010016EDH   LINE      CODE     ---       #1121
      010016EFH   LINE      CODE     ---       #1122
      010016F1H   LINE      CODE     ---       #1124
      010016F1H   LINE      CODE     ---       #1125
      010016F3H   LINE      CODE     ---       #1126
      010016F3H   LINE      CODE     ---       #1127
      010016F5H   LINE      CODE     ---       #1129
      010016F5H   LINE      CODE     ---       #1130
      010016F8H   LINE      CODE     ---       #1131
      010016F8H   LINE      CODE     ---       #1132
      010016FAH   LINE      CODE     ---       #1133
      01001704H   LINE      CODE     ---       #1135
      01001704H   LINE      CODE     ---       #1136
      0100170BH   LINE      CODE     ---       #1137
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 109


      0100170DH   LINE      CODE     ---       #1138
      0100170DH   LINE      CODE     ---       #1139
      0100170FH   LINE      CODE     ---       #1141
      0100170FH   LINE      CODE     ---       #1142
      01001711H   LINE      CODE     ---       #1143
      0100171BH   LINE      CODE     ---       #1145
      0100171BH   LINE      CODE     ---       #1146
      01001722H   LINE      CODE     ---       #1147
      01001724H   LINE      CODE     ---       #1148
      01001724H   LINE      CODE     ---       #1149
      01001724H   LINE      CODE     ---       #1150
      01001724H   LINE      CODE     ---       #1151
      01001724H   LINE      CODE     ---       #1154
      01001727H   LINE      CODE     ---       #1155
      01001727H   LINE      CODE     ---       #1156
      01001735H   LINE      CODE     ---       #1157
      01001744H   LINE      CODE     ---       #1158
      01001744H   LINE      CODE     ---       #1159
      01001746H   LINE      CODE     ---       #1160
      01001748H   LINE      CODE     ---       #1162
      01001748H   LINE      CODE     ---       #1163
      0100174AH   LINE      CODE     ---       #1164
      0100174CH   LINE      CODE     ---       #1165
      01001753H   LINE      CODE     ---       #1166
      01001753H   LINE      CODE     ---       #1167
      01001753H   LINE      CODE     ---       #1170
      01001759H   LINE      CODE     ---       #1171
      01001763H   LINE      CODE     ---       #1172
      01001763H   LINE      CODE     ---       #1173
      01001765H   LINE      CODE     ---       #1174
      0100176AH   LINE      CODE     ---       #1175
      0100176AH   LINE      CODE     ---       #1178
      01001778H   LINE      CODE     ---       #1179
      01001787H   LINE      CODE     ---       #1180
      01001787H   LINE      CODE     ---       #1181
      0100178BH   LINE      CODE     ---       #1182
      01001790H   LINE      CODE     ---       #1183
      01001790H   LINE      CODE     ---       #1186
      01001798H   LINE      CODE     ---       #1187
      01001798H   LINE      CODE     ---       #1188
      0100179EH   LINE      CODE     ---       #1189
      010017A8H   LINE      CODE     ---       #1190
      010017A8H   LINE      CODE     ---       #1191
      010017AAH   LINE      CODE     ---       #1192
      010017AFH   LINE      CODE     ---       #1193
      010017AFH   LINE      CODE     ---       #1194
      010017B0H   LINE      CODE     ---       #1196
      010017B0H   LINE      CODE     ---       #1197
      010017B5H   LINE      CODE     ---       #1198
      010017B7H   LINE      CODE     ---       #1199
      010017B7H   LINE      CODE     ---       #1200
      ---         BLOCKEND  ---      ---       LVL=0

      010011DFH   BLOCK     CODE     ---       LVL=0
      010011DFH   LINE      CODE     ---       #1209
      010011DFH   LINE      CODE     ---       #1210
      010011DFH   LINE      CODE     ---       #1212
      010011EBH   LINE      CODE     ---       #1213
      010011EBH   LINE      CODE     ---       #1214
      010011EEH   LINE      CODE     ---       #1215
      010011EEH   LINE      CODE     ---       #1216
      010011F0H   LINE      CODE     ---       #1217
      010011F2H   LINE      CODE     ---       #1218
      010011F4H   LINE      CODE     ---       #1219
      010011F6H   LINE      CODE     ---       #1220
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 110


      010011F8H   LINE      CODE     ---       #1221
      010011FEH   LINE      CODE     ---       #1222
      010011FEH   LINE      CODE     ---       #1223
      010011FEH   LINE      CODE     ---       #1226
      0100121FH   LINE      CODE     ---       #1227
      0100121FH   LINE      CODE     ---       #1229
      01001225H   LINE      CODE     ---       #1230
      01001225H   LINE      CODE     ---       #1231
      0100122EH   LINE      CODE     ---       #1232
      01001234H   LINE      CODE     ---       #1233
      01001236H   LINE      CODE     ---       #1234
      01001238H   LINE      CODE     ---       #1235
      0100123AH   LINE      CODE     ---       #1236
      0100123CH   LINE      CODE     ---       #1237
      01001240H   LINE      CODE     ---       #1238
      01001240H   LINE      CODE     ---       #1239
      01001240H   LINE      CODE     ---       #1242
      01001243H   LINE      CODE     ---       #1243
      01001243H   LINE      CODE     ---       #1244
      01001245H   LINE      CODE     ---       #1246
      01001260H   LINE      CODE     ---       #1247
      01001260H   LINE      CODE     ---       #1249
      01001262H   LINE      CODE     ---       #1250
      01001264H   LINE      CODE     ---       #1251
      01001266H   LINE      CODE     ---       #1254
      0100126FH   LINE      CODE     ---       #1255
      01001275H   LINE      CODE     ---       #1256
      01001277H   LINE      CODE     ---       #1257
      01001279H   LINE      CODE     ---       #1258
      0100127DH   LINE      CODE     ---       #1259
      01001284H   LINE      CODE     ---       #1260
      01001286H   LINE      CODE     ---       #1261
      01001295H   LINE      CODE     ---       #1262
      01001295H   LINE      CODE     ---       #1264
      0100129BH   LINE      CODE     ---       #1265
      0100129DH   LINE      CODE     ---       #1266
      0100129FH   LINE      CODE     ---       #1267
      010012A1H   LINE      CODE     ---       #1268
      010012A3H   LINE      CODE     ---       #1269
      010012A3H   LINE      CODE     ---       #1270
      010012A3H   LINE      CODE     ---       #1273
      010012C4H   LINE      CODE     ---       #1274
      010012C4H   LINE      CODE     ---       #1276
      010012CAH   LINE      CODE     ---       #1277
      010012CAH   LINE      CODE     ---       #1278
      010012D3H   LINE      CODE     ---       #1279
      010012D9H   LINE      CODE     ---       #1280
      010012DBH   LINE      CODE     ---       #1281
      010012DDH   LINE      CODE     ---       #1282
      010012DFH   LINE      CODE     ---       #1283
      010012E1H   LINE      CODE     ---       #1284
      010012E6H   LINE      CODE     ---       #1285
      010012E6H   LINE      CODE     ---       #1286
      010012E6H   LINE      CODE     ---       #1289
      010012E9H   LINE      CODE     ---       #1290
      010012E9H   LINE      CODE     ---       #1291
      010012EBH   LINE      CODE     ---       #1293
      01001306H   LINE      CODE     ---       #1294
      01001306H   LINE      CODE     ---       #1296
      01001308H   LINE      CODE     ---       #1297
      0100130AH   LINE      CODE     ---       #1298
      0100130CH   LINE      CODE     ---       #1301
      01001315H   LINE      CODE     ---       #1302
      0100131BH   LINE      CODE     ---       #1303
      0100131DH   LINE      CODE     ---       #1304
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 111


      0100131FH   LINE      CODE     ---       #1305
      01001324H   LINE      CODE     ---       #1306
      0100132BH   LINE      CODE     ---       #1307
      0100132CH   LINE      CODE     ---       #1308
      0100133BH   LINE      CODE     ---       #1309
      0100133BH   LINE      CODE     ---       #1311
      01001341H   LINE      CODE     ---       #1312
      01001343H   LINE      CODE     ---       #1313
      01001345H   LINE      CODE     ---       #1314
      01001347H   LINE      CODE     ---       #1315
      01001349H   LINE      CODE     ---       #1316
      01001349H   LINE      CODE     ---       #1317
      01001349H   LINE      CODE     ---       #1318
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ?C?FPMUL
      010009CDH   PUBLIC    CODE     ---       ?C?FPMUL

      ---         MODULE    ---      ---       ?C?FPDIV
      01000AD6H   PUBLIC    CODE     ---       ?C?FPDIV

      ---         MODULE    ---      ---       ?C?FCAST
      01000B7DH   PUBLIC    CODE     ---       ?C?FCASTC
      01000B78H   PUBLIC    CODE     ---       ?C?FCASTI
      01000B73H   PUBLIC    CODE     ---       ?C?FCASTL

      ---         MODULE    ---      ---       PRINTF

      ---         MODULE    ---      ---       ?C?FPGETOPN
      01000BB1H   PUBLIC    CODE     ---       ?C?FPGETOPN2
      01000BE6H   PUBLIC    CODE     ---       ?C?FPNANRESULT
      01000BF0H   PUBLIC    CODE     ---       ?C?FPOVERFLOW
      01000BC8H   PUBLIC    CODE     ---       ?C?FPRESULT
      01000BDCH   PUBLIC    CODE     ---       ?C?FPRESULT2
      01000BEDH   PUBLIC    CODE     ---       ?C?FPUNDERFLOW

      ---         MODULE    ---      ---       ?C?FPROUND
      01000BFBH   PUBLIC    CODE     ---       ?C?FPROUND

      ---         MODULE    ---      ---       ?C?FPCONVERT
      01000C38H   PUBLIC    CODE     ---       ?C?FPCONVERT

      ---         MODULE    ---      ---       ?C?FPADD
      01000D44H   PUBLIC    CODE     ---       ?C?FPADD
      01000D40H   PUBLIC    CODE     ---       ?C?FPSUB

      ---         MODULE    ---      ---       ?C?FTNPWR
      01000E65H   PUBLIC    CODE     ---       ?C?FTNPWR

      ---         MODULE    ---      ---       ?C_INIT
      010019C9H   PUBLIC    CODE     ---       ?C_START

      ---         MODULE    ---      ---       ?C?COPY
      01000F75H   PUBLIC    CODE     ---       ?C?COPY

      ---         MODULE    ---      ---       ?C?CLDPTR
      01000F9BH   PUBLIC    CODE     ---       ?C?CLDPTR

      ---         MODULE    ---      ---       ?C?CLDOPTR
      01000FB4H   PUBLIC    CODE     ---       ?C?CLDOPTR

      ---         MODULE    ---      ---       ?C?CSTPTR
      01000FE1H   PUBLIC    CODE     ---       ?C?CSTPTR

      ---         MODULE    ---      ---       ?C?CSTOPTR
LX51 LINKER/LOCATER V4.66.97.0                                                        07/23/2025  14:09:31  PAGE 112


      01000FF3H   PUBLIC    CODE     ---       ?C?CSTOPTR

      ---         MODULE    ---      ---       ?C?UIDIV
      01001015H   PUBLIC    CODE     ---       ?C?UIDIV

      ---         MODULE    ---      ---       ?C?ILDIX
      0100106AH   PUBLIC    CODE     ---       ?C?ILDIX

      ---         MODULE    ---      ---       ?C?ULDIV
      010010BCH   PUBLIC    CODE     ---       ?C?ULDIV

      ---         MODULE    ---      ---       ?C?LNEG
      0100114EH   PUBLIC    CODE     ---       ?C?LNEG

      ---         MODULE    ---      ---       ?C?LSTXDATA
      0100115CH   PUBLIC    CODE     ---       ?C?LSTXDATA

      ---         MODULE    ---      ---       ?C?LSTKXDATA
      01001168H   PUBLIC    CODE     ---       ?C?LSTKXDATA

      ---         MODULE    ---      ---       ?C?PLDIXDATA
      01001199H   PUBLIC    CODE     ---       ?C?PLDIXDATA

      ---         MODULE    ---      ---       ?C?PSTXDATA
      010011B0H   PUBLIC    CODE     ---       ?C?PSTXDATA

      ---         MODULE    ---      ---       ?C?CCASE
      010011B9H   PUBLIC    CODE     ---       ?C?CCASE

Program Size: data=15.2 xdata=162 const=13 code=8786
LX51 RUN COMPLETE.  0 WARNING(S),  0 ERROR(S)
