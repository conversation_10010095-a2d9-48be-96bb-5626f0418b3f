C51 COMPILER V9.60.0.0   DEMO_TIMER                                                        12/22/2024 13:27:36 PAGE 1   


C51 COMPILER V9.60.0.0, COMPILATION OF MODULE DEMO_TIMER
OBJECT MODULE PLACED IN .\Objects\demo_timer.obj
COMPILER INVOKED BY: D:\Program Files\Keil C51\C51\BIN\C51.EXE ..\Libary\StdDriver\src\demo_timer.c OMF2 OPTIMIZE(8,SPEE
                    -D) BROWSE INCDIR(..\Libary\Device\CMS8S6990\Include;..\Libary\StdDriver\inc;..\code;..\Driver) DEBUG PRINT(.\Listings\de
                    -mo_timer.lst) OBJECT(.\Objects\demo_timer.obj)

line level    source

   1          /*******************************************************************************
   2          * Copyright (C) 2019 China Micro Semiconductor Limited Company. All Rights Reserved.
   3          *
   4          * This software is owned and published by:
   5          * CMS LLC, No 2609-10, Taurus Plaza, TaoyuanRoad, NanshanDistrict, Shenzhen, China.
   6          *
   7          * BY DOWNLOADING, INSTALLING OR USING THIS SOFTWARE, YOU AGREE TO BE BOUND
   8          * BY ALL THE TERMS AND CONDITIONS OF THIS AGREEMENT.
   9          *
  10          * This software contains source code for use with CMS
  11          * components. This software is licensed by CMS to be adapted only
  12          * for use in systems utilizing CMS components. CMS shall not be
  13          * responsible for misuse or illegal use of this software for devices not
  14          * supported herein. CMS is providing this software "AS IS" and will
  15          * not be responsible for issues arising from incorrect user implementation
  16          * of the software.
  17          *
  18          * This software may be replicated in part or whole for the licensed use,
  19          * with the restriction that this Disclaimer and Copyright notice must be
  20          * included with each copy of this software, whether used in part or whole,
  21          * at all times.
  22          */
  23          
  24          /****************************************************************************/
  25          /** \file demo_timer.c
  26          **
  27          **  
  28          **
  29          **      History:
  30          **      
  31          *****************************************************************************/
  32          /****************************************************************************/
  33          /*      include files
  34          *****************************************************************************/
  35          #include "demo_timer.h"
  36          
  37          /****************************************************************************/
  38          /*      Local pre-processor symbols('#define')
  39          ****************************************************************************/
  40          
  41          /****************************************************************************/
  42          /*      Global variable definitions(declared in header file with 'extern')
  43          ****************************************************************************/
  44          
  45          /****************************************************************************/
  46          /*      Local type definitions('typedef')
  47          ****************************************************************************/
  48          
  49          /****************************************************************************/
  50          /*      Local variable  definitions('static')
  51          ****************************************************************************/
  52          
  53          /****************************************************************************/
C51 COMPILER V9.60.0.0   DEMO_TIMER                                                        12/22/2024 13:27:36 PAGE 2   

  54          /*      Local function prototypes('static')
  55          ****************************************************************************/
  56          
  57          /****************************************************************************/
  58          /*      Function implementation - global ('extern') and local('static')
  59          ****************************************************************************/
  60          /******************************************************************************
  61          ** \brief        TMR0_Config
  62          ** \param [in] 
  63          **              
  64          ** \return  none
  65          ** \note  
  66          ******************************************************************************/
  67          void TMR0_Config(void)
  68          {
  69   1      
  70   1              /*
  71   1              (1)设置Timer的运行模式
  72   1              */
  73   1              TMR_ConfigRunMode(TMR0, TMR_MODE_TIMING,TMR_TIM_16BIT); 
  74   1              /*
  75   1              (2)设置Timer 运行时钟
  76   1              */
  77   1              TMR_ConfigTimerClk(TMR0, TMR_CLK_DIV_12);                                               /*Fsys = 24Mhz，Ftimer = 2Mhz,Ttmr=0.5us*/
  78   1              /*
  79   1              (3)设置Timer周期
  80   1              */      
  81   1              TMR_ConfigTimerPeriod(TMR0, (65536-2000)>>8, 65536-2000);                               // 2000*0.5us = 1000us,递增计数
  82   1                      
  83   1              /*
  84   1              (4)开启中断
  85   1              */
  86   1              TMR_EnableOverflowInt(TMR0);
  87   1      
  88   1              /*
  89   1              (5)设置Timer中断优先级
  90   1              */      
  91   1              IRQ_SET_PRIORITY(IRQ_TMR0,IRQ_PRIORITY_HIGH);
  92   1              IRQ_ALL_ENABLE();       
  93   1      
  94   1              /*
  95   1              (6)开启Timer
  96   1              */
  97   1              TMR_Start(TMR0);
  98   1      }
  99          
 100          
 101          
 102          
 103          
 104          
 105          
 106          
 107          
 108          
 109          
 110          
 111          
 112          
 113          
 114          
 115          
C51 COMPILER V9.60.0.0   DEMO_TIMER                                                        12/22/2024 13:27:36 PAGE 3   

 116          
 117          
 118          
 119          
 120          
 121          
 122          
 123          
 124          
 125          
 126          


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =     42    ----
   CONSTANT SIZE    =   ----    ----
   XDATA SIZE       =   ----    ----
   PDATA SIZE       =   ----    ----
   DATA SIZE        =   ----    ----
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
   EDATA SIZE       =   ----    ----
   HDATA SIZE       =   ----    ----
   XDATA CONST SIZE =   ----    ----
   FAR CONST SIZE   =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
