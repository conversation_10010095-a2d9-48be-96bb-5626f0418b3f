C51 COMPILER V9.60.7.0   OPA                                                               12/21/2024 16:05:10 PAGE 1   


C51 COMPILER V9.60.7.0, COMPILATION OF MODULE OPA
OBJECT MODULE PLACED IN .\Objects\opa.obj
COMPILER INVOKED BY: C:\Keil_C51\C51\BIN\C51.EXE ..\Libary\StdDriver\src\opa.c OMF2 OPTIMIZE(8,SPEED) BROWSE INCDIR(..\L
                    -ibary\Device\CMS8S6990\Include;..\Libary\StdDriver\inc;..\code) DEBUG PRINT(.\Listings\opa.lst) OBJECT(.\Objects\opa.obj
                    -)

line level    source

   1          /*******************************************************************************
   2          * Copyright (C) 2019 China Micro Semiconductor Limited Company. All Rights Reserved.
   3          *
   4          * This software is owned and published by:
   5          * CMS LLC, No 2609-10, Taurus Plaza, TaoyuanRoad, NanshanDistrict, Shenzhen, China.
   6          *
   7          * BY DOWNLOADING, INSTALLING OR USING THIS SOFTWARE, YOU AGREE TO BE BOUND
   8          * BY ALL THE TERMS AND CONDITIONS OF THIS AGREEMENT.
   9          *
  10          * This software contains source code for use with CMS
  11          * components. This software is licensed by CMS to be adapted only
  12          * for use in systems utilizing CMS components. CMS shall not be
  13          * responsible for misuse or illegal use of this software for devices not
  14          * supported herein. CMS is providing this software "AS IS" and will
  15          * not be responsible for issues arising from incorrect user implementation
  16          * of the software.
  17          *
  18          * This software may be replicated in part or whole for the licensed use,
  19          * with the restriction that this Disclaimer and Copyright notice must be
  20          * included with each copy of this software, whether used in part or whole,
  21          * at all times.
  22          */
  23          
  24          /****************************************************************************/
  25          /** \file opa.c
  26          **
  27          **  
  28          **
  29          **      History:
  30          **      
  31          *****************************************************************************/
  32          /****************************************************************************/
  33          /*      include files
  34          *****************************************************************************/
  35          #include "opa.h"
  36          
  37          /****************************************************************************/
  38          /*      Local pre-processor symbols/macros('#define')
  39          ****************************************************************************/
  40          
  41          /****************************************************************************/
  42          /*      Global variable definitions(declared in header file with 'extern')
  43          ****************************************************************************/
  44          
  45          /****************************************************************************/
  46          /*      Local type definitions('typedef')
  47          ****************************************************************************/
  48          
  49          /****************************************************************************/
  50          /*      Local variable  definitions('static')
  51          ****************************************************************************/
  52          
  53          /****************************************************************************/
C51 COMPILER V9.60.7.0   OPA                                                               12/21/2024 16:05:10 PAGE 2   

  54          /*      Local function prototypes('static')
  55          ****************************************************************************/
  56          
  57          /****************************************************************************/
  58          /*      Function implementation - global ('extern') and local('static')
  59          ****************************************************************************/
  60          
  61          /*****************************************************************************
  62           ** \brief       OPA_SetRunMode
  63           **                      设置运放运行模式
  64           ** \param [in] OPAn : OPA0、OPA1
  65           **                             OpaMode:  (1)OPA_MODE_OPA
  66           **                                               (2)OPA_MODE_ACMP
  67           ** \return  none
  68           ** \note   
  69           *****************************************************************************/
  70          void OPA_SetRunMode(uint8_t OPAn, uint8_t OpaMode)
  71          {
  72   1              uint8_t Temp=0;
  73   1              
  74   1              if(OPA0 == OPAn)
  75   1              {
  76   2                      Temp = OP0CON0;
  77   2                      Temp &= ~(OPA_OP0CON0_OP0FIL_Msk);
  78   2                      Temp |= (OpaMode << OPA_OP0CON0_OP0FIL_Pos);
  79   2                      OP0CON0 = Temp;
  80   2              }
  81   1              if(OPA1 == OPAn)
  82   1              {
  83   2                      Temp = OP1CON0;
  84   2                      Temp &= ~(OPA_OP1CON0_OP1FIL_Msk);
  85   2                      Temp |= (OpaMode << OPA_OP1CON0_OP1FIL_Pos);    
  86   2                      OP1CON0 = Temp; 
  87   2              }
  88   1      }
  89          
  90          /*****************************************************************************
  91           ** \brief       OPA_ConfigPositive
  92           **                      设置运放正端输入
  93           ** \param [in] OPAn : OPA0、OPA1
  94           **                             PositiveSource:  (1)OPA_POSSEL_P        :OPnP
  95           **                                                              (2)OPA_POSSEL_BG
  96           ** \return  none
  97           ** \note    
  98           *****************************************************************************/
  99          void OPA_ConfigPositive(uint8_t OPAn, uint8_t PositiveSource)
 100          {
 101   1              uint8_t Temp=0;
 102   1              
 103   1              if(OPA0 == OPAn)
 104   1              {
 105   2                      Temp = OP0CON0;
 106   2                      Temp &= ~(OPA_OP0CON0_OP0PS_Msk);
 107   2                      Temp |= (PositiveSource << OPA_OP0CON0_OP0PS_Pos);
 108   2                      OP0CON0 = Temp;
 109   2              }
 110   1              if(OPA1 == OPAn)
 111   1              {
 112   2                      Temp = OP1CON0;
 113   2                      Temp &= ~(OPA_OP1CON0_OP1PS_Msk);
 114   2                      Temp |= (PositiveSource << OPA_OP1CON0_OP1PS_Pos);      
 115   2                      OP1CON0 = Temp;
C51 COMPILER V9.60.7.0   OPA                                                               12/21/2024 16:05:10 PAGE 3   

 116   2              }
 117   1      }
 118          
 119          /*****************************************************************************
 120           ** \brief       OPA_ConfigNegative
 121           **                      设置运放器负端输入
 122           ** \param [in] OPAn : OPA0、OPA1
 123           **                             NegativeSource:  (1)OPA_NEGSEL_N                :OPnN                   
 124           ** \return  none
 125           ** \note   
 126           *****************************************************************************/
 127          void OPA_ConfigNegative(uint8_t OPAn, uint8_t NegativeSource)
 128          {
 129   1              uint8_t Temp=0;
 130   1              
 131   1              if(OPA0 == OPAn)
 132   1              {
 133   2                      Temp = OP0CON0;
 134   2                      Temp &= ~(OPA_OP0CON0_OP0NS_Msk);
 135   2                      Temp |= (NegativeSource << OPA_OP0CON0_OP0NS_Pos);
 136   2                      OP0CON0 = Temp;
 137   2              }
 138   1              if(OPA1 == OPAn)
 139   1              {
 140   2                      Temp = OP1CON0;
 141   2                      Temp &= ~(OPA_OP1CON0_OP1NS_Msk);
 142   2                      Temp |= (NegativeSource << OPA_OP1CON0_OP1NS_Pos);
 143   2                      OP1CON0 = Temp;
 144   2              }
 145   1      
 146   1      }
 147          
 148          /*****************************************************************************
 149           ** \brief       OPA_EnableOutput
 150           **                      开启运放器输出端
 151           ** \param [in] OPAn : OPA0、OPA1
 152           ** \return  none
 153           ** \note   
 154           *****************************************************************************/
 155          void OPA_EnableOutput(uint8_t OPAn)
 156          {
 157   1              if(OPA0 == OPAn)
 158   1              {
 159   2                      OP0CON0 |= (OPA_OP0CON0_OP0OS_Msk);
 160   2              }
 161   1              if(OPA1 == OPAn)
 162   1              {
 163   2                      OP1CON0 |= (OPA_OP1CON0_OP1OS_Msk);     
 164   2              }
 165   1      }
 166          
 167          /*****************************************************************************
 168           ** \brief       OPA_DisableOutput
 169           **                      关闭运放器输出端
 170           ** \param [in] OPAn : OPA0、OPA1
 171           ** \return  none
 172           ** \note   
 173           *****************************************************************************/
 174          void OPA_DisableOutput(uint8_t OPAn)
 175          {
 176   1              if(OPA0 == OPAn)
 177   1              {
C51 COMPILER V9.60.7.0   OPA                                                               12/21/2024 16:05:10 PAGE 4   

 178   2                      OP0CON0 &= ~(OPA_OP0CON0_OP0OS_Msk);
 179   2              }
 180   1              if(OPA1 == OPAn)
 181   1              {
 182   2                      OP1CON0 &= ~(OPA_OP1CON0_OP1OS_Msk);
 183   2              }
 184   1      }
 185          
 186          
 187          
 188          /********************************************************************************
 189           ** \brief       OPA_Start
 190           **                      开启OP
 191           ** \param [in] OPAn : OPA0、OPA1
 192           ** \return  none
 193           ** \note   
 194           ******************************************************************************/
 195          void OPA_Start(uint8_t OPAn)
 196          {
 197   1              if(OPA0 == OPAn)
 198   1              {
 199   2                      OP0CON0 |= OPA_OP0CON0_OP0EN_Msk;
 200   2              }
 201   1              if(OPA1 == OPAn)
 202   1              {
 203   2                      OP1CON0 |= OPA_OP1CON0_OP1EN_Msk;       
 204   2              }
 205   1      }
 206          
 207          /********************************************************************************
 208           ** \brief       OPA_Stop
 209           **                      关闭OPA
 210           ** \param [in] OPAn : OPA0、OPA1 
 211           ** \return  none
 212           ** \note   
 213           ******************************************************************************/
 214          void OPA_Stop(uint8_t OPAn)
 215          {
 216   1              if(OPA0 == OPAn)
 217   1              {
 218   2                      OP0CON0 &= ~OPA_OP0CON0_OP0EN_Msk;
 219   2              }
 220   1              if(OPA1 == OPAn)
 221   1              {
 222   2                      OP1CON0 &= ~OPA_OP1CON0_OP1EN_Msk;      
 223   2              }       
 224   1      }
 225          
 226          /********************************************************************************
 227           ** \brief       OPA_GetResult
 228           **                      获取结果
 229           ** \param [in] OPAn : OPA0、OPA1
 230           ** \return  0/1
 231           ** \note   
 232           ******************************************************************************/
 233          uint8_t OPA_GetResult(uint8_t OPAn)
 234          {
 235   1              if(OPA0 == OPAn)
 236   1              {
 237   2                      return ((OP0CON1 & OPA_OP0CON1_OP0DOUT_Msk)? 1:0);
 238   2              }
 239   1              if(OPA1 == OPAn)
C51 COMPILER V9.60.7.0   OPA                                                               12/21/2024 16:05:10 PAGE 5   

 240   1              {
 241   2                      return ((OP1CON1 & OPA_OP1CON1_OP1DOUT_Msk)? 1:0);
 242   2              }       
 243   1              return 0;
 244   1      }
 245          
 246          
 247          /*****************************************************************************
 248           ** \brief       OPA_ConfigOffsetAdj
 249           **                      配置OPA失调电压调节方式
 250           ** \param [in] OPAn : OPA0、OPA1              
 251           **                             OffsetAdj:      (1)OPA_OFFSET_CONFIG
 252           **                                                     (2)OPA_OFFSET_OPADJ
 253           **                             AdjVlue : 失调电压调节值：0x00~0x1f
 254           ** \return  none
 255           ** \note   
 256          *****************************************************************************/
 257          void OPA_ConfigOffsetAdj(uint8_t OPAn,uint8_t OffsetAdj, uint8_t AdjVlue)
 258          {
 259   1              uint8_t Temp=0;
 260   1              
 261   1              if(OPA0 == OPAn)
 262   1              {
 263   2                      Temp = OP0CON1;
 264   2                      Temp &= ~(OPA_OP0CON1_OP0ADJ_Msk);
 265   2                      Temp |= 0x1f & AdjVlue;
 266   2                      OP0CON1 = Temp;
 267   2                      OP0ADJE = OffsetAdj;
 268   2              }
 269   1              if(OPA1 == OPAn)
 270   1              {
 271   2                      Temp = OP1CON1;
 272   2                      Temp &= ~(OPA_OP1CON1_OP1ADJ_Msk);
 273   2                      Temp |= 0x1f & AdjVlue;
 274   2                      OP1CON1 = Temp;
 275   2                      OP1ADJE = OffsetAdj;
 276   2              }               
 277   1      }
 278          /*****************************************************************************
 279           ** \brief       OPA_Delay
 280           **                      延时函数
 281           ** \param [in]none
 282           ** \return  none
 283           ** \note   
 284          *****************************************************************************/
 285          static void OPA_Delay(void)
 286          {
 287   1              volatile uint8_t i;
 288   1              for(i=0;i<50;i++)
 289   1                      _nop_();
 290   1      }
 291          /*****************************************************************************
 292           ** \brief       OPA_GetOffsetAdjValue
 293           **                      获取OPA失调电压调节值
 294           ** \param [in] OPAn : OPA0、OPA1
 295           **                             OpaMode:  (1)OPA_MODE_OPA
 296           **                                               (2)OPA_MODE_ACMP      
 297           ** \return  5位失调电压修调值
 298           ** \note   
 299           *****************************************************************************/
 300          uint8_t  OPA_GetOffsetAdjValue(uint8_t OPAn, uint8_t OpaMode)
 301          {
C51 COMPILER V9.60.7.0   OPA                                                               12/21/2024 16:05:10 PAGE 6   

 302   1              uint8_t temp, OPAOut, Adjvalue;
 303   1              Adjvalue =0;    
 304   1              if(OPA0 == OPAn)
 305   1              {
 306   2                      /*(1)关闭OPA0*/               
 307   2                      OPA_Stop(OPA0);
 308   2                      OPA_SetRunMode(OPA0,OpaMode);
 309   2                      /*(2)设置修调模式下的信号输入端*/
 310   2                      OP0CON1 = 0x00; /*选择OP0N端口为信号输入端*/  
 311   2                      P31CFG = 0x01;  /*设置P31为OPA0_N*/
 312   2                      
 313   2                      OP0CON0 &= ~(0x3<<2);
 314   2                      /*（3）开启OPA0调节模式*/
 315   2                      OP0CON0 |= (1<<6);      
 316   2                      /*(4)设置OPA的调节值源为OPA0ADJ<4:0>*/
 317   2                      OP0ADJE = 0xAA;
 318   2                      /*(5)开启OPA模块*/
 319   2                      OPA_Start(OPA0);
 320   2                      /*(6)获取调节值*/
 321   2                      OPA_Delay();
 322   2                      OPAOut = OPA_GetResult(OPA0);
 323   2                      for(temp=0; temp<0x20; temp++)
 324   2                      {
 325   3                              OP0CON1 = temp;
 326   3                              OPA_Delay();
 327   3                              if(OPAOut != OPA_GetResult(OPA0))
 328   3                              {
 329   4                                      Adjvalue = temp;
 330   4                                      break;
 331   4                              }
 332   3                      }
 333   2                      /*(7)关闭OPA修调模式*/
 334   2                      OP0CON0 &= ~(1<<6);
 335   2                      OPA_Stop(OPA0);
 336   2                      return  Adjvalue;
 337   2              }       
 338   1      
 339   1              if(OPA1 == OPAn)
 340   1              {
 341   2                      /*(1)关闭OPA0*/               
 342   2                      OPA_Stop(OPA1);
 343   2                      OPA_SetRunMode(OPA1,OpaMode);
 344   2                      /*(2)设置修调模式下的信号输入端*/
 345   2                      OP1CON1 = 0x00; /*选择OPA1N端口为信号输入端*/                 
 346   2                      P23CFG = 0x01;/*设置P23为OPA1_N*/
 347   2                      OP1CON0 &= ~(0x3<<2);
 348   2                      /*（3）开启OPA1调节模式*/
 349   2                      OP1CON0 |= (1<<6);      
 350   2                      /*(4)设置OPA的调节值源为OPA1ADJ<4:0>*/
 351   2                      OP1ADJE = 0xAA;
 352   2                      /*(5)开启OPA模块*/
 353   2                      OPA_Start(OPA1);
 354   2                      /*(6)获取调节值*/
 355   2                      OPA_Delay();
 356   2                      OPAOut = OPA_GetResult(OPA1);
 357   2                      for(temp=0; temp<0x20; temp++)
 358   2                      {
 359   3                              OP1CON1 = temp;
 360   3                              OPA_Delay();
 361   3                              if(OPAOut != OPA_GetResult(OPA1))
 362   3                              {
 363   4                                      Adjvalue = temp;
C51 COMPILER V9.60.7.0   OPA                                                               12/21/2024 16:05:10 PAGE 7   

 364   4                                      break;
 365   4                              }
 366   3                      }
 367   2                      /*(7)关闭OPA修调模式*/
 368   2                      OP1CON0 &= ~(1<<6);
 369   2                      OPA_Stop(OPA1);
 370   2                      
 371   2                      return  Adjvalue;
 372   2              }
 373   1              return 0;       
 374   1      }


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =    489    ----
   CONSTANT SIZE    =   ----    ----
   XDATA SIZE       =   ----    ----
   PDATA SIZE       =   ----    ----
   DATA SIZE        =   ----       2
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
   EDATA SIZE       =   ----    ----
   HDATA SIZE       =   ----    ----
   XDATA CONST SIZE =   ----    ----
   FAR CONST SIZE   =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
