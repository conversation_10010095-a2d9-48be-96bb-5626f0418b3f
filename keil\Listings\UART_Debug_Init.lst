C51 COMPILER V9.60.0.0   UART_DEBUG_INIT                                                   12/22/2024 13:27:37 PAGE 1   


C51 COMPILER V9.60.0.0, COMPILATION OF MODULE UART_DEBUG_INIT
OBJECT MODULE PLACED IN .\Objects\UART_Debug_Init.obj
COMPILER INVOKED BY: D:\Program Files\Keil C51\C51\BIN\C51.EXE ..\Driver\UART_Debug_Init.c OMF2 OPTIMIZE(8,SPEED) BROWSE
                    - INCDIR(..\Libary\Device\CMS8S6990\Include;..\Libary\StdDriver\inc;..\code;..\Driver) DEBUG PRINT(.\Listings\UART_Debug_
                    -Init.lst) OBJECT(.\Objects\UART_Debug_Init.obj)

line level    source

*** ERROR C318 OF ..\Driver\UART_Debug_Init.c: can't open file '..\Driver\UART_Debug_Init.c'

C51 COMPILATION COMPLETE.  0 WARNING(S),  1 ERROR(S)
