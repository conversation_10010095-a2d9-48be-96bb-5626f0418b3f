#include "ADC_Used.h"
#include "define.h"
#include <stdio.h>

#define FILTER_SIZE 5  

uint16_t ADC(uint8_t ADC_Channel) 
{

    static uint16_t filter_buffer[FILTER_SIZE] = {0};
    static uint8_t filter_index = 0;
    uint32_t sum = 0;
    uint16_t adc_result;
    uint8_t i;
    float v_adc, v_bat;

    // ??????14
    ADC_Channel = ADC_CH_14;
    ADC_EnableChannel(ADC_Channel);

    ADC_GO();
    while (ADC_IS_BUSY);
    adc_result = ADC_GetADCResult();

    // ?????????
    filter_buffer[filter_index] = adc_result;
    filter_index = (filter_index + 1) % FILTER_SIZE;
    for (i = 0; i < FILTER_SIZE; i++) 
    {
        sum += filter_buffer[i];
    }
    adc_result = sum / FILTER_SIZE;

    // ?????????
    v_adc = adc_result * 2.4f / 4095.0f;
    v_bat = v_adc * (20.7f + 10.5f) / 10.5f;

    printf("ADC_CH14: %d (Vbat=%.2fV)\r\n", adc_result, v_bat);
    return adc_result;
}

// ??????ADC???????????
void ADC_StartConvert(uint8_t ADC_Channel)
{
    ADC_Channel = ADC_CH_14;  // ?????????14
    ADC_EnableChannel(ADC_Channel);
    ADC_GO();  // ???????
}

uint8_t ADC_GetConvertIntFlag(void)
{
    return !ADC_IS_BUSY;  // ???????????1
}

void ADC_ClearConvertIntFlag(void)
{
    // ADC????????????????????????????????
}

uint16_t ADC_GetResult(void)
{
    return ADC_GetADCResult();
}