C51 COMPILER V9.60.0.0   DEMO_GPIO                                                         12/22/2024 13:27:36 PAGE 1   


C51 COMPILER V9.60.0.0, COMPILATION OF MODULE DEMO_GPIO
OBJECT MODULE PLACED IN .\Objects\demo_gpio.obj
COMPILER INVOKED BY: D:\Program Files\Keil C51\C51\BIN\C51.EXE ..\Libary\StdDriver\src\demo_gpio.c OMF2 OPTIMIZE(8,SPEED
                    -) BROWSE INCDIR(..\Libary\Device\CMS8S6990\Include;..\Libary\StdDriver\inc;..\code;..\Driver) DEBUG PRINT(.\Listings\dem
                    -o_gpio.lst) OBJECT(.\Objects\demo_gpio.obj)

line level    source

   1          /*******************************************************************************
   2          * Copyright (C) 2019 China Micro Semiconductor Limited Company. All Rights Reserved.
   3          *
   4          * This software is owned and published by:
   5          * CMS LLC, No 2609-10, Taurus Plaza, TaoyuanRoad, NanshanDistrict, Shenzhen, China.
   6          *
   7          * BY DOWNLOADING, INSTALLING OR USING THIS SOFTWARE, YOU AGREE TO BE BOUND
   8          * BY ALL THE TERMS AND CONDITIONS OF THIS AGREEMENT.
   9          *
  10          * This software contains source code for use with CMS
  11          * components. This software is licensed by CMS to be adapted only
  12          * for use in systems utilizing CMS components. CMS shall not be
  13          * responsible for misuse or illegal use of this software for devices not
  14          * supported herein. CMS is providing this software "AS IS" and will
  15          * not be responsible for issues arising from incorrect user implementation
  16          * of the software.
  17          *
  18          * This software may be replicated in part or whole for the licensed use,
  19          * with the restriction that this Disclaimer and Copyright notice must be
  20          * included with each copy of this software, whether used in part or whole,
  21          * at all times.
  22          */
  23          
  24          /****************************************************************************/
  25          /** \file demo_gpio.c
  26          **
  27          **  
  28          **
  29          **      History:
  30          **      
  31          *****************************************************************************/
  32          /****************************************************************************/
  33          /*      include files
  34          *****************************************************************************/
  35          #include "demo_gpio.h"
  36          
  37          /****************************************************************************/
  38          /*      Local pre-processor symbols('#define')
  39          ****************************************************************************/
  40          
  41          /****************************************************************************/
  42          /*      Global variable definitions(declared in header file with 'extern')
  43          ****************************************************************************/
  44          
  45          /****************************************************************************/
  46          /*      Local type definitions('typedef')
  47          ****************************************************************************/
  48          
  49          /****************************************************************************/
  50          /*      Local variable  definitions('static')
  51          ****************************************************************************/
  52          
  53          /****************************************************************************/
C51 COMPILER V9.60.0.0   DEMO_GPIO                                                         12/22/2024 13:27:36 PAGE 2   

  54          /*      Local function prototypes('static')
  55          ****************************************************************************/
  56          
  57          /****************************************************************************/
  58          /*      Function implementation - global ('extern') and local('static')
  59          ****************************************************************************/
  60          
  61          /******************************************************************************
  62           ** \brief       GPIO_Config
  63           ** \param [in] none
  64           **          GPIO中断功能
  65           ** \return  none
  66           ** \note  
  67           ******************************************************************************/
  68          void GPIO_Config(void)
  69          {
  70   1              /*
  71   1              (1)设置P23 IO功能
  72   1              */
  73   1      ////    GPIO_SET_MUX_MODE(P23CFG, GPIO_MUX_GPIO);               //设置P23为GPIO模式
  74   1      ////    GPIO_ENABLE_INPUT(P2TRIS, GPIO_PIN_3);                  //设置为输入模式
  75   1      ////    GPIO_ENABLE_RD(P2RD, GPIO_PIN_3);                               //开启下拉
  76   1              
  77   1              //A_Phase
  78   1              GPIO_SET_MUX_MODE(P24CFG, GPIO_MUX_GPIO);
  79   1              GPIO_ENABLE_OUTPUT(P2TRIS, GPIO_PIN_4);
  80   1              A_Phase = Off;
*** ERROR C202 IN LINE 80 OF ..\Libary\StdDriver\src\demo_gpio.c: 'A_Phase': undefined identifier
  81   1              
  82   1              //B_Phase
  83   1              GPIO_SET_MUX_MODE(P25CFG, GPIO_MUX_GPIO);
  84   1              GPIO_ENABLE_OUTPUT(P2TRIS, GPIO_PIN_5);
  85   1              B_Phase = Off;
*** ERROR C202 IN LINE 85 OF ..\Libary\StdDriver\src\demo_gpio.c: 'B_Phase': undefined identifier
  86   1      
  87   1              //C_Phase
  88   1              GPIO_SET_MUX_MODE(P26CFG, GPIO_MUX_GPIO);
  89   1              GPIO_ENABLE_OUTPUT(P2TRIS, GPIO_PIN_6);
  90   1              C_Phase = Off;
*** ERROR C202 IN LINE 90 OF ..\Libary\StdDriver\src\demo_gpio.c: 'C_Phase': undefined identifier
  91   1              
  92   1              //D_Phase
  93   1              GPIO_SET_MUX_MODE(P30CFG, GPIO_MUX_GPIO);
  94   1              GPIO_ENABLE_OUTPUT(P3TRIS, GPIO_PIN_0);
  95   1              D_Phase = Off;
*** ERROR C202 IN LINE 95 OF ..\Libary\StdDriver\src\demo_gpio.c: 'D_Phase': undefined identifier
  96   1              
  97   1              //BAT_EN
  98   1              GPIO_SET_MUX_MODE(P31CFG, GPIO_MUX_GPIO);
  99   1              GPIO_ENABLE_OUTPUT(P3TRIS, GPIO_PIN_1);
 100   1              BAT_EN = On;
*** ERROR C202 IN LINE 100 OF ..\Libary\StdDriver\src\demo_gpio.c: 'BAT_EN': undefined identifier
 101   1              
 102   1              //LEDG
 103   1              GPIO_SET_MUX_MODE(P23CFG, GPIO_MUX_GPIO);
 104   1              GPIO_ENABLE_OUTPUT(P2TRIS, GPIO_PIN_3);
 105   1              LEDG = Off;
*** ERROR C202 IN LINE 105 OF ..\Libary\StdDriver\src\demo_gpio.c: 'LEDG': undefined identifier
 106   1              
 107   1              //LEDR
 108   1              GPIO_SET_MUX_MODE(P22CFG, GPIO_MUX_GPIO);
 109   1              GPIO_ENABLE_OUTPUT(P2TRIS, GPIO_PIN_2);
C51 COMPILER V9.60.0.0   DEMO_GPIO                                                         12/22/2024 13:27:36 PAGE 3   

 110   1              LEDR = Off;
*** ERROR C202 IN LINE 110 OF ..\Libary\StdDriver\src\demo_gpio.c: 'LEDR': undefined identifier
 111   1              
 112   1              
 113   1              //K3 K0 K1
 114   1              
 115   1              GPIO_SET_MUX_MODE(P21CFG, GPIO_MUX_GPIO);               // 
 116   1              GPIO_ENABLE_INPUT(P2TRIS, GPIO_PIN_1);                  // 
 117   1              GPIO_ENABLE_UP(P2UP, GPIO_PIN_1);                               // 
 118   1              
 119   1              
 120   1              GPIO_SET_MUX_MODE(P17CFG, GPIO_MUX_GPIO);               //设置P017为GPIO模式
 121   1              GPIO_ENABLE_INPUT(P1TRIS, GPIO_PIN_7);                  //设置为输入模式
 122   1              GPIO_ENABLE_UP(P1UP, GPIO_PIN_7);                               //开启上拉
 123   1      
 124   1              //  
 125   1              GPIO_SET_MUX_MODE(P16CFG, GPIO_MUX_GPIO);               //设置P16为GPIO模式
 126   1              GPIO_ENABLE_INPUT(P1TRIS, GPIO_PIN_6);                  //设置为输入模式
 127   1              GPIO_ENABLE_UP(P1UP, GPIO_PIN_6);                               //开启上拉
 128   1      
 129   1      //CHG_FULL
 130   1              GPIO_SET_MUX_MODE(P15CFG, GPIO_MUX_GPIO);               //设置P15为GPIO模式
 131   1              GPIO_ENABLE_INPUT(P1TRIS, GPIO_PIN_5);                  //设置为输入模式
 132   1              GPIO_ENABLE_UP(P1UP, GPIO_PIN_5);                               //开启上拉
 133   1      
 134   1      //CHG_CHARGE
 135   1              GPIO_SET_MUX_MODE(P14CFG, GPIO_MUX_GPIO);               //设置P14为GPIO模式
 136   1              GPIO_ENABLE_INPUT(P1TRIS, GPIO_PIN_4);                  //设置为输入模式
 137   1              GPIO_ENABLE_UP(P1UP, GPIO_PIN_4);                               //开启上拉
 138   1      
 139   1      
 140   1      
 141   1              
 142   1              /*
 143   1              (2)设置中断方式
 144   1              */
 145   1      ////    GPIO_SET_INT_MODE(P23EICFG, GPIO_INT_FALLING);  //设置为下降沿中断模式
 146   1      ////    GPIO_EnableInt(GPIO2, GPIO_PIN_3_MSK);                  //开启P23中断
 147   1              
 148   1              
 149   1              /*
 150   1              (3)设置中断优先级
 151   1              */
 152   1      ////    IRQ_SET_PRIORITY(IRQ_P2, IRQ_PRIORITY_LOW);
 153   1              /*
 154   1              (4)开启总中断
 155   1              */      
 156   1              IRQ_ALL_ENABLE();
 157   1      
 158   1              
 159   1      }
 160          
 161          
 162          
 163          
 164          
 165          
 166          
 167          
 168          
 169          
 170          
C51 COMPILER V9.60.0.0   DEMO_GPIO                                                         12/22/2024 13:27:36 PAGE 4   

 171          
 172          
 173          
 174          
 175          
 176          
 177          
 178          
 179          
 180          
 181          
 182          
 183          

C51 COMPILATION COMPLETE.  0 WARNING(S),  7 ERROR(S)
