#ifndef __UART_FUNCTION_H__
#define __UART_FUNCTION_H__

#include "define.h"

#define Data_Length_Max		32

#define Data_Error		0x00
#define Data_Pass		0x01
#define Re_Pair			0x02


#define Turn_Off		0xC0

#define Speed_Slw		0xC1
#define Speed_Mid		0xC2
#define Speed_Fst		0xC3

#define Clockwise		0xCA
#define Cclockwise		0xCF

extern unsigned char UART_Get_String[Data_Length_Max] ;

void UART_Data_Copy(unsigned char * Data_Point , unsigned char Source_Data) ;

unsigned char Return_UART_Data_Length(void) ;

void Clean_UART_Data_Length(void) ;

void UART_Data_Init(void) ;


uint16_t Function_Strcat_Plus_Assign(uint8_t *A, uint16_t A_Start, uint8_t *B, uint16_t B_start, uint16_t B_End) ;

void Function_UART_Send_CMD(uint8_t CMD_No) ;


uint8_t UART_Data_Process(void) ;



#endif



